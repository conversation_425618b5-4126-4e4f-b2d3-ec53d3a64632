/**
 * @file freq_sweep_test.h
 * @brief 频率扫描测试和验证程序头文件
 * <AUTHOR>
 * @date 2025.7.30
 */

#ifndef __FREQ_SWEEP_TEST_H__
#define __FREQ_SWEEP_TEST_H__

#include "main.h"
#include <math.h>

// 测试函数声明
void FreqSweepTest_Config(void);
void FreqSweepTest_FreqCalculation(void);
void FreqSweepTest_StateManagement(void);
void FreqSweepTest_BoundaryConditions(void);
void FreqSweepTest_RunAll(void);
void FreqSweepTest_ShowStatus(void);
void FreqSweepTest_SimulateMeasurement(void);

// 测试宏定义
#define TEST_ENABLE_CONFIG          1
#define TEST_ENABLE_CALCULATION     1
#define TEST_ENABLE_STATE           1
#define TEST_ENABLE_BOUNDARY        1
#define TEST_ENABLE_SIMULATION      1

// 如果需要在main函数中调用测试，可以使用以下宏
#define RUN_FREQ_SWEEP_TESTS() do { \
    FreqSweepTest_RunAll(); \
} while(0)

#endif /* __FREQ_SWEEP_TEST_H__ */
