Component: ARM Compiler 5.06 update 5 (build 528) Tool: armlink [4d35e2]

==============================================================================

Section Cross References

    startup_stm32f407xx.o(RESET) refers to startup_stm32f407xx.o(STACK) for __initial_sp
    startup_stm32f407xx.o(RESET) refers to startup_stm32f407xx.o(.text) for Reset_Handler
    startup_stm32f407xx.o(RESET) refers to stm32f4xx_it.o(i.NMI_Handler) for NMI_Handler
    startup_stm32f407xx.o(RESET) refers to stm32f4xx_it.o(i.HardFault_Handler) for HardFault_Handler
    startup_stm32f407xx.o(RESET) refers to stm32f4xx_it.o(i.MemManage_Handler) for MemManage_Handler
    startup_stm32f407xx.o(RESET) refers to stm32f4xx_it.o(i.BusFault_Handler) for BusFault_Handler
    startup_stm32f407xx.o(RESET) refers to stm32f4xx_it.o(i.UsageFault_Handler) for UsageFault_Handler
    startup_stm32f407xx.o(RESET) refers to stm32f4xx_it.o(i.SVC_Handler) for SVC_Handler
    startup_stm32f407xx.o(RESET) refers to stm32f4xx_it.o(i.DebugMon_Handler) for DebugMon_Handler
    startup_stm32f407xx.o(RESET) refers to stm32f4xx_it.o(i.PendSV_Handler) for PendSV_Handler
    startup_stm32f407xx.o(RESET) refers to stm32f4xx_it.o(i.SysTick_Handler) for SysTick_Handler
    startup_stm32f407xx.o(RESET) refers to stm32f4xx_it.o(i.DMA2_Stream0_IRQHandler) for DMA2_Stream0_IRQHandler
    startup_stm32f407xx.o(.text) refers to system_stm32f4xx.o(i.SystemInit) for SystemInit
    startup_stm32f407xx.o(.text) refers to entry.o(.ARM.Collect$$$$00000000) for __main
    main.o(i.HAL_ADC_ConvCpltCallback) refers to main.o(.data) for .data
    main.o(i.SystemClock_Config) refers to memseta.o(.text) for __aeabi_memclr4
    main.o(i.SystemClock_Config) refers to stm32f4xx_hal_rcc.o(i.HAL_RCC_OscConfig) for HAL_RCC_OscConfig
    main.o(i.SystemClock_Config) refers to stm32f4xx_hal_rcc.o(i.HAL_RCC_ClockConfig) for HAL_RCC_ClockConfig
    main.o(i.main) refers to stm32f4xx_hal.o(i.HAL_Init) for HAL_Init
    main.o(i.main) refers to main.o(i.SystemClock_Config) for SystemClock_Config
    main.o(i.main) refers to gpio.o(i.MX_GPIO_Init) for MX_GPIO_Init
    main.o(i.main) refers to dma.o(i.MX_DMA_Init) for MX_DMA_Init
    main.o(i.main) refers to adc.o(i.MX_ADC1_Init) for MX_ADC1_Init
    main.o(i.main) refers to tim.o(i.MX_TIM3_Init) for MX_TIM3_Init
    main.o(i.main) refers to usart.o(i.MX_USART1_UART_Init) for MX_USART1_UART_Init
    main.o(i.main) refers to tim.o(i.MX_TIM4_Init) for MX_TIM4_Init
    main.o(i.main) refers to ad9959_new.o(i.Init_AD9959) for Init_AD9959
    main.o(i.main) refers to ad9959_new.o(i.WriteAmplitude) for WriteAmplitude
    main.o(i.main) refers to ad9959_new.o(i.WriteFreq) for WriteFreq
    main.o(i.main) refers to stm32f4xx_hal_tim.o(i.HAL_TIM_Base_Start) for HAL_TIM_Base_Start
    main.o(i.main) refers to stm32f4xx_hal_adc.o(i.HAL_ADC_Start_DMA) for HAL_ADC_Start_DMA
    main.o(i.main) refers to printfa.o(i.__0printf) for __2printf
    main.o(i.main) refers to fft_processing.o(i.FFT_InitWindow) for FFT_InitWindow
    main.o(i.main) refers to fft_processing.o(i.FFT_Process) for FFT_Process
    main.o(i.main) refers to fft_processing.o(i.FFT_DetectFundamentalFrequency) for FFT_DetectFundamentalFrequency
    main.o(i.main) refers to f2d.o(.text) for __aeabi_f2d
    main.o(i.main) refers to fft_processing.o(i.FFT_SmoothFrequency) for FFT_SmoothFrequency
    main.o(i.main) refers to fft_processing.o(i.FFT_DetectHarmonic) for FFT_DetectHarmonic
    main.o(i.main) refers to tim.o(.bss) for htim3
    main.o(i.main) refers to main.o(.bss) for .bss
    main.o(i.main) refers to adc.o(.bss) for hadc1
    main.o(i.main) refers to main.o(.data) for .data
    adc.o(i.HAL_ADC_MspDeInit) refers to stm32f4xx_hal_gpio.o(i.HAL_GPIO_DeInit) for HAL_GPIO_DeInit
    adc.o(i.HAL_ADC_MspDeInit) refers to stm32f4xx_hal_dma.o(i.HAL_DMA_DeInit) for HAL_DMA_DeInit
    adc.o(i.HAL_ADC_MspInit) refers to memseta.o(.text) for __aeabi_memclr4
    adc.o(i.HAL_ADC_MspInit) refers to stm32f4xx_hal_gpio.o(i.HAL_GPIO_Init) for HAL_GPIO_Init
    adc.o(i.HAL_ADC_MspInit) refers to stm32f4xx_hal_dma.o(i.HAL_DMA_Init) for HAL_DMA_Init
    adc.o(i.HAL_ADC_MspInit) refers to main.o(i.Error_Handler) for Error_Handler
    adc.o(i.HAL_ADC_MspInit) refers to adc.o(.bss) for .bss
    adc.o(i.MX_ADC1_Init) refers to stm32f4xx_hal_adc.o(i.HAL_ADC_Init) for HAL_ADC_Init
    adc.o(i.MX_ADC1_Init) refers to main.o(i.Error_Handler) for Error_Handler
    adc.o(i.MX_ADC1_Init) refers to stm32f4xx_hal_adc.o(i.HAL_ADC_ConfigChannel) for HAL_ADC_ConfigChannel
    adc.o(i.MX_ADC1_Init) refers to adc.o(.bss) for .bss
    dma.o(i.MX_DMA_Init) refers to stm32f4xx_hal_cortex.o(i.HAL_NVIC_SetPriority) for HAL_NVIC_SetPriority
    dma.o(i.MX_DMA_Init) refers to stm32f4xx_hal_cortex.o(i.HAL_NVIC_EnableIRQ) for HAL_NVIC_EnableIRQ
    tim.o(i.MX_TIM3_Init) refers to stm32f4xx_hal_tim.o(i.HAL_TIM_Base_Init) for HAL_TIM_Base_Init
    tim.o(i.MX_TIM3_Init) refers to main.o(i.Error_Handler) for Error_Handler
    tim.o(i.MX_TIM3_Init) refers to stm32f4xx_hal_tim.o(i.HAL_TIM_ConfigClockSource) for HAL_TIM_ConfigClockSource
    tim.o(i.MX_TIM3_Init) refers to stm32f4xx_hal_tim_ex.o(i.HAL_TIMEx_MasterConfigSynchronization) for HAL_TIMEx_MasterConfigSynchronization
    tim.o(i.MX_TIM3_Init) refers to tim.o(.bss) for .bss
    tim.o(i.MX_TIM4_Init) refers to stm32f4xx_hal_tim.o(i.HAL_TIM_Base_Init) for HAL_TIM_Base_Init
    tim.o(i.MX_TIM4_Init) refers to main.o(i.Error_Handler) for Error_Handler
    tim.o(i.MX_TIM4_Init) refers to stm32f4xx_hal_tim.o(i.HAL_TIM_ConfigClockSource) for HAL_TIM_ConfigClockSource
    tim.o(i.MX_TIM4_Init) refers to stm32f4xx_hal_tim_ex.o(i.HAL_TIMEx_MasterConfigSynchronization) for HAL_TIMEx_MasterConfigSynchronization
    tim.o(i.MX_TIM4_Init) refers to tim.o(.bss) for .bss
    usart.o(i.HAL_UART_MspDeInit) refers to stm32f4xx_hal_gpio.o(i.HAL_GPIO_DeInit) for HAL_GPIO_DeInit
    usart.o(i.HAL_UART_MspInit) refers to memseta.o(.text) for __aeabi_memclr4
    usart.o(i.HAL_UART_MspInit) refers to stm32f4xx_hal_gpio.o(i.HAL_GPIO_Init) for HAL_GPIO_Init
    usart.o(i.MX_USART1_UART_Init) refers to stm32f4xx_hal_uart.o(i.HAL_UART_Init) for HAL_UART_Init
    usart.o(i.MX_USART1_UART_Init) refers to main.o(i.Error_Handler) for Error_Handler
    usart.o(i.MX_USART1_UART_Init) refers to usart.o(.bss) for .bss
    usart.o(i.fgetc) refers to stm32f4xx_hal_uart.o(i.HAL_UART_Receive) for HAL_UART_Receive
    usart.o(i.fgetc) refers to usart.o(.bss) for .bss
    usart.o(i.fputc) refers to stm32f4xx_hal_uart.o(i.HAL_UART_Transmit) for HAL_UART_Transmit
    usart.o(i.fputc) refers to usart.o(.bss) for .bss
    stm32f4xx_it.o(i.DMA2_Stream0_IRQHandler) refers to stm32f4xx_hal_dma.o(i.HAL_DMA_IRQHandler) for HAL_DMA_IRQHandler
    stm32f4xx_it.o(i.DMA2_Stream0_IRQHandler) refers to adc.o(.bss) for hdma_adc1
    stm32f4xx_it.o(i.SysTick_Handler) refers to stm32f4xx_hal.o(i.HAL_IncTick) for HAL_IncTick
    stm32f4xx_hal_adc.o(i.ADC_DMAConvCplt) refers to stm32f4xx_hal_adc.o(i.HAL_ADC_ErrorCallback) for HAL_ADC_ErrorCallback
    stm32f4xx_hal_adc.o(i.ADC_DMAConvCplt) refers to main.o(i.HAL_ADC_ConvCpltCallback) for HAL_ADC_ConvCpltCallback
    stm32f4xx_hal_adc.o(i.ADC_DMAError) refers to stm32f4xx_hal_adc.o(i.HAL_ADC_ErrorCallback) for HAL_ADC_ErrorCallback
    stm32f4xx_hal_adc.o(i.ADC_DMAHalfConvCplt) refers to stm32f4xx_hal_adc.o(i.HAL_ADC_ConvHalfCpltCallback) for HAL_ADC_ConvHalfCpltCallback
    stm32f4xx_hal_adc.o(i.HAL_ADC_ConfigChannel) refers to system_stm32f4xx.o(.data) for SystemCoreClock
    stm32f4xx_hal_adc.o(i.HAL_ADC_DeInit) refers to adc.o(i.HAL_ADC_MspDeInit) for HAL_ADC_MspDeInit
    stm32f4xx_hal_adc.o(i.HAL_ADC_IRQHandler) refers to main.o(i.HAL_ADC_ConvCpltCallback) for HAL_ADC_ConvCpltCallback
    stm32f4xx_hal_adc.o(i.HAL_ADC_IRQHandler) refers to stm32f4xx_hal_adc_ex.o(i.HAL_ADCEx_InjectedConvCpltCallback) for HAL_ADCEx_InjectedConvCpltCallback
    stm32f4xx_hal_adc.o(i.HAL_ADC_IRQHandler) refers to stm32f4xx_hal_adc.o(i.HAL_ADC_LevelOutOfWindowCallback) for HAL_ADC_LevelOutOfWindowCallback
    stm32f4xx_hal_adc.o(i.HAL_ADC_IRQHandler) refers to stm32f4xx_hal_adc.o(i.HAL_ADC_ErrorCallback) for HAL_ADC_ErrorCallback
    stm32f4xx_hal_adc.o(i.HAL_ADC_Init) refers to adc.o(i.HAL_ADC_MspInit) for HAL_ADC_MspInit
    stm32f4xx_hal_adc.o(i.HAL_ADC_Init) refers to stm32f4xx_hal_adc.o(i.ADC_Init) for ADC_Init
    stm32f4xx_hal_adc.o(i.HAL_ADC_PollForConversion) refers to stm32f4xx_hal.o(i.HAL_GetTick) for HAL_GetTick
    stm32f4xx_hal_adc.o(i.HAL_ADC_PollForEvent) refers to stm32f4xx_hal.o(i.HAL_GetTick) for HAL_GetTick
    stm32f4xx_hal_adc.o(i.HAL_ADC_Start) refers to system_stm32f4xx.o(.data) for SystemCoreClock
    stm32f4xx_hal_adc.o(i.HAL_ADC_Start_DMA) refers to stm32f4xx_hal_dma.o(i.HAL_DMA_Start_IT) for HAL_DMA_Start_IT
    stm32f4xx_hal_adc.o(i.HAL_ADC_Start_DMA) refers to system_stm32f4xx.o(.data) for SystemCoreClock
    stm32f4xx_hal_adc.o(i.HAL_ADC_Start_DMA) refers to stm32f4xx_hal_adc.o(i.ADC_DMAConvCplt) for ADC_DMAConvCplt
    stm32f4xx_hal_adc.o(i.HAL_ADC_Start_DMA) refers to stm32f4xx_hal_adc.o(i.ADC_DMAHalfConvCplt) for ADC_DMAHalfConvCplt
    stm32f4xx_hal_adc.o(i.HAL_ADC_Start_DMA) refers to stm32f4xx_hal_adc.o(i.ADC_DMAError) for ADC_DMAError
    stm32f4xx_hal_adc.o(i.HAL_ADC_Start_IT) refers to system_stm32f4xx.o(.data) for SystemCoreClock
    stm32f4xx_hal_adc.o(i.HAL_ADC_Stop_DMA) refers to stm32f4xx_hal_dma.o(i.HAL_DMA_Abort) for HAL_DMA_Abort
    stm32f4xx_hal_adc_ex.o(i.ADC_MultiModeDMAConvCplt) refers to main.o(i.HAL_ADC_ConvCpltCallback) for HAL_ADC_ConvCpltCallback
    stm32f4xx_hal_adc_ex.o(i.ADC_MultiModeDMAError) refers to stm32f4xx_hal_adc.o(i.HAL_ADC_ErrorCallback) for HAL_ADC_ErrorCallback
    stm32f4xx_hal_adc_ex.o(i.ADC_MultiModeDMAHalfConvCplt) refers to stm32f4xx_hal_adc.o(i.HAL_ADC_ConvHalfCpltCallback) for HAL_ADC_ConvHalfCpltCallback
    stm32f4xx_hal_adc_ex.o(i.HAL_ADCEx_InjectedPollForConversion) refers to stm32f4xx_hal.o(i.HAL_GetTick) for HAL_GetTick
    stm32f4xx_hal_adc_ex.o(i.HAL_ADCEx_InjectedStart) refers to system_stm32f4xx.o(.data) for SystemCoreClock
    stm32f4xx_hal_adc_ex.o(i.HAL_ADCEx_InjectedStart_IT) refers to system_stm32f4xx.o(.data) for SystemCoreClock
    stm32f4xx_hal_adc_ex.o(i.HAL_ADCEx_MultiModeStart_DMA) refers to stm32f4xx_hal_dma.o(i.HAL_DMA_Start_IT) for HAL_DMA_Start_IT
    stm32f4xx_hal_adc_ex.o(i.HAL_ADCEx_MultiModeStart_DMA) refers to system_stm32f4xx.o(.data) for SystemCoreClock
    stm32f4xx_hal_adc_ex.o(i.HAL_ADCEx_MultiModeStart_DMA) refers to stm32f4xx_hal_adc_ex.o(i.ADC_MultiModeDMAConvCplt) for ADC_MultiModeDMAConvCplt
    stm32f4xx_hal_adc_ex.o(i.HAL_ADCEx_MultiModeStart_DMA) refers to stm32f4xx_hal_adc_ex.o(i.ADC_MultiModeDMAHalfConvCplt) for ADC_MultiModeDMAHalfConvCplt
    stm32f4xx_hal_adc_ex.o(i.HAL_ADCEx_MultiModeStart_DMA) refers to stm32f4xx_hal_adc_ex.o(i.ADC_MultiModeDMAError) for ADC_MultiModeDMAError
    stm32f4xx_hal_adc_ex.o(i.HAL_ADCEx_MultiModeStop_DMA) refers to stm32f4xx_hal_dma.o(i.HAL_DMA_Abort) for HAL_DMA_Abort
    stm32f4xx_hal_rcc.o(i.HAL_RCC_ClockConfig) refers to stm32f4xx_hal.o(i.HAL_GetTick) for HAL_GetTick
    stm32f4xx_hal_rcc.o(i.HAL_RCC_ClockConfig) refers to stm32f4xx_hal_rcc.o(i.HAL_RCC_GetSysClockFreq) for HAL_RCC_GetSysClockFreq
    stm32f4xx_hal_rcc.o(i.HAL_RCC_ClockConfig) refers to stm32f4xx_hal.o(i.HAL_InitTick) for HAL_InitTick
    stm32f4xx_hal_rcc.o(i.HAL_RCC_ClockConfig) refers to system_stm32f4xx.o(.constdata) for AHBPrescTable
    stm32f4xx_hal_rcc.o(i.HAL_RCC_ClockConfig) refers to system_stm32f4xx.o(.data) for SystemCoreClock
    stm32f4xx_hal_rcc.o(i.HAL_RCC_ClockConfig) refers to stm32f4xx_hal.o(.data) for uwTickPrio
    stm32f4xx_hal_rcc.o(i.HAL_RCC_GetHCLKFreq) refers to system_stm32f4xx.o(.data) for SystemCoreClock
    stm32f4xx_hal_rcc.o(i.HAL_RCC_GetPCLK1Freq) refers to system_stm32f4xx.o(.data) for SystemCoreClock
    stm32f4xx_hal_rcc.o(i.HAL_RCC_GetPCLK1Freq) refers to system_stm32f4xx.o(.constdata) for APBPrescTable
    stm32f4xx_hal_rcc.o(i.HAL_RCC_GetPCLK2Freq) refers to system_stm32f4xx.o(.data) for SystemCoreClock
    stm32f4xx_hal_rcc.o(i.HAL_RCC_GetPCLK2Freq) refers to system_stm32f4xx.o(.constdata) for APBPrescTable
    stm32f4xx_hal_rcc.o(i.HAL_RCC_GetSysClockFreq) refers to uldiv.o(.text) for __aeabi_uldivmod
    stm32f4xx_hal_rcc.o(i.HAL_RCC_MCOConfig) refers to stm32f4xx_hal_gpio.o(i.HAL_GPIO_Init) for HAL_GPIO_Init
    stm32f4xx_hal_rcc.o(i.HAL_RCC_NMI_IRQHandler) refers to stm32f4xx_hal_rcc.o(i.HAL_RCC_CSSCallback) for HAL_RCC_CSSCallback
    stm32f4xx_hal_rcc.o(i.HAL_RCC_OscConfig) refers to stm32f4xx_hal.o(i.HAL_GetTick) for HAL_GetTick
    stm32f4xx_hal_rcc_ex.o(i.HAL_RCCEx_DisablePLLI2S) refers to stm32f4xx_hal.o(i.HAL_GetTick) for HAL_GetTick
    stm32f4xx_hal_rcc_ex.o(i.HAL_RCCEx_EnablePLLI2S) refers to stm32f4xx_hal.o(i.HAL_GetTick) for HAL_GetTick
    stm32f4xx_hal_rcc_ex.o(i.HAL_RCCEx_PeriphCLKConfig) refers to stm32f4xx_hal.o(i.HAL_GetTick) for HAL_GetTick
    stm32f4xx_hal_rcc_ex.o(i.HAL_RCC_DeInit) refers to stm32f4xx_hal.o(i.HAL_GetTick) for HAL_GetTick
    stm32f4xx_hal_rcc_ex.o(i.HAL_RCC_DeInit) refers to stm32f4xx_hal.o(i.HAL_InitTick) for HAL_InitTick
    stm32f4xx_hal_rcc_ex.o(i.HAL_RCC_DeInit) refers to system_stm32f4xx.o(.data) for SystemCoreClock
    stm32f4xx_hal_rcc_ex.o(i.HAL_RCC_DeInit) refers to stm32f4xx_hal.o(.data) for uwTickPrio
    stm32f4xx_hal_flash.o(i.FLASH_SetErrorCode) refers to stm32f4xx_hal_flash.o(.bss) for .bss
    stm32f4xx_hal_flash.o(i.FLASH_WaitForLastOperation) refers to stm32f4xx_hal.o(i.HAL_GetTick) for HAL_GetTick
    stm32f4xx_hal_flash.o(i.FLASH_WaitForLastOperation) refers to stm32f4xx_hal_flash.o(i.FLASH_SetErrorCode) for FLASH_SetErrorCode
    stm32f4xx_hal_flash.o(i.FLASH_WaitForLastOperation) refers to stm32f4xx_hal_flash.o(.bss) for .bss
    stm32f4xx_hal_flash.o(i.HAL_FLASH_GetError) refers to stm32f4xx_hal_flash.o(.bss) for .bss
    stm32f4xx_hal_flash.o(i.HAL_FLASH_IRQHandler) refers to stm32f4xx_hal_flash.o(i.FLASH_SetErrorCode) for FLASH_SetErrorCode
    stm32f4xx_hal_flash.o(i.HAL_FLASH_IRQHandler) refers to stm32f4xx_hal_flash.o(i.HAL_FLASH_OperationErrorCallback) for HAL_FLASH_OperationErrorCallback
    stm32f4xx_hal_flash.o(i.HAL_FLASH_IRQHandler) refers to stm32f4xx_hal_flash.o(i.HAL_FLASH_EndOfOperationCallback) for HAL_FLASH_EndOfOperationCallback
    stm32f4xx_hal_flash.o(i.HAL_FLASH_IRQHandler) refers to stm32f4xx_hal_flash_ex.o(i.FLASH_Erase_Sector) for FLASH_Erase_Sector
    stm32f4xx_hal_flash.o(i.HAL_FLASH_IRQHandler) refers to stm32f4xx_hal_flash_ex.o(i.FLASH_FlushCaches) for FLASH_FlushCaches
    stm32f4xx_hal_flash.o(i.HAL_FLASH_IRQHandler) refers to stm32f4xx_hal_flash.o(.bss) for .bss
    stm32f4xx_hal_flash.o(i.HAL_FLASH_OB_Launch) refers to stm32f4xx_hal_flash.o(i.FLASH_WaitForLastOperation) for FLASH_WaitForLastOperation
    stm32f4xx_hal_flash.o(i.HAL_FLASH_Program) refers to stm32f4xx_hal_flash.o(i.FLASH_WaitForLastOperation) for FLASH_WaitForLastOperation
    stm32f4xx_hal_flash.o(i.HAL_FLASH_Program) refers to stm32f4xx_hal_flash.o(i.FLASH_Program_DoubleWord) for FLASH_Program_DoubleWord
    stm32f4xx_hal_flash.o(i.HAL_FLASH_Program) refers to stm32f4xx_hal_flash.o(i.FLASH_Program_Byte) for FLASH_Program_Byte
    stm32f4xx_hal_flash.o(i.HAL_FLASH_Program) refers to stm32f4xx_hal_flash.o(i.FLASH_Program_HalfWord) for FLASH_Program_HalfWord
    stm32f4xx_hal_flash.o(i.HAL_FLASH_Program) refers to stm32f4xx_hal_flash.o(i.FLASH_Program_Word) for FLASH_Program_Word
    stm32f4xx_hal_flash.o(i.HAL_FLASH_Program) refers to stm32f4xx_hal_flash.o(.bss) for .bss
    stm32f4xx_hal_flash.o(i.HAL_FLASH_Program_IT) refers to stm32f4xx_hal_flash.o(i.FLASH_Program_DoubleWord) for FLASH_Program_DoubleWord
    stm32f4xx_hal_flash.o(i.HAL_FLASH_Program_IT) refers to stm32f4xx_hal_flash.o(i.FLASH_Program_Byte) for FLASH_Program_Byte
    stm32f4xx_hal_flash.o(i.HAL_FLASH_Program_IT) refers to stm32f4xx_hal_flash.o(i.FLASH_Program_HalfWord) for FLASH_Program_HalfWord
    stm32f4xx_hal_flash.o(i.HAL_FLASH_Program_IT) refers to stm32f4xx_hal_flash.o(i.FLASH_Program_Word) for FLASH_Program_Word
    stm32f4xx_hal_flash.o(i.HAL_FLASH_Program_IT) refers to stm32f4xx_hal_flash.o(.bss) for .bss
    stm32f4xx_hal_flash_ex.o(i.HAL_FLASHEx_Erase) refers to stm32f4xx_hal_flash.o(i.FLASH_WaitForLastOperation) for FLASH_WaitForLastOperation
    stm32f4xx_hal_flash_ex.o(i.HAL_FLASHEx_Erase) refers to stm32f4xx_hal_flash_ex.o(i.FLASH_MassErase) for FLASH_MassErase
    stm32f4xx_hal_flash_ex.o(i.HAL_FLASHEx_Erase) refers to stm32f4xx_hal_flash_ex.o(i.FLASH_Erase_Sector) for FLASH_Erase_Sector
    stm32f4xx_hal_flash_ex.o(i.HAL_FLASHEx_Erase) refers to stm32f4xx_hal_flash_ex.o(i.FLASH_FlushCaches) for FLASH_FlushCaches
    stm32f4xx_hal_flash_ex.o(i.HAL_FLASHEx_Erase) refers to stm32f4xx_hal_flash.o(.bss) for pFlash
    stm32f4xx_hal_flash_ex.o(i.HAL_FLASHEx_Erase_IT) refers to stm32f4xx_hal_flash_ex.o(i.FLASH_Erase_Sector) for FLASH_Erase_Sector
    stm32f4xx_hal_flash_ex.o(i.HAL_FLASHEx_Erase_IT) refers to stm32f4xx_hal_flash_ex.o(i.FLASH_MassErase) for FLASH_MassErase
    stm32f4xx_hal_flash_ex.o(i.HAL_FLASHEx_Erase_IT) refers to stm32f4xx_hal_flash.o(.bss) for pFlash
    stm32f4xx_hal_flash_ex.o(i.HAL_FLASHEx_OBProgram) refers to stm32f4xx_hal_flash.o(i.FLASH_WaitForLastOperation) for FLASH_WaitForLastOperation
    stm32f4xx_hal_flash_ex.o(i.HAL_FLASHEx_OBProgram) refers to stm32f4xx_hal_flash.o(.bss) for pFlash
    stm32f4xx_hal_gpio.o(i.HAL_GPIO_EXTI_IRQHandler) refers to stm32f4xx_hal_gpio.o(i.HAL_GPIO_EXTI_Callback) for HAL_GPIO_EXTI_Callback
    stm32f4xx_hal_dma_ex.o(i.HAL_DMAEx_MultiBufferStart) refers to stm32f4xx_hal_dma_ex.o(i.DMA_MultiBufferSetConfig) for DMA_MultiBufferSetConfig
    stm32f4xx_hal_dma_ex.o(i.HAL_DMAEx_MultiBufferStart_IT) refers to stm32f4xx_hal_dma_ex.o(i.DMA_MultiBufferSetConfig) for DMA_MultiBufferSetConfig
    stm32f4xx_hal_dma.o(i.DMA_CalcBaseAndBitshift) refers to stm32f4xx_hal_dma.o(.constdata) for .constdata
    stm32f4xx_hal_dma.o(i.HAL_DMA_Abort) refers to stm32f4xx_hal.o(i.HAL_GetTick) for HAL_GetTick
    stm32f4xx_hal_dma.o(i.HAL_DMA_DeInit) refers to stm32f4xx_hal_dma.o(i.DMA_CalcBaseAndBitshift) for DMA_CalcBaseAndBitshift
    stm32f4xx_hal_dma.o(i.HAL_DMA_IRQHandler) refers to system_stm32f4xx.o(.data) for SystemCoreClock
    stm32f4xx_hal_dma.o(i.HAL_DMA_Init) refers to stm32f4xx_hal.o(i.HAL_GetTick) for HAL_GetTick
    stm32f4xx_hal_dma.o(i.HAL_DMA_Init) refers to stm32f4xx_hal_dma.o(i.DMA_CheckFifoParam) for DMA_CheckFifoParam
    stm32f4xx_hal_dma.o(i.HAL_DMA_Init) refers to stm32f4xx_hal_dma.o(i.DMA_CalcBaseAndBitshift) for DMA_CalcBaseAndBitshift
    stm32f4xx_hal_dma.o(i.HAL_DMA_PollForTransfer) refers to stm32f4xx_hal.o(i.HAL_GetTick) for HAL_GetTick
    stm32f4xx_hal_dma.o(i.HAL_DMA_PollForTransfer) refers to stm32f4xx_hal_dma.o(i.HAL_DMA_Abort) for HAL_DMA_Abort
    stm32f4xx_hal_dma.o(i.HAL_DMA_Start) refers to stm32f4xx_hal_dma.o(i.DMA_SetConfig) for DMA_SetConfig
    stm32f4xx_hal_dma.o(i.HAL_DMA_Start_IT) refers to stm32f4xx_hal_dma.o(i.DMA_SetConfig) for DMA_SetConfig
    stm32f4xx_hal_pwr.o(i.HAL_PWR_PVD_IRQHandler) refers to stm32f4xx_hal_pwr.o(i.HAL_PWR_PVDCallback) for HAL_PWR_PVDCallback
    stm32f4xx_hal_pwr_ex.o(i.HAL_PWREx_ControlVoltageScaling) refers to stm32f4xx_hal.o(i.HAL_GetTick) for HAL_GetTick
    stm32f4xx_hal_pwr_ex.o(i.HAL_PWREx_DisableBkUpReg) refers to stm32f4xx_hal.o(i.HAL_GetTick) for HAL_GetTick
    stm32f4xx_hal_pwr_ex.o(i.HAL_PWREx_EnableBkUpReg) refers to stm32f4xx_hal.o(i.HAL_GetTick) for HAL_GetTick
    stm32f4xx_hal_cortex.o(i.HAL_NVIC_SetPriority) refers to stm32f4xx_hal_cortex.o(i.__NVIC_SetPriority) for __NVIC_SetPriority
    stm32f4xx_hal_cortex.o(i.HAL_SYSTICK_Config) refers to stm32f4xx_hal_cortex.o(i.__NVIC_SetPriority) for __NVIC_SetPriority
    stm32f4xx_hal_cortex.o(i.HAL_SYSTICK_IRQHandler) refers to stm32f4xx_hal_cortex.o(i.HAL_SYSTICK_Callback) for HAL_SYSTICK_Callback
    stm32f4xx_hal.o(i.HAL_DeInit) refers to stm32f4xx_hal.o(i.HAL_MspDeInit) for HAL_MspDeInit
    stm32f4xx_hal.o(i.HAL_Delay) refers to stm32f4xx_hal.o(i.HAL_GetTick) for HAL_GetTick
    stm32f4xx_hal.o(i.HAL_Delay) refers to stm32f4xx_hal.o(.data) for .data
    stm32f4xx_hal.o(i.HAL_GetTick) refers to stm32f4xx_hal.o(.data) for .data
    stm32f4xx_hal.o(i.HAL_GetTickFreq) refers to stm32f4xx_hal.o(.data) for .data
    stm32f4xx_hal.o(i.HAL_GetTickPrio) refers to stm32f4xx_hal.o(.data) for .data
    stm32f4xx_hal.o(i.HAL_IncTick) refers to stm32f4xx_hal.o(.data) for .data
    stm32f4xx_hal.o(i.HAL_Init) refers to stm32f4xx_hal_cortex.o(i.HAL_NVIC_SetPriorityGrouping) for HAL_NVIC_SetPriorityGrouping
    stm32f4xx_hal.o(i.HAL_Init) refers to stm32f4xx_hal.o(i.HAL_InitTick) for HAL_InitTick
    stm32f4xx_hal.o(i.HAL_Init) refers to stm32f4xx_hal_msp.o(i.HAL_MspInit) for HAL_MspInit
    stm32f4xx_hal.o(i.HAL_InitTick) refers to stm32f4xx_hal_cortex.o(i.HAL_SYSTICK_Config) for HAL_SYSTICK_Config
    stm32f4xx_hal.o(i.HAL_InitTick) refers to stm32f4xx_hal_cortex.o(i.HAL_NVIC_SetPriority) for HAL_NVIC_SetPriority
    stm32f4xx_hal.o(i.HAL_InitTick) refers to stm32f4xx_hal.o(.data) for .data
    stm32f4xx_hal.o(i.HAL_InitTick) refers to system_stm32f4xx.o(.data) for SystemCoreClock
    stm32f4xx_hal.o(i.HAL_SetTickFreq) refers to stm32f4xx_hal.o(i.HAL_InitTick) for HAL_InitTick
    stm32f4xx_hal.o(i.HAL_SetTickFreq) refers to stm32f4xx_hal.o(.data) for .data
    stm32f4xx_hal_tim.o(i.HAL_TIM_Base_DeInit) refers to tim.o(i.HAL_TIM_Base_MspDeInit) for HAL_TIM_Base_MspDeInit
    stm32f4xx_hal_tim.o(i.HAL_TIM_Base_Init) refers to tim.o(i.HAL_TIM_Base_MspInit) for HAL_TIM_Base_MspInit
    stm32f4xx_hal_tim.o(i.HAL_TIM_Base_Init) refers to stm32f4xx_hal_tim.o(i.TIM_Base_SetConfig) for TIM_Base_SetConfig
    stm32f4xx_hal_tim.o(i.HAL_TIM_Base_Start_DMA) refers to stm32f4xx_hal_dma.o(i.HAL_DMA_Start_IT) for HAL_DMA_Start_IT
    stm32f4xx_hal_tim.o(i.HAL_TIM_Base_Start_DMA) refers to stm32f4xx_hal_tim.o(i.TIM_DMAPeriodElapsedCplt) for TIM_DMAPeriodElapsedCplt
    stm32f4xx_hal_tim.o(i.HAL_TIM_Base_Start_DMA) refers to stm32f4xx_hal_tim.o(i.TIM_DMAPeriodElapsedHalfCplt) for TIM_DMAPeriodElapsedHalfCplt
    stm32f4xx_hal_tim.o(i.HAL_TIM_Base_Start_DMA) refers to stm32f4xx_hal_tim.o(i.TIM_DMAError) for TIM_DMAError
    stm32f4xx_hal_tim.o(i.HAL_TIM_Base_Stop_DMA) refers to stm32f4xx_hal_dma.o(i.HAL_DMA_Abort_IT) for HAL_DMA_Abort_IT
    stm32f4xx_hal_tim.o(i.HAL_TIM_ConfigClockSource) refers to stm32f4xx_hal_tim.o(i.TIM_ETR_SetConfig) for TIM_ETR_SetConfig
    stm32f4xx_hal_tim.o(i.HAL_TIM_ConfigClockSource) refers to stm32f4xx_hal_tim.o(i.TIM_TI1_ConfigInputStage) for TIM_TI1_ConfigInputStage
    stm32f4xx_hal_tim.o(i.HAL_TIM_ConfigClockSource) refers to stm32f4xx_hal_tim.o(i.TIM_TI2_ConfigInputStage) for TIM_TI2_ConfigInputStage
    stm32f4xx_hal_tim.o(i.HAL_TIM_ConfigClockSource) refers to stm32f4xx_hal_tim.o(i.TIM_ITRx_SetConfig) for TIM_ITRx_SetConfig
    stm32f4xx_hal_tim.o(i.HAL_TIM_ConfigOCrefClear) refers to stm32f4xx_hal_tim.o(i.TIM_ETR_SetConfig) for TIM_ETR_SetConfig
    stm32f4xx_hal_tim.o(i.HAL_TIM_DMABurst_MultiReadStart) refers to stm32f4xx_hal_dma.o(i.HAL_DMA_Start_IT) for HAL_DMA_Start_IT
    stm32f4xx_hal_tim.o(i.HAL_TIM_DMABurst_MultiReadStart) refers to stm32f4xx_hal_tim.o(i.TIM_DMAError) for TIM_DMAError
    stm32f4xx_hal_tim.o(i.HAL_TIM_DMABurst_MultiReadStart) refers to stm32f4xx_hal_tim.o(i.TIM_DMACaptureCplt) for TIM_DMACaptureCplt
    stm32f4xx_hal_tim.o(i.HAL_TIM_DMABurst_MultiReadStart) refers to stm32f4xx_hal_tim.o(i.TIM_DMACaptureHalfCplt) for TIM_DMACaptureHalfCplt
    stm32f4xx_hal_tim.o(i.HAL_TIM_DMABurst_MultiReadStart) refers to stm32f4xx_hal_tim.o(i.TIM_DMAPeriodElapsedCplt) for TIM_DMAPeriodElapsedCplt
    stm32f4xx_hal_tim.o(i.HAL_TIM_DMABurst_MultiReadStart) refers to stm32f4xx_hal_tim.o(i.TIM_DMAPeriodElapsedHalfCplt) for TIM_DMAPeriodElapsedHalfCplt
    stm32f4xx_hal_tim.o(i.HAL_TIM_DMABurst_MultiReadStart) refers to stm32f4xx_hal_tim_ex.o(i.TIMEx_DMACommutationCplt) for TIMEx_DMACommutationCplt
    stm32f4xx_hal_tim.o(i.HAL_TIM_DMABurst_MultiReadStart) refers to stm32f4xx_hal_tim_ex.o(i.TIMEx_DMACommutationHalfCplt) for TIMEx_DMACommutationHalfCplt
    stm32f4xx_hal_tim.o(i.HAL_TIM_DMABurst_MultiReadStart) refers to stm32f4xx_hal_tim.o(i.TIM_DMATriggerCplt) for TIM_DMATriggerCplt
    stm32f4xx_hal_tim.o(i.HAL_TIM_DMABurst_MultiReadStart) refers to stm32f4xx_hal_tim.o(i.TIM_DMATriggerHalfCplt) for TIM_DMATriggerHalfCplt
    stm32f4xx_hal_tim.o(i.HAL_TIM_DMABurst_MultiWriteStart) refers to stm32f4xx_hal_dma.o(i.HAL_DMA_Start_IT) for HAL_DMA_Start_IT
    stm32f4xx_hal_tim.o(i.HAL_TIM_DMABurst_MultiWriteStart) refers to stm32f4xx_hal_tim.o(i.TIM_DMAError) for TIM_DMAError
    stm32f4xx_hal_tim.o(i.HAL_TIM_DMABurst_MultiWriteStart) refers to stm32f4xx_hal_tim.o(i.TIM_DMADelayPulseCplt) for TIM_DMADelayPulseCplt
    stm32f4xx_hal_tim.o(i.HAL_TIM_DMABurst_MultiWriteStart) refers to stm32f4xx_hal_tim.o(i.TIM_DMADelayPulseHalfCplt) for TIM_DMADelayPulseHalfCplt
    stm32f4xx_hal_tim.o(i.HAL_TIM_DMABurst_MultiWriteStart) refers to stm32f4xx_hal_tim.o(i.TIM_DMAPeriodElapsedCplt) for TIM_DMAPeriodElapsedCplt
    stm32f4xx_hal_tim.o(i.HAL_TIM_DMABurst_MultiWriteStart) refers to stm32f4xx_hal_tim.o(i.TIM_DMAPeriodElapsedHalfCplt) for TIM_DMAPeriodElapsedHalfCplt
    stm32f4xx_hal_tim.o(i.HAL_TIM_DMABurst_MultiWriteStart) refers to stm32f4xx_hal_tim_ex.o(i.TIMEx_DMACommutationCplt) for TIMEx_DMACommutationCplt
    stm32f4xx_hal_tim.o(i.HAL_TIM_DMABurst_MultiWriteStart) refers to stm32f4xx_hal_tim_ex.o(i.TIMEx_DMACommutationHalfCplt) for TIMEx_DMACommutationHalfCplt
    stm32f4xx_hal_tim.o(i.HAL_TIM_DMABurst_MultiWriteStart) refers to stm32f4xx_hal_tim.o(i.TIM_DMATriggerCplt) for TIM_DMATriggerCplt
    stm32f4xx_hal_tim.o(i.HAL_TIM_DMABurst_MultiWriteStart) refers to stm32f4xx_hal_tim.o(i.TIM_DMATriggerHalfCplt) for TIM_DMATriggerHalfCplt
    stm32f4xx_hal_tim.o(i.HAL_TIM_DMABurst_ReadStart) refers to stm32f4xx_hal_tim.o(i.HAL_TIM_DMABurst_MultiReadStart) for HAL_TIM_DMABurst_MultiReadStart
    stm32f4xx_hal_tim.o(i.HAL_TIM_DMABurst_ReadStop) refers to stm32f4xx_hal_dma.o(i.HAL_DMA_Abort_IT) for HAL_DMA_Abort_IT
    stm32f4xx_hal_tim.o(i.HAL_TIM_DMABurst_WriteStart) refers to stm32f4xx_hal_tim.o(i.HAL_TIM_DMABurst_MultiWriteStart) for HAL_TIM_DMABurst_MultiWriteStart
    stm32f4xx_hal_tim.o(i.HAL_TIM_DMABurst_WriteStop) refers to stm32f4xx_hal_dma.o(i.HAL_DMA_Abort_IT) for HAL_DMA_Abort_IT
    stm32f4xx_hal_tim.o(i.HAL_TIM_Encoder_DeInit) refers to stm32f4xx_hal_tim.o(i.HAL_TIM_Encoder_MspDeInit) for HAL_TIM_Encoder_MspDeInit
    stm32f4xx_hal_tim.o(i.HAL_TIM_Encoder_Init) refers to stm32f4xx_hal_tim.o(i.HAL_TIM_Encoder_MspInit) for HAL_TIM_Encoder_MspInit
    stm32f4xx_hal_tim.o(i.HAL_TIM_Encoder_Init) refers to stm32f4xx_hal_tim.o(i.TIM_Base_SetConfig) for TIM_Base_SetConfig
    stm32f4xx_hal_tim.o(i.HAL_TIM_Encoder_Start) refers to stm32f4xx_hal_tim.o(i.TIM_CCxChannelCmd) for TIM_CCxChannelCmd
    stm32f4xx_hal_tim.o(i.HAL_TIM_Encoder_Start_DMA) refers to stm32f4xx_hal_dma.o(i.HAL_DMA_Start_IT) for HAL_DMA_Start_IT
    stm32f4xx_hal_tim.o(i.HAL_TIM_Encoder_Start_DMA) refers to stm32f4xx_hal_tim.o(i.TIM_CCxChannelCmd) for TIM_CCxChannelCmd
    stm32f4xx_hal_tim.o(i.HAL_TIM_Encoder_Start_DMA) refers to stm32f4xx_hal_tim.o(i.TIM_DMACaptureCplt) for TIM_DMACaptureCplt
    stm32f4xx_hal_tim.o(i.HAL_TIM_Encoder_Start_DMA) refers to stm32f4xx_hal_tim.o(i.TIM_DMACaptureHalfCplt) for TIM_DMACaptureHalfCplt
    stm32f4xx_hal_tim.o(i.HAL_TIM_Encoder_Start_DMA) refers to stm32f4xx_hal_tim.o(i.TIM_DMAError) for TIM_DMAError
    stm32f4xx_hal_tim.o(i.HAL_TIM_Encoder_Start_IT) refers to stm32f4xx_hal_tim.o(i.TIM_CCxChannelCmd) for TIM_CCxChannelCmd
    stm32f4xx_hal_tim.o(i.HAL_TIM_Encoder_Stop) refers to stm32f4xx_hal_tim.o(i.TIM_CCxChannelCmd) for TIM_CCxChannelCmd
    stm32f4xx_hal_tim.o(i.HAL_TIM_Encoder_Stop_DMA) refers to stm32f4xx_hal_tim.o(i.TIM_CCxChannelCmd) for TIM_CCxChannelCmd
    stm32f4xx_hal_tim.o(i.HAL_TIM_Encoder_Stop_DMA) refers to stm32f4xx_hal_dma.o(i.HAL_DMA_Abort_IT) for HAL_DMA_Abort_IT
    stm32f4xx_hal_tim.o(i.HAL_TIM_Encoder_Stop_IT) refers to stm32f4xx_hal_tim.o(i.TIM_CCxChannelCmd) for TIM_CCxChannelCmd
    stm32f4xx_hal_tim.o(i.HAL_TIM_IC_ConfigChannel) refers to stm32f4xx_hal_tim.o(i.TIM_TI1_SetConfig) for TIM_TI1_SetConfig
    stm32f4xx_hal_tim.o(i.HAL_TIM_IC_ConfigChannel) refers to stm32f4xx_hal_tim.o(i.TIM_TI2_SetConfig) for TIM_TI2_SetConfig
    stm32f4xx_hal_tim.o(i.HAL_TIM_IC_DeInit) refers to stm32f4xx_hal_tim.o(i.HAL_TIM_IC_MspDeInit) for HAL_TIM_IC_MspDeInit
    stm32f4xx_hal_tim.o(i.HAL_TIM_IC_Init) refers to stm32f4xx_hal_tim.o(i.HAL_TIM_IC_MspInit) for HAL_TIM_IC_MspInit
    stm32f4xx_hal_tim.o(i.HAL_TIM_IC_Init) refers to stm32f4xx_hal_tim.o(i.TIM_Base_SetConfig) for TIM_Base_SetConfig
    stm32f4xx_hal_tim.o(i.HAL_TIM_IC_Start) refers to stm32f4xx_hal_tim.o(i.TIM_CCxChannelCmd) for TIM_CCxChannelCmd
    stm32f4xx_hal_tim.o(i.HAL_TIM_IC_Start_DMA) refers to stm32f4xx_hal_tim.o(i.TIM_CCxChannelCmd) for TIM_CCxChannelCmd
    stm32f4xx_hal_tim.o(i.HAL_TIM_IC_Start_DMA) refers to stm32f4xx_hal_dma.o(i.HAL_DMA_Start_IT) for HAL_DMA_Start_IT
    stm32f4xx_hal_tim.o(i.HAL_TIM_IC_Start_DMA) refers to stm32f4xx_hal_tim.o(i.TIM_DMACaptureCplt) for TIM_DMACaptureCplt
    stm32f4xx_hal_tim.o(i.HAL_TIM_IC_Start_DMA) refers to stm32f4xx_hal_tim.o(i.TIM_DMACaptureHalfCplt) for TIM_DMACaptureHalfCplt
    stm32f4xx_hal_tim.o(i.HAL_TIM_IC_Start_DMA) refers to stm32f4xx_hal_tim.o(i.TIM_DMAError) for TIM_DMAError
    stm32f4xx_hal_tim.o(i.HAL_TIM_IC_Start_IT) refers to stm32f4xx_hal_tim.o(i.TIM_CCxChannelCmd) for TIM_CCxChannelCmd
    stm32f4xx_hal_tim.o(i.HAL_TIM_IC_Stop) refers to stm32f4xx_hal_tim.o(i.TIM_CCxChannelCmd) for TIM_CCxChannelCmd
    stm32f4xx_hal_tim.o(i.HAL_TIM_IC_Stop_DMA) refers to stm32f4xx_hal_tim.o(i.TIM_CCxChannelCmd) for TIM_CCxChannelCmd
    stm32f4xx_hal_tim.o(i.HAL_TIM_IC_Stop_DMA) refers to stm32f4xx_hal_dma.o(i.HAL_DMA_Abort_IT) for HAL_DMA_Abort_IT
    stm32f4xx_hal_tim.o(i.HAL_TIM_IC_Stop_IT) refers to stm32f4xx_hal_tim.o(i.TIM_CCxChannelCmd) for TIM_CCxChannelCmd
    stm32f4xx_hal_tim.o(i.HAL_TIM_IRQHandler) refers to stm32f4xx_hal_tim.o(i.HAL_TIM_IC_CaptureCallback) for HAL_TIM_IC_CaptureCallback
    stm32f4xx_hal_tim.o(i.HAL_TIM_IRQHandler) refers to stm32f4xx_hal_tim.o(i.HAL_TIM_OC_DelayElapsedCallback) for HAL_TIM_OC_DelayElapsedCallback
    stm32f4xx_hal_tim.o(i.HAL_TIM_IRQHandler) refers to stm32f4xx_hal_tim.o(i.HAL_TIM_PWM_PulseFinishedCallback) for HAL_TIM_PWM_PulseFinishedCallback
    stm32f4xx_hal_tim.o(i.HAL_TIM_IRQHandler) refers to stm32f4xx_hal_tim.o(i.HAL_TIM_PeriodElapsedCallback) for HAL_TIM_PeriodElapsedCallback
    stm32f4xx_hal_tim.o(i.HAL_TIM_IRQHandler) refers to stm32f4xx_hal_tim_ex.o(i.HAL_TIMEx_BreakCallback) for HAL_TIMEx_BreakCallback
    stm32f4xx_hal_tim.o(i.HAL_TIM_IRQHandler) refers to stm32f4xx_hal_tim.o(i.HAL_TIM_TriggerCallback) for HAL_TIM_TriggerCallback
    stm32f4xx_hal_tim.o(i.HAL_TIM_IRQHandler) refers to stm32f4xx_hal_tim_ex.o(i.HAL_TIMEx_CommutCallback) for HAL_TIMEx_CommutCallback
    stm32f4xx_hal_tim.o(i.HAL_TIM_OC_ConfigChannel) refers to stm32f4xx_hal_tim.o(i.TIM_OC1_SetConfig) for TIM_OC1_SetConfig
    stm32f4xx_hal_tim.o(i.HAL_TIM_OC_ConfigChannel) refers to stm32f4xx_hal_tim.o(i.TIM_OC2_SetConfig) for TIM_OC2_SetConfig
    stm32f4xx_hal_tim.o(i.HAL_TIM_OC_ConfigChannel) refers to stm32f4xx_hal_tim.o(i.TIM_OC3_SetConfig) for TIM_OC3_SetConfig
    stm32f4xx_hal_tim.o(i.HAL_TIM_OC_ConfigChannel) refers to stm32f4xx_hal_tim.o(i.TIM_OC4_SetConfig) for TIM_OC4_SetConfig
    stm32f4xx_hal_tim.o(i.HAL_TIM_OC_DeInit) refers to stm32f4xx_hal_tim.o(i.HAL_TIM_OC_MspDeInit) for HAL_TIM_OC_MspDeInit
    stm32f4xx_hal_tim.o(i.HAL_TIM_OC_Init) refers to stm32f4xx_hal_tim.o(i.HAL_TIM_OC_MspInit) for HAL_TIM_OC_MspInit
    stm32f4xx_hal_tim.o(i.HAL_TIM_OC_Init) refers to stm32f4xx_hal_tim.o(i.TIM_Base_SetConfig) for TIM_Base_SetConfig
    stm32f4xx_hal_tim.o(i.HAL_TIM_OC_Start) refers to stm32f4xx_hal_tim.o(i.TIM_CCxChannelCmd) for TIM_CCxChannelCmd
    stm32f4xx_hal_tim.o(i.HAL_TIM_OC_Start_DMA) refers to stm32f4xx_hal_dma.o(i.HAL_DMA_Start_IT) for HAL_DMA_Start_IT
    stm32f4xx_hal_tim.o(i.HAL_TIM_OC_Start_DMA) refers to stm32f4xx_hal_tim.o(i.TIM_CCxChannelCmd) for TIM_CCxChannelCmd
    stm32f4xx_hal_tim.o(i.HAL_TIM_OC_Start_DMA) refers to stm32f4xx_hal_tim.o(i.TIM_DMADelayPulseCplt) for TIM_DMADelayPulseCplt
    stm32f4xx_hal_tim.o(i.HAL_TIM_OC_Start_DMA) refers to stm32f4xx_hal_tim.o(i.TIM_DMADelayPulseHalfCplt) for TIM_DMADelayPulseHalfCplt
    stm32f4xx_hal_tim.o(i.HAL_TIM_OC_Start_DMA) refers to stm32f4xx_hal_tim.o(i.TIM_DMAError) for TIM_DMAError
    stm32f4xx_hal_tim.o(i.HAL_TIM_OC_Start_IT) refers to stm32f4xx_hal_tim.o(i.TIM_CCxChannelCmd) for TIM_CCxChannelCmd
    stm32f4xx_hal_tim.o(i.HAL_TIM_OC_Stop) refers to stm32f4xx_hal_tim.o(i.TIM_CCxChannelCmd) for TIM_CCxChannelCmd
    stm32f4xx_hal_tim.o(i.HAL_TIM_OC_Stop_DMA) refers to stm32f4xx_hal_dma.o(i.HAL_DMA_Abort_IT) for HAL_DMA_Abort_IT
    stm32f4xx_hal_tim.o(i.HAL_TIM_OC_Stop_DMA) refers to stm32f4xx_hal_tim.o(i.TIM_CCxChannelCmd) for TIM_CCxChannelCmd
    stm32f4xx_hal_tim.o(i.HAL_TIM_OC_Stop_IT) refers to stm32f4xx_hal_tim.o(i.TIM_CCxChannelCmd) for TIM_CCxChannelCmd
    stm32f4xx_hal_tim.o(i.HAL_TIM_OnePulse_ConfigChannel) refers to stm32f4xx_hal_tim.o(i.TIM_OC1_SetConfig) for TIM_OC1_SetConfig
    stm32f4xx_hal_tim.o(i.HAL_TIM_OnePulse_ConfigChannel) refers to stm32f4xx_hal_tim.o(i.TIM_OC2_SetConfig) for TIM_OC2_SetConfig
    stm32f4xx_hal_tim.o(i.HAL_TIM_OnePulse_ConfigChannel) refers to stm32f4xx_hal_tim.o(i.TIM_TI1_SetConfig) for TIM_TI1_SetConfig
    stm32f4xx_hal_tim.o(i.HAL_TIM_OnePulse_ConfigChannel) refers to stm32f4xx_hal_tim.o(i.TIM_TI2_SetConfig) for TIM_TI2_SetConfig
    stm32f4xx_hal_tim.o(i.HAL_TIM_OnePulse_DeInit) refers to stm32f4xx_hal_tim.o(i.HAL_TIM_OnePulse_MspDeInit) for HAL_TIM_OnePulse_MspDeInit
    stm32f4xx_hal_tim.o(i.HAL_TIM_OnePulse_Init) refers to stm32f4xx_hal_tim.o(i.HAL_TIM_OnePulse_MspInit) for HAL_TIM_OnePulse_MspInit
    stm32f4xx_hal_tim.o(i.HAL_TIM_OnePulse_Init) refers to stm32f4xx_hal_tim.o(i.TIM_Base_SetConfig) for TIM_Base_SetConfig
    stm32f4xx_hal_tim.o(i.HAL_TIM_OnePulse_Start) refers to stm32f4xx_hal_tim.o(i.TIM_CCxChannelCmd) for TIM_CCxChannelCmd
    stm32f4xx_hal_tim.o(i.HAL_TIM_OnePulse_Start_IT) refers to stm32f4xx_hal_tim.o(i.TIM_CCxChannelCmd) for TIM_CCxChannelCmd
    stm32f4xx_hal_tim.o(i.HAL_TIM_OnePulse_Stop) refers to stm32f4xx_hal_tim.o(i.TIM_CCxChannelCmd) for TIM_CCxChannelCmd
    stm32f4xx_hal_tim.o(i.HAL_TIM_OnePulse_Stop_IT) refers to stm32f4xx_hal_tim.o(i.TIM_CCxChannelCmd) for TIM_CCxChannelCmd
    stm32f4xx_hal_tim.o(i.HAL_TIM_PWM_ConfigChannel) refers to stm32f4xx_hal_tim.o(i.TIM_OC1_SetConfig) for TIM_OC1_SetConfig
    stm32f4xx_hal_tim.o(i.HAL_TIM_PWM_ConfigChannel) refers to stm32f4xx_hal_tim.o(i.TIM_OC2_SetConfig) for TIM_OC2_SetConfig
    stm32f4xx_hal_tim.o(i.HAL_TIM_PWM_ConfigChannel) refers to stm32f4xx_hal_tim.o(i.TIM_OC3_SetConfig) for TIM_OC3_SetConfig
    stm32f4xx_hal_tim.o(i.HAL_TIM_PWM_ConfigChannel) refers to stm32f4xx_hal_tim.o(i.TIM_OC4_SetConfig) for TIM_OC4_SetConfig
    stm32f4xx_hal_tim.o(i.HAL_TIM_PWM_DeInit) refers to stm32f4xx_hal_tim.o(i.HAL_TIM_PWM_MspDeInit) for HAL_TIM_PWM_MspDeInit
    stm32f4xx_hal_tim.o(i.HAL_TIM_PWM_Init) refers to stm32f4xx_hal_tim.o(i.HAL_TIM_PWM_MspInit) for HAL_TIM_PWM_MspInit
    stm32f4xx_hal_tim.o(i.HAL_TIM_PWM_Init) refers to stm32f4xx_hal_tim.o(i.TIM_Base_SetConfig) for TIM_Base_SetConfig
    stm32f4xx_hal_tim.o(i.HAL_TIM_PWM_Start) refers to stm32f4xx_hal_tim.o(i.TIM_CCxChannelCmd) for TIM_CCxChannelCmd
    stm32f4xx_hal_tim.o(i.HAL_TIM_PWM_Start_DMA) refers to stm32f4xx_hal_dma.o(i.HAL_DMA_Start_IT) for HAL_DMA_Start_IT
    stm32f4xx_hal_tim.o(i.HAL_TIM_PWM_Start_DMA) refers to stm32f4xx_hal_tim.o(i.TIM_CCxChannelCmd) for TIM_CCxChannelCmd
    stm32f4xx_hal_tim.o(i.HAL_TIM_PWM_Start_DMA) refers to stm32f4xx_hal_tim.o(i.TIM_DMADelayPulseCplt) for TIM_DMADelayPulseCplt
    stm32f4xx_hal_tim.o(i.HAL_TIM_PWM_Start_DMA) refers to stm32f4xx_hal_tim.o(i.TIM_DMADelayPulseHalfCplt) for TIM_DMADelayPulseHalfCplt
    stm32f4xx_hal_tim.o(i.HAL_TIM_PWM_Start_DMA) refers to stm32f4xx_hal_tim.o(i.TIM_DMAError) for TIM_DMAError
    stm32f4xx_hal_tim.o(i.HAL_TIM_PWM_Start_IT) refers to stm32f4xx_hal_tim.o(i.TIM_CCxChannelCmd) for TIM_CCxChannelCmd
    stm32f4xx_hal_tim.o(i.HAL_TIM_PWM_Stop) refers to stm32f4xx_hal_tim.o(i.TIM_CCxChannelCmd) for TIM_CCxChannelCmd
    stm32f4xx_hal_tim.o(i.HAL_TIM_PWM_Stop_DMA) refers to stm32f4xx_hal_dma.o(i.HAL_DMA_Abort_IT) for HAL_DMA_Abort_IT
    stm32f4xx_hal_tim.o(i.HAL_TIM_PWM_Stop_DMA) refers to stm32f4xx_hal_tim.o(i.TIM_CCxChannelCmd) for TIM_CCxChannelCmd
    stm32f4xx_hal_tim.o(i.HAL_TIM_PWM_Stop_IT) refers to stm32f4xx_hal_tim.o(i.TIM_CCxChannelCmd) for TIM_CCxChannelCmd
    stm32f4xx_hal_tim.o(i.HAL_TIM_SlaveConfigSynchro) refers to stm32f4xx_hal_tim.o(i.TIM_SlaveTimer_SetConfig) for TIM_SlaveTimer_SetConfig
    stm32f4xx_hal_tim.o(i.HAL_TIM_SlaveConfigSynchro_IT) refers to stm32f4xx_hal_tim.o(i.TIM_SlaveTimer_SetConfig) for TIM_SlaveTimer_SetConfig
    stm32f4xx_hal_tim.o(i.TIM_DMACaptureCplt) refers to stm32f4xx_hal_tim.o(i.HAL_TIM_IC_CaptureCallback) for HAL_TIM_IC_CaptureCallback
    stm32f4xx_hal_tim.o(i.TIM_DMACaptureHalfCplt) refers to stm32f4xx_hal_tim.o(i.HAL_TIM_IC_CaptureHalfCpltCallback) for HAL_TIM_IC_CaptureHalfCpltCallback
    stm32f4xx_hal_tim.o(i.TIM_DMADelayPulseCplt) refers to stm32f4xx_hal_tim.o(i.HAL_TIM_PWM_PulseFinishedCallback) for HAL_TIM_PWM_PulseFinishedCallback
    stm32f4xx_hal_tim.o(i.TIM_DMADelayPulseHalfCplt) refers to stm32f4xx_hal_tim.o(i.HAL_TIM_PWM_PulseFinishedHalfCpltCallback) for HAL_TIM_PWM_PulseFinishedHalfCpltCallback
    stm32f4xx_hal_tim.o(i.TIM_DMAError) refers to stm32f4xx_hal_tim.o(i.HAL_TIM_ErrorCallback) for HAL_TIM_ErrorCallback
    stm32f4xx_hal_tim.o(i.TIM_DMAPeriodElapsedCplt) refers to stm32f4xx_hal_tim.o(i.HAL_TIM_PeriodElapsedCallback) for HAL_TIM_PeriodElapsedCallback
    stm32f4xx_hal_tim.o(i.TIM_DMAPeriodElapsedHalfCplt) refers to stm32f4xx_hal_tim.o(i.HAL_TIM_PeriodElapsedHalfCpltCallback) for HAL_TIM_PeriodElapsedHalfCpltCallback
    stm32f4xx_hal_tim.o(i.TIM_DMATriggerCplt) refers to stm32f4xx_hal_tim.o(i.HAL_TIM_TriggerCallback) for HAL_TIM_TriggerCallback
    stm32f4xx_hal_tim.o(i.TIM_DMATriggerHalfCplt) refers to stm32f4xx_hal_tim.o(i.HAL_TIM_TriggerHalfCpltCallback) for HAL_TIM_TriggerHalfCpltCallback
    stm32f4xx_hal_tim.o(i.TIM_SlaveTimer_SetConfig) refers to stm32f4xx_hal_tim.o(i.TIM_ETR_SetConfig) for TIM_ETR_SetConfig
    stm32f4xx_hal_tim.o(i.TIM_SlaveTimer_SetConfig) refers to stm32f4xx_hal_tim.o(i.TIM_TI1_ConfigInputStage) for TIM_TI1_ConfigInputStage
    stm32f4xx_hal_tim.o(i.TIM_SlaveTimer_SetConfig) refers to stm32f4xx_hal_tim.o(i.TIM_TI2_ConfigInputStage) for TIM_TI2_ConfigInputStage
    stm32f4xx_hal_tim_ex.o(i.HAL_TIMEx_ConfigCommutEvent_DMA) refers to stm32f4xx_hal_tim_ex.o(i.TIMEx_DMACommutationCplt) for TIMEx_DMACommutationCplt
    stm32f4xx_hal_tim_ex.o(i.HAL_TIMEx_ConfigCommutEvent_DMA) refers to stm32f4xx_hal_tim_ex.o(i.TIMEx_DMACommutationHalfCplt) for TIMEx_DMACommutationHalfCplt
    stm32f4xx_hal_tim_ex.o(i.HAL_TIMEx_ConfigCommutEvent_DMA) refers to stm32f4xx_hal_tim.o(i.TIM_DMAError) for TIM_DMAError
    stm32f4xx_hal_tim_ex.o(i.HAL_TIMEx_HallSensor_DeInit) refers to stm32f4xx_hal_tim_ex.o(i.HAL_TIMEx_HallSensor_MspDeInit) for HAL_TIMEx_HallSensor_MspDeInit
    stm32f4xx_hal_tim_ex.o(i.HAL_TIMEx_HallSensor_Init) refers to stm32f4xx_hal_tim_ex.o(i.HAL_TIMEx_HallSensor_MspInit) for HAL_TIMEx_HallSensor_MspInit
    stm32f4xx_hal_tim_ex.o(i.HAL_TIMEx_HallSensor_Init) refers to stm32f4xx_hal_tim.o(i.TIM_Base_SetConfig) for TIM_Base_SetConfig
    stm32f4xx_hal_tim_ex.o(i.HAL_TIMEx_HallSensor_Init) refers to stm32f4xx_hal_tim.o(i.TIM_TI1_SetConfig) for TIM_TI1_SetConfig
    stm32f4xx_hal_tim_ex.o(i.HAL_TIMEx_HallSensor_Init) refers to stm32f4xx_hal_tim.o(i.TIM_OC2_SetConfig) for TIM_OC2_SetConfig
    stm32f4xx_hal_tim_ex.o(i.HAL_TIMEx_HallSensor_Start) refers to stm32f4xx_hal_tim.o(i.TIM_CCxChannelCmd) for TIM_CCxChannelCmd
    stm32f4xx_hal_tim_ex.o(i.HAL_TIMEx_HallSensor_Start_DMA) refers to stm32f4xx_hal_tim.o(i.TIM_CCxChannelCmd) for TIM_CCxChannelCmd
    stm32f4xx_hal_tim_ex.o(i.HAL_TIMEx_HallSensor_Start_DMA) refers to stm32f4xx_hal_dma.o(i.HAL_DMA_Start_IT) for HAL_DMA_Start_IT
    stm32f4xx_hal_tim_ex.o(i.HAL_TIMEx_HallSensor_Start_DMA) refers to stm32f4xx_hal_tim.o(i.TIM_DMACaptureCplt) for TIM_DMACaptureCplt
    stm32f4xx_hal_tim_ex.o(i.HAL_TIMEx_HallSensor_Start_DMA) refers to stm32f4xx_hal_tim.o(i.TIM_DMACaptureHalfCplt) for TIM_DMACaptureHalfCplt
    stm32f4xx_hal_tim_ex.o(i.HAL_TIMEx_HallSensor_Start_DMA) refers to stm32f4xx_hal_tim.o(i.TIM_DMAError) for TIM_DMAError
    stm32f4xx_hal_tim_ex.o(i.HAL_TIMEx_HallSensor_Start_IT) refers to stm32f4xx_hal_tim.o(i.TIM_CCxChannelCmd) for TIM_CCxChannelCmd
    stm32f4xx_hal_tim_ex.o(i.HAL_TIMEx_HallSensor_Stop) refers to stm32f4xx_hal_tim.o(i.TIM_CCxChannelCmd) for TIM_CCxChannelCmd
    stm32f4xx_hal_tim_ex.o(i.HAL_TIMEx_HallSensor_Stop_DMA) refers to stm32f4xx_hal_tim.o(i.TIM_CCxChannelCmd) for TIM_CCxChannelCmd
    stm32f4xx_hal_tim_ex.o(i.HAL_TIMEx_HallSensor_Stop_DMA) refers to stm32f4xx_hal_dma.o(i.HAL_DMA_Abort_IT) for HAL_DMA_Abort_IT
    stm32f4xx_hal_tim_ex.o(i.HAL_TIMEx_HallSensor_Stop_IT) refers to stm32f4xx_hal_tim.o(i.TIM_CCxChannelCmd) for TIM_CCxChannelCmd
    stm32f4xx_hal_tim_ex.o(i.HAL_TIMEx_OCN_Start) refers to stm32f4xx_hal_tim_ex.o(i.TIM_CCxNChannelCmd) for TIM_CCxNChannelCmd
    stm32f4xx_hal_tim_ex.o(i.HAL_TIMEx_OCN_Start_DMA) refers to stm32f4xx_hal_dma.o(i.HAL_DMA_Start_IT) for HAL_DMA_Start_IT
    stm32f4xx_hal_tim_ex.o(i.HAL_TIMEx_OCN_Start_DMA) refers to stm32f4xx_hal_tim_ex.o(i.TIM_CCxNChannelCmd) for TIM_CCxNChannelCmd
    stm32f4xx_hal_tim_ex.o(i.HAL_TIMEx_OCN_Start_DMA) refers to stm32f4xx_hal_tim_ex.o(i.TIM_DMADelayPulseNCplt) for TIM_DMADelayPulseNCplt
    stm32f4xx_hal_tim_ex.o(i.HAL_TIMEx_OCN_Start_DMA) refers to stm32f4xx_hal_tim.o(i.TIM_DMADelayPulseHalfCplt) for TIM_DMADelayPulseHalfCplt
    stm32f4xx_hal_tim_ex.o(i.HAL_TIMEx_OCN_Start_DMA) refers to stm32f4xx_hal_tim_ex.o(i.TIM_DMAErrorCCxN) for TIM_DMAErrorCCxN
    stm32f4xx_hal_tim_ex.o(i.HAL_TIMEx_OCN_Start_IT) refers to stm32f4xx_hal_tim_ex.o(i.TIM_CCxNChannelCmd) for TIM_CCxNChannelCmd
    stm32f4xx_hal_tim_ex.o(i.HAL_TIMEx_OCN_Stop) refers to stm32f4xx_hal_tim_ex.o(i.TIM_CCxNChannelCmd) for TIM_CCxNChannelCmd
    stm32f4xx_hal_tim_ex.o(i.HAL_TIMEx_OCN_Stop_DMA) refers to stm32f4xx_hal_dma.o(i.HAL_DMA_Abort_IT) for HAL_DMA_Abort_IT
    stm32f4xx_hal_tim_ex.o(i.HAL_TIMEx_OCN_Stop_DMA) refers to stm32f4xx_hal_tim_ex.o(i.TIM_CCxNChannelCmd) for TIM_CCxNChannelCmd
    stm32f4xx_hal_tim_ex.o(i.HAL_TIMEx_OCN_Stop_IT) refers to stm32f4xx_hal_tim_ex.o(i.TIM_CCxNChannelCmd) for TIM_CCxNChannelCmd
    stm32f4xx_hal_tim_ex.o(i.HAL_TIMEx_OnePulseN_Start) refers to stm32f4xx_hal_tim_ex.o(i.TIM_CCxNChannelCmd) for TIM_CCxNChannelCmd
    stm32f4xx_hal_tim_ex.o(i.HAL_TIMEx_OnePulseN_Start) refers to stm32f4xx_hal_tim.o(i.TIM_CCxChannelCmd) for TIM_CCxChannelCmd
    stm32f4xx_hal_tim_ex.o(i.HAL_TIMEx_OnePulseN_Start_IT) refers to stm32f4xx_hal_tim_ex.o(i.TIM_CCxNChannelCmd) for TIM_CCxNChannelCmd
    stm32f4xx_hal_tim_ex.o(i.HAL_TIMEx_OnePulseN_Start_IT) refers to stm32f4xx_hal_tim.o(i.TIM_CCxChannelCmd) for TIM_CCxChannelCmd
    stm32f4xx_hal_tim_ex.o(i.HAL_TIMEx_OnePulseN_Stop) refers to stm32f4xx_hal_tim_ex.o(i.TIM_CCxNChannelCmd) for TIM_CCxNChannelCmd
    stm32f4xx_hal_tim_ex.o(i.HAL_TIMEx_OnePulseN_Stop) refers to stm32f4xx_hal_tim.o(i.TIM_CCxChannelCmd) for TIM_CCxChannelCmd
    stm32f4xx_hal_tim_ex.o(i.HAL_TIMEx_OnePulseN_Stop_IT) refers to stm32f4xx_hal_tim_ex.o(i.TIM_CCxNChannelCmd) for TIM_CCxNChannelCmd
    stm32f4xx_hal_tim_ex.o(i.HAL_TIMEx_OnePulseN_Stop_IT) refers to stm32f4xx_hal_tim.o(i.TIM_CCxChannelCmd) for TIM_CCxChannelCmd
    stm32f4xx_hal_tim_ex.o(i.HAL_TIMEx_PWMN_Start) refers to stm32f4xx_hal_tim_ex.o(i.TIM_CCxNChannelCmd) for TIM_CCxNChannelCmd
    stm32f4xx_hal_tim_ex.o(i.HAL_TIMEx_PWMN_Start_DMA) refers to stm32f4xx_hal_dma.o(i.HAL_DMA_Start_IT) for HAL_DMA_Start_IT
    stm32f4xx_hal_tim_ex.o(i.HAL_TIMEx_PWMN_Start_DMA) refers to stm32f4xx_hal_tim_ex.o(i.TIM_CCxNChannelCmd) for TIM_CCxNChannelCmd
    stm32f4xx_hal_tim_ex.o(i.HAL_TIMEx_PWMN_Start_DMA) refers to stm32f4xx_hal_tim_ex.o(i.TIM_DMADelayPulseNCplt) for TIM_DMADelayPulseNCplt
    stm32f4xx_hal_tim_ex.o(i.HAL_TIMEx_PWMN_Start_DMA) refers to stm32f4xx_hal_tim.o(i.TIM_DMADelayPulseHalfCplt) for TIM_DMADelayPulseHalfCplt
    stm32f4xx_hal_tim_ex.o(i.HAL_TIMEx_PWMN_Start_DMA) refers to stm32f4xx_hal_tim_ex.o(i.TIM_DMAErrorCCxN) for TIM_DMAErrorCCxN
    stm32f4xx_hal_tim_ex.o(i.HAL_TIMEx_PWMN_Start_IT) refers to stm32f4xx_hal_tim_ex.o(i.TIM_CCxNChannelCmd) for TIM_CCxNChannelCmd
    stm32f4xx_hal_tim_ex.o(i.HAL_TIMEx_PWMN_Stop) refers to stm32f4xx_hal_tim_ex.o(i.TIM_CCxNChannelCmd) for TIM_CCxNChannelCmd
    stm32f4xx_hal_tim_ex.o(i.HAL_TIMEx_PWMN_Stop_DMA) refers to stm32f4xx_hal_dma.o(i.HAL_DMA_Abort_IT) for HAL_DMA_Abort_IT
    stm32f4xx_hal_tim_ex.o(i.HAL_TIMEx_PWMN_Stop_DMA) refers to stm32f4xx_hal_tim_ex.o(i.TIM_CCxNChannelCmd) for TIM_CCxNChannelCmd
    stm32f4xx_hal_tim_ex.o(i.HAL_TIMEx_PWMN_Stop_IT) refers to stm32f4xx_hal_tim_ex.o(i.TIM_CCxNChannelCmd) for TIM_CCxNChannelCmd
    stm32f4xx_hal_tim_ex.o(i.TIMEx_DMACommutationCplt) refers to stm32f4xx_hal_tim_ex.o(i.HAL_TIMEx_CommutCallback) for HAL_TIMEx_CommutCallback
    stm32f4xx_hal_tim_ex.o(i.TIMEx_DMACommutationHalfCplt) refers to stm32f4xx_hal_tim_ex.o(i.HAL_TIMEx_CommutHalfCpltCallback) for HAL_TIMEx_CommutHalfCpltCallback
    stm32f4xx_hal_tim_ex.o(i.TIM_DMADelayPulseNCplt) refers to stm32f4xx_hal_tim.o(i.HAL_TIM_PWM_PulseFinishedCallback) for HAL_TIM_PWM_PulseFinishedCallback
    stm32f4xx_hal_tim_ex.o(i.TIM_DMAErrorCCxN) refers to stm32f4xx_hal_tim.o(i.HAL_TIM_ErrorCallback) for HAL_TIM_ErrorCallback
    stm32f4xx_hal_uart.o(i.HAL_HalfDuplex_Init) refers to usart.o(i.HAL_UART_MspInit) for HAL_UART_MspInit
    stm32f4xx_hal_uart.o(i.HAL_HalfDuplex_Init) refers to stm32f4xx_hal_uart.o(i.UART_SetConfig) for UART_SetConfig
    stm32f4xx_hal_uart.o(i.HAL_LIN_Init) refers to usart.o(i.HAL_UART_MspInit) for HAL_UART_MspInit
    stm32f4xx_hal_uart.o(i.HAL_LIN_Init) refers to stm32f4xx_hal_uart.o(i.UART_SetConfig) for UART_SetConfig
    stm32f4xx_hal_uart.o(i.HAL_MultiProcessor_Init) refers to usart.o(i.HAL_UART_MspInit) for HAL_UART_MspInit
    stm32f4xx_hal_uart.o(i.HAL_MultiProcessor_Init) refers to stm32f4xx_hal_uart.o(i.UART_SetConfig) for UART_SetConfig
    stm32f4xx_hal_uart.o(i.HAL_UARTEx_ReceiveToIdle) refers to stm32f4xx_hal.o(i.HAL_GetTick) for HAL_GetTick
    stm32f4xx_hal_uart.o(i.HAL_UARTEx_ReceiveToIdle_DMA) refers to stm32f4xx_hal_uart.o(i.UART_Start_Receive_DMA) for UART_Start_Receive_DMA
    stm32f4xx_hal_uart.o(i.HAL_UARTEx_ReceiveToIdle_IT) refers to stm32f4xx_hal_uart.o(i.UART_Start_Receive_IT) for UART_Start_Receive_IT
    stm32f4xx_hal_uart.o(i.HAL_UART_Abort) refers to stm32f4xx_hal_dma.o(i.HAL_DMA_Abort) for HAL_DMA_Abort
    stm32f4xx_hal_uart.o(i.HAL_UART_Abort) refers to stm32f4xx_hal_dma.o(i.HAL_DMA_GetError) for HAL_DMA_GetError
    stm32f4xx_hal_uart.o(i.HAL_UART_AbortReceive) refers to stm32f4xx_hal_dma.o(i.HAL_DMA_Abort) for HAL_DMA_Abort
    stm32f4xx_hal_uart.o(i.HAL_UART_AbortReceive) refers to stm32f4xx_hal_dma.o(i.HAL_DMA_GetError) for HAL_DMA_GetError
    stm32f4xx_hal_uart.o(i.HAL_UART_AbortReceive_IT) refers to stm32f4xx_hal_dma.o(i.HAL_DMA_Abort_IT) for HAL_DMA_Abort_IT
    stm32f4xx_hal_uart.o(i.HAL_UART_AbortReceive_IT) refers to stm32f4xx_hal_uart.o(i.HAL_UART_AbortReceiveCpltCallback) for HAL_UART_AbortReceiveCpltCallback
    stm32f4xx_hal_uart.o(i.HAL_UART_AbortReceive_IT) refers to stm32f4xx_hal_uart.o(i.UART_DMARxOnlyAbortCallback) for UART_DMARxOnlyAbortCallback
    stm32f4xx_hal_uart.o(i.HAL_UART_AbortTransmit) refers to stm32f4xx_hal_dma.o(i.HAL_DMA_Abort) for HAL_DMA_Abort
    stm32f4xx_hal_uart.o(i.HAL_UART_AbortTransmit) refers to stm32f4xx_hal_dma.o(i.HAL_DMA_GetError) for HAL_DMA_GetError
    stm32f4xx_hal_uart.o(i.HAL_UART_AbortTransmit_IT) refers to stm32f4xx_hal_dma.o(i.HAL_DMA_Abort_IT) for HAL_DMA_Abort_IT
    stm32f4xx_hal_uart.o(i.HAL_UART_AbortTransmit_IT) refers to stm32f4xx_hal_uart.o(i.HAL_UART_AbortTransmitCpltCallback) for HAL_UART_AbortTransmitCpltCallback
    stm32f4xx_hal_uart.o(i.HAL_UART_AbortTransmit_IT) refers to stm32f4xx_hal_uart.o(i.UART_DMATxOnlyAbortCallback) for UART_DMATxOnlyAbortCallback
    stm32f4xx_hal_uart.o(i.HAL_UART_Abort_IT) refers to stm32f4xx_hal_dma.o(i.HAL_DMA_Abort_IT) for HAL_DMA_Abort_IT
    stm32f4xx_hal_uart.o(i.HAL_UART_Abort_IT) refers to stm32f4xx_hal_uart.o(i.HAL_UART_AbortCpltCallback) for HAL_UART_AbortCpltCallback
    stm32f4xx_hal_uart.o(i.HAL_UART_Abort_IT) refers to stm32f4xx_hal_uart.o(i.UART_DMATxAbortCallback) for UART_DMATxAbortCallback
    stm32f4xx_hal_uart.o(i.HAL_UART_Abort_IT) refers to stm32f4xx_hal_uart.o(i.UART_DMARxAbortCallback) for UART_DMARxAbortCallback
    stm32f4xx_hal_uart.o(i.HAL_UART_DMAStop) refers to stm32f4xx_hal_dma.o(i.HAL_DMA_Abort) for HAL_DMA_Abort
    stm32f4xx_hal_uart.o(i.HAL_UART_DMAStop) refers to stm32f4xx_hal_uart.o(i.UART_EndTxTransfer) for UART_EndTxTransfer
    stm32f4xx_hal_uart.o(i.HAL_UART_DMAStop) refers to stm32f4xx_hal_uart.o(i.UART_EndRxTransfer) for UART_EndRxTransfer
    stm32f4xx_hal_uart.o(i.HAL_UART_DeInit) refers to usart.o(i.HAL_UART_MspDeInit) for HAL_UART_MspDeInit
    stm32f4xx_hal_uart.o(i.HAL_UART_IRQHandler) refers to stm32f4xx_hal_uart.o(i.UART_Receive_IT) for UART_Receive_IT
    stm32f4xx_hal_uart.o(i.HAL_UART_IRQHandler) refers to stm32f4xx_hal_uart.o(i.UART_EndRxTransfer) for UART_EndRxTransfer
    stm32f4xx_hal_uart.o(i.HAL_UART_IRQHandler) refers to stm32f4xx_hal_dma.o(i.HAL_DMA_Abort_IT) for HAL_DMA_Abort_IT
    stm32f4xx_hal_uart.o(i.HAL_UART_IRQHandler) refers to stm32f4xx_hal_uart.o(i.HAL_UART_ErrorCallback) for HAL_UART_ErrorCallback
    stm32f4xx_hal_uart.o(i.HAL_UART_IRQHandler) refers to stm32f4xx_hal_dma.o(i.HAL_DMA_Abort) for HAL_DMA_Abort
    stm32f4xx_hal_uart.o(i.HAL_UART_IRQHandler) refers to stm32f4xx_hal_uart.o(i.HAL_UARTEx_RxEventCallback) for HAL_UARTEx_RxEventCallback
    stm32f4xx_hal_uart.o(i.HAL_UART_IRQHandler) refers to stm32f4xx_hal_uart.o(i.HAL_UART_TxCpltCallback) for HAL_UART_TxCpltCallback
    stm32f4xx_hal_uart.o(i.HAL_UART_IRQHandler) refers to stm32f4xx_hal_uart.o(i.UART_DMAAbortOnError) for UART_DMAAbortOnError
    stm32f4xx_hal_uart.o(i.HAL_UART_Init) refers to usart.o(i.HAL_UART_MspInit) for HAL_UART_MspInit
    stm32f4xx_hal_uart.o(i.HAL_UART_Init) refers to stm32f4xx_hal_uart.o(i.UART_SetConfig) for UART_SetConfig
    stm32f4xx_hal_uart.o(i.HAL_UART_Receive) refers to stm32f4xx_hal.o(i.HAL_GetTick) for HAL_GetTick
    stm32f4xx_hal_uart.o(i.HAL_UART_Receive) refers to stm32f4xx_hal_uart.o(i.UART_WaitOnFlagUntilTimeout) for UART_WaitOnFlagUntilTimeout
    stm32f4xx_hal_uart.o(i.HAL_UART_Receive_DMA) refers to stm32f4xx_hal_uart.o(i.UART_Start_Receive_DMA) for UART_Start_Receive_DMA
    stm32f4xx_hal_uart.o(i.HAL_UART_Receive_IT) refers to stm32f4xx_hal_uart.o(i.UART_Start_Receive_IT) for UART_Start_Receive_IT
    stm32f4xx_hal_uart.o(i.HAL_UART_Transmit) refers to stm32f4xx_hal.o(i.HAL_GetTick) for HAL_GetTick
    stm32f4xx_hal_uart.o(i.HAL_UART_Transmit) refers to stm32f4xx_hal_uart.o(i.UART_WaitOnFlagUntilTimeout) for UART_WaitOnFlagUntilTimeout
    stm32f4xx_hal_uart.o(i.HAL_UART_Transmit_DMA) refers to stm32f4xx_hal_dma.o(i.HAL_DMA_Start_IT) for HAL_DMA_Start_IT
    stm32f4xx_hal_uart.o(i.HAL_UART_Transmit_DMA) refers to stm32f4xx_hal_uart.o(i.UART_DMATransmitCplt) for UART_DMATransmitCplt
    stm32f4xx_hal_uart.o(i.HAL_UART_Transmit_DMA) refers to stm32f4xx_hal_uart.o(i.UART_DMATxHalfCplt) for UART_DMATxHalfCplt
    stm32f4xx_hal_uart.o(i.HAL_UART_Transmit_DMA) refers to stm32f4xx_hal_uart.o(i.UART_DMAError) for UART_DMAError
    stm32f4xx_hal_uart.o(i.UART_DMAAbortOnError) refers to stm32f4xx_hal_uart.o(i.HAL_UART_ErrorCallback) for HAL_UART_ErrorCallback
    stm32f4xx_hal_uart.o(i.UART_DMAError) refers to stm32f4xx_hal_uart.o(i.UART_EndTxTransfer) for UART_EndTxTransfer
    stm32f4xx_hal_uart.o(i.UART_DMAError) refers to stm32f4xx_hal_uart.o(i.UART_EndRxTransfer) for UART_EndRxTransfer
    stm32f4xx_hal_uart.o(i.UART_DMAError) refers to stm32f4xx_hal_uart.o(i.HAL_UART_ErrorCallback) for HAL_UART_ErrorCallback
    stm32f4xx_hal_uart.o(i.UART_DMAReceiveCplt) refers to stm32f4xx_hal_uart.o(i.HAL_UART_RxCpltCallback) for HAL_UART_RxCpltCallback
    stm32f4xx_hal_uart.o(i.UART_DMAReceiveCplt) refers to stm32f4xx_hal_uart.o(i.HAL_UARTEx_RxEventCallback) for HAL_UARTEx_RxEventCallback
    stm32f4xx_hal_uart.o(i.UART_DMARxAbortCallback) refers to stm32f4xx_hal_uart.o(i.HAL_UART_AbortCpltCallback) for HAL_UART_AbortCpltCallback
    stm32f4xx_hal_uart.o(i.UART_DMARxHalfCplt) refers to stm32f4xx_hal_uart.o(i.HAL_UART_RxHalfCpltCallback) for HAL_UART_RxHalfCpltCallback
    stm32f4xx_hal_uart.o(i.UART_DMARxHalfCplt) refers to stm32f4xx_hal_uart.o(i.HAL_UARTEx_RxEventCallback) for HAL_UARTEx_RxEventCallback
    stm32f4xx_hal_uart.o(i.UART_DMARxOnlyAbortCallback) refers to stm32f4xx_hal_uart.o(i.HAL_UART_AbortReceiveCpltCallback) for HAL_UART_AbortReceiveCpltCallback
    stm32f4xx_hal_uart.o(i.UART_DMATransmitCplt) refers to stm32f4xx_hal_uart.o(i.HAL_UART_TxCpltCallback) for HAL_UART_TxCpltCallback
    stm32f4xx_hal_uart.o(i.UART_DMATxAbortCallback) refers to stm32f4xx_hal_uart.o(i.HAL_UART_AbortCpltCallback) for HAL_UART_AbortCpltCallback
    stm32f4xx_hal_uart.o(i.UART_DMATxHalfCplt) refers to stm32f4xx_hal_uart.o(i.HAL_UART_TxHalfCpltCallback) for HAL_UART_TxHalfCpltCallback
    stm32f4xx_hal_uart.o(i.UART_DMATxOnlyAbortCallback) refers to stm32f4xx_hal_uart.o(i.HAL_UART_AbortTransmitCpltCallback) for HAL_UART_AbortTransmitCpltCallback
    stm32f4xx_hal_uart.o(i.UART_Receive_IT) refers to stm32f4xx_hal_uart.o(i.HAL_UART_RxCpltCallback) for HAL_UART_RxCpltCallback
    stm32f4xx_hal_uart.o(i.UART_Receive_IT) refers to stm32f4xx_hal_uart.o(i.HAL_UARTEx_RxEventCallback) for HAL_UARTEx_RxEventCallback
    stm32f4xx_hal_uart.o(i.UART_SetConfig) refers to stm32f4xx_hal_rcc.o(i.HAL_RCC_GetPCLK2Freq) for HAL_RCC_GetPCLK2Freq
    stm32f4xx_hal_uart.o(i.UART_SetConfig) refers to stm32f4xx_hal_rcc.o(i.HAL_RCC_GetPCLK1Freq) for HAL_RCC_GetPCLK1Freq
    stm32f4xx_hal_uart.o(i.UART_SetConfig) refers to uldiv.o(.text) for __aeabi_uldivmod
    stm32f4xx_hal_uart.o(i.UART_Start_Receive_DMA) refers to stm32f4xx_hal_dma.o(i.HAL_DMA_Start_IT) for HAL_DMA_Start_IT
    stm32f4xx_hal_uart.o(i.UART_Start_Receive_DMA) refers to stm32f4xx_hal_uart.o(i.UART_DMAReceiveCplt) for UART_DMAReceiveCplt
    stm32f4xx_hal_uart.o(i.UART_Start_Receive_DMA) refers to stm32f4xx_hal_uart.o(i.UART_DMARxHalfCplt) for UART_DMARxHalfCplt
    stm32f4xx_hal_uart.o(i.UART_Start_Receive_DMA) refers to stm32f4xx_hal_uart.o(i.UART_DMAError) for UART_DMAError
    stm32f4xx_hal_uart.o(i.UART_WaitOnFlagUntilTimeout) refers to stm32f4xx_hal.o(i.HAL_GetTick) for HAL_GetTick
    stm32f4xx_hal_uart.o(i.UART_WaitOnFlagUntilTimeout) refers to stm32f4xx_hal_uart.o(i.UART_EndRxTransfer) for UART_EndRxTransfer
    system_stm32f4xx.o(i.SystemCoreClockUpdate) refers to system_stm32f4xx.o(.data) for .data
    system_stm32f4xx.o(i.SystemCoreClockUpdate) refers to system_stm32f4xx.o(.constdata) for .constdata
    ad9959_new.o(i.AD9959FreqSweep) refers to ad9959_new.o(i.CalculateFreq) for CalculateFreq
    ad9959_new.o(i.AD9959FreqSweep) refers to ad9959_new.o(i.CalculateStayTime) for CalculateStayTime
    ad9959_new.o(i.AD9959FreqSweep) refers to ad9959_new.o(i.WritePhase) for WritePhase
    ad9959_new.o(i.AD9959FreqSweep) refers to ad9959_new.o(i.IO_Update) for IO_Update
    ad9959_new.o(i.AD9959FreqSweep) refers to ad9959_new.o(i.WriteData_AD9959) for WriteData_AD9959
    ad9959_new.o(i.AD9959FreqSweep) refers to ad9959_new.o(i.ReadData_AD9959) for ReadData_AD9959
    ad9959_new.o(i.AD9959FreqSweep) refers to stm32f4xx_hal_gpio.o(i.HAL_GPIO_WritePin) for HAL_GPIO_WritePin
    ad9959_new.o(i.AD9959FreqSweep) refers to ad9959_new.o(i.delay1) for delay1
    ad9959_new.o(i.AD9959MSGInit) refers to ad9959_new.o(.bss) for .bss
    ad9959_new.o(i.CalculateFreq) refers to dfltui.o(.text) for __aeabi_ui2d
    ad9959_new.o(i.CalculateFreq) refers to dmul.o(.text) for __aeabi_dmul
    ad9959_new.o(i.CalculateFreq) refers to dfixui.o(.text) for __aeabi_d2uiz
    ad9959_new.o(i.CalculatePhase) refers to dfixui.o(.text) for __aeabi_d2uiz
    ad9959_new.o(i.CalculateStayTime) refers to cdrcmple.o(.text) for __aeabi_cdrcmple
    ad9959_new.o(i.CalculateStayTime) refers to cdcmple.o(.text) for __aeabi_cdcmple
    ad9959_new.o(i.CalculateStayTime) refers to dmul.o(.text) for __aeabi_dmul
    ad9959_new.o(i.CalculateStayTime) refers to dfixui.o(.text) for __aeabi_d2uiz
    ad9959_new.o(i.Channel_Select) refers to stm32f4xx_hal_gpio.o(i.HAL_GPIO_WritePin) for HAL_GPIO_WritePin
    ad9959_new.o(i.IO_Update) refers to stm32f4xx_hal_gpio.o(i.HAL_GPIO_WritePin) for HAL_GPIO_WritePin
    ad9959_new.o(i.IO_Update) refers to ad9959_new.o(i.delay1) for delay1
    ad9959_new.o(i.Init_AD9959) refers to ad9959_new.o(i.AD9959MSGInit) for AD9959MSGInit
    ad9959_new.o(i.Init_AD9959) refers to memseta.o(.text) for __aeabi_memclr4
    ad9959_new.o(i.Init_AD9959) refers to stm32f4xx_hal_gpio.o(i.HAL_GPIO_Init) for HAL_GPIO_Init
    ad9959_new.o(i.Init_AD9959) refers to ad9959_new.o(i.Intserve) for Intserve
    ad9959_new.o(i.Init_AD9959) refers to ad9959_new.o(i.IntReset) for IntReset
    ad9959_new.o(i.Init_AD9959) refers to ad9959_new.o(i.WriteData_AD9959) for WriteData_AD9959
    ad9959_new.o(i.Init_AD9959) refers to ad9959_new.o(i.WriteAmplitude) for WriteAmplitude
    ad9959_new.o(i.Init_AD9959) refers to ad9959_new.o(i.WriteFreq) for WriteFreq
    ad9959_new.o(i.Init_AD9959) refers to ad9959_new.o(.data) for .data
    ad9959_new.o(i.Init_AD9959) refers to ad9959_new.o(.bss) for .bss
    ad9959_new.o(i.IntReset) refers to stm32f4xx_hal_gpio.o(i.HAL_GPIO_WritePin) for HAL_GPIO_WritePin
    ad9959_new.o(i.IntReset) refers to ad9959_new.o(i.delay1) for delay1
    ad9959_new.o(i.Intserve) refers to stm32f4xx_hal_gpio.o(i.HAL_GPIO_WritePin) for HAL_GPIO_WritePin
    ad9959_new.o(i.Phase_2_AD9959) refers to fmodf.o(i.__hardfp_fmodf) for __hardfp_fmodf
    ad9959_new.o(i.Phase_2_AD9959) refers to f2d.o(.text) for __aeabi_f2d
    ad9959_new.o(i.Phase_2_AD9959) refers to dmul.o(.text) for __aeabi_dmul
    ad9959_new.o(i.Phase_2_AD9959) refers to ddiv.o(.text) for __aeabi_ddiv
    ad9959_new.o(i.Phase_2_AD9959) refers to dfixui.o(.text) for __aeabi_d2uiz
    ad9959_new.o(i.ReadData_AD9959) refers to memseta.o(.text) for __aeabi_memclr4
    ad9959_new.o(i.ReadData_AD9959) refers to stm32f4xx_hal_gpio.o(i.HAL_GPIO_Init) for HAL_GPIO_Init
    ad9959_new.o(i.ReadData_AD9959) refers to stm32f4xx_hal_gpio.o(i.HAL_GPIO_WritePin) for HAL_GPIO_WritePin
    ad9959_new.o(i.ReadData_AD9959) refers to stm32f4xx_hal_gpio.o(i.HAL_GPIO_ReadPin) for HAL_GPIO_ReadPin
    ad9959_new.o(i.SweepFrequency) refers to ad9959_new.o(i.Channel_Select) for Channel_Select
    ad9959_new.o(i.SweepFrequency) refers to ad9959_new.o(i.ReadData_AD9959) for ReadData_AD9959
    ad9959_new.o(i.SweepFrequency) refers to ad9959_new.o(i.CalculateFreq) for CalculateFreq
    ad9959_new.o(i.SweepFrequency) refers to dfltui.o(.text) for __aeabi_ui2d
    ad9959_new.o(i.SweepFrequency) refers to ad9959_new.o(i.CalculateStayTime) for CalculateStayTime
    ad9959_new.o(i.SweepFrequency) refers to ad9959_new.o(i.WriteData_AD9959) for WriteData_AD9959
    ad9959_new.o(i.SweepFrequency) refers to ad9959_new.o(i.IO_Update) for IO_Update
    ad9959_new.o(i.SweepFrequency) refers to stm32f4xx_hal_gpio.o(i.HAL_GPIO_WritePin) for HAL_GPIO_WritePin
    ad9959_new.o(i.SweepFrequency) refers to ad9959_new.o(i.delay1) for delay1
    ad9959_new.o(i.WriteAmplitude) refers to ad9959_new.o(i.WriteData_AD9959) for WriteData_AD9959
    ad9959_new.o(i.WriteAmplitude) refers to ad9959_new.o(.bss) for .bss
    ad9959_new.o(i.WriteAmplitude) refers to ad9959_new.o(.data) for .data
    ad9959_new.o(i.WriteData_AD9959) refers to stm32f4xx_hal_gpio.o(i.HAL_GPIO_WritePin) for HAL_GPIO_WritePin
    ad9959_new.o(i.WriteData_AD9959) refers to ad9959_new.o(i.IO_Update) for IO_Update
    ad9959_new.o(i.WriteFreq) refers to dfltui.o(.text) for __aeabi_ui2d
    ad9959_new.o(i.WriteFreq) refers to dmul.o(.text) for __aeabi_dmul
    ad9959_new.o(i.WriteFreq) refers to dfixui.o(.text) for __aeabi_d2uiz
    ad9959_new.o(i.WriteFreq) refers to ad9959_new.o(i.WriteData_AD9959) for WriteData_AD9959
    ad9959_new.o(i.WriteFreq) refers to ad9959_new.o(.bss) for .bss
    ad9959_new.o(i.WriteFreq) refers to ad9959_new.o(.data) for .data
    ad9959_new.o(i.WriteFreqOrtho) refers to uldiv.o(.text) for __aeabi_uldivmod
    ad9959_new.o(i.WriteFreqOrtho) refers to dfltui.o(.text) for __aeabi_ui2d
    ad9959_new.o(i.WriteFreqOrtho) refers to dmul.o(.text) for __aeabi_dmul
    ad9959_new.o(i.WriteFreqOrtho) refers to dfixui.o(.text) for __aeabi_d2uiz
    ad9959_new.o(i.WriteFreqOrtho) refers to ad9959_new.o(i.WritePhase) for WritePhase
    ad9959_new.o(i.WriteFreqOrtho) refers to ad9959_new.o(i.WriteData_AD9959) for WriteData_AD9959
    ad9959_new.o(i.WriteFreqOrtho) refers to ad9959_new.o(i.IO_Update) for IO_Update
    ad9959_new.o(i.WriteFreqOrtho) refers to ad9959_new.o(.bss) for .bss
    ad9959_new.o(i.WritePhase) refers to ad9959_new.o(i.WriteData_AD9959) for WriteData_AD9959
    ad9959_new.o(i.WritePhase) refers to ad9959_new.o(.data) for .data
    ad9959_new.o(i.WritePhase) refers to ad9959_new.o(.bss) for .bss
    ad9959_new.o(i.Write_Amplitude) refers to ad9959_new.o(i.WriteData_AD9959) for WriteData_AD9959
    ad9959_new.o(i.Write_Amplitude) refers to ad9959_new.o(.bss) for .bss
    ad9959_new.o(i.Write_Amplitude) refers to ad9959_new.o(.data) for .data
    ad9959_new.o(i.Write_Phase) refers to ad9959_new.o(i.WriteData_AD9959) for WriteData_AD9959
    ad9959_new.o(i.Write_Phase) refers to ad9959_new.o(.data) for .data
    ad9959_new.o(i.Write_Phase) refers to ad9959_new.o(.bss) for .bss
    ad9959_new.o(i.Write_frequence) refers to dfltui.o(.text) for __aeabi_ui2d
    ad9959_new.o(i.Write_frequence) refers to dmul.o(.text) for __aeabi_dmul
    ad9959_new.o(i.Write_frequence) refers to dfixui.o(.text) for __aeabi_d2uiz
    ad9959_new.o(i.Write_frequence) refers to ad9959_new.o(i.WriteData_AD9959) for WriteData_AD9959
    ad9959_new.o(i.Write_frequence) refers to ad9959_new.o(.bss) for .bss
    ad9959_new.o(i.Write_frequence) refers to ad9959_new.o(.data) for .data
    fft_processing.o(i.FFT_DetectFundamentalFrequency) refers to fft_processing.o(i.calculateThreshold) for calculateThreshold
    fft_processing.o(i.FFT_DetectFundamentalFrequency) refers to fft_processing.o(i.parabolicInterpolation) for parabolicInterpolation
    fft_processing.o(i.FFT_DetectHarmonic) refers to expf.o(i.__hardfp_expf) for __hardfp_expf
    fft_processing.o(i.FFT_InitWindow) refers to cosf.o(i.__hardfp_cosf) for __hardfp_cosf
    fft_processing.o(i.FFT_Process) refers to transformfunctions.o(i.arm_cfft_f32) for arm_cfft_f32
    fft_processing.o(i.FFT_Process) refers to complexmathfunctions.o(i.arm_cmplx_mag_f32) for arm_cmplx_mag_f32
    fft_processing.o(i.FFT_Process) refers to commontables.o(.constdata) for arm_cfft_sR_f32_len1024
    fft_processing.o(i.calculateThreshold) refers to sqrtf.o(i.__hardfp_sqrtf) for __hardfp_sqrtf
    9833.o(i.AD9833_AdjustPhase) refers to fmodf.o(i.__hardfp_fmodf) for __hardfp_fmodf
    9833.o(i.AD9833_AdjustPhase) refers to stm32f4xx_hal_gpio.o(i.HAL_GPIO_WritePin) for HAL_GPIO_WritePin
    9833.o(i.AD9833_AdjustPhase) refers to 9833.o(i.writeSPI) for writeSPI
    9833.o(i.AD9833_AdjustPhase) refers to 9833.o(i.AD9833_SetWave) for AD9833_SetWave
    9833.o(i.AD9833_AdjustPhase) refers to 9833.o(.data) for .data
    9833.o(i.AD9833_Init) refers to stm32f4xx_hal_gpio.o(i.HAL_GPIO_WritePin) for HAL_GPIO_WritePin
    9833.o(i.AD9833_Init) refers to 9833.o(i.AD9833_SetWave) for AD9833_SetWave
    9833.o(i.AD9833_Init) refers to 9833.o(i.AD9833_SetWaveData) for AD9833_SetWaveData
    9833.o(i.AD9833_SetAbsolutePhase) refers to stm32f4xx_hal_gpio.o(i.HAL_GPIO_WritePin) for HAL_GPIO_WritePin
    9833.o(i.AD9833_SetAbsolutePhase) refers to stm32f4xx_hal.o(i.HAL_Delay) for HAL_Delay
    9833.o(i.AD9833_SetAbsolutePhase) refers to 9833.o(i.writeSPI) for writeSPI
    9833.o(i.AD9833_SetAbsolutePhase) refers to 9833.o(i.AD9833_SetWave) for AD9833_SetWave
    9833.o(i.AD9833_SetAbsolutePhase) refers to 9833.o(.data) for .data
    9833.o(i.AD9833_SetWave) refers to stm32f4xx_hal_gpio.o(i.HAL_GPIO_WritePin) for HAL_GPIO_WritePin
    9833.o(i.AD9833_SetWave) refers to 9833.o(i.writeSPI) for writeSPI
    9833.o(i.AD9833_SetWave) refers to 9833.o(.data) for .data
    9833.o(i.AD9833_SetWaveData) refers to pow.o(i.__hardfp_pow) for __hardfp_pow
    9833.o(i.AD9833_SetWaveData) refers to f2d.o(.text) for __aeabi_f2d
    9833.o(i.AD9833_SetWaveData) refers to dmul.o(.text) for __aeabi_dmul
    9833.o(i.AD9833_SetWaveData) refers to ddiv.o(.text) for __aeabi_ddiv
    9833.o(i.AD9833_SetWaveData) refers to dadd.o(.text) for __aeabi_dadd
    9833.o(i.AD9833_SetWaveData) refers to dfixi.o(.text) for __aeabi_d2iz
    9833.o(i.AD9833_SetWaveData) refers to stm32f4xx_hal_gpio.o(i.HAL_GPIO_WritePin) for HAL_GPIO_WritePin
    9833.o(i.AD9833_SetWaveData) refers to 9833.o(i.writeSPI) for writeSPI
    9833.o(i.AD9833_SetWaveData) refers to 9833.o(i.AD9833_SetWave) for AD9833_SetWave
    9833.o(i.AD9833_SetWaveData) refers to 9833.o(.data) for .data
    9833.o(i.writeSPI) refers to stm32f4xx_hal_gpio.o(i.HAL_GPIO_WritePin) for HAL_GPIO_WritePin
    tm1637.o(i.DisplayCO2ppm) refers to tm1637.o(i.TM1637_WRITE_DISPLAY_BYTE_FIX_ADDRESS) for TM1637_WRITE_DISPLAY_BYTE_FIX_ADDRESS
    tm1637.o(i.TM1637_CHECK_ack) refers to stm32f4xx_hal_gpio.o(i.HAL_GPIO_WritePin) for HAL_GPIO_WritePin
    tm1637.o(i.TM1637_CHECK_ack) refers to tm1637.o(i.delay_us) for delay_us
    tm1637.o(i.TM1637_CHECK_ack) refers to stm32f4xx_hal_gpio.o(i.HAL_GPIO_ReadPin) for HAL_GPIO_ReadPin
    tm1637.o(i.TM1637_CHECK_ack) refers to tm1637.o(.data) for .data
    tm1637.o(i.TM1637_ClearAll) refers to tm1637.o(i.TM1637_START) for TM1637_START
    tm1637.o(i.TM1637_ClearAll) refers to tm1637.o(i.TM1637_WRITE_BYTE_DATA) for TM1637_WRITE_BYTE_DATA
    tm1637.o(i.TM1637_ClearAll) refers to tm1637.o(i.TM1637_CHECK_ack) for TM1637_CHECK_ack
    tm1637.o(i.TM1637_ClearAll) refers to tm1637.o(i.TM1637_STOP) for TM1637_STOP
    tm1637.o(i.TM1637_START) refers to stm32f4xx_hal_gpio.o(i.HAL_GPIO_WritePin) for HAL_GPIO_WritePin
    tm1637.o(i.TM1637_START) refers to tm1637.o(i.delay_us) for delay_us
    tm1637.o(i.TM1637_START) refers to tm1637.o(.data) for .data
    tm1637.o(i.TM1637_STOP) refers to stm32f4xx_hal_gpio.o(i.HAL_GPIO_WritePin) for HAL_GPIO_WritePin
    tm1637.o(i.TM1637_STOP) refers to tm1637.o(i.delay_us) for delay_us
    tm1637.o(i.TM1637_STOP) refers to tm1637.o(.data) for .data
    tm1637.o(i.TM1637_WRITE_BYTE_DATA) refers to stm32f4xx_hal_gpio.o(i.HAL_GPIO_WritePin) for HAL_GPIO_WritePin
    tm1637.o(i.TM1637_WRITE_BYTE_DATA) refers to tm1637.o(i.delay_us) for delay_us
    tm1637.o(i.TM1637_WRITE_BYTE_DATA) refers to tm1637.o(.data) for .data
    tm1637.o(i.TM1637_WRITE_DISPLAY_BYTE_FIX_ADDRESS) refers to tm1637.o(i.TM1637_START) for TM1637_START
    tm1637.o(i.TM1637_WRITE_DISPLAY_BYTE_FIX_ADDRESS) refers to tm1637.o(i.TM1637_WRITE_BYTE_DATA) for TM1637_WRITE_BYTE_DATA
    tm1637.o(i.TM1637_WRITE_DISPLAY_BYTE_FIX_ADDRESS) refers to tm1637.o(i.TM1637_CHECK_ack) for TM1637_CHECK_ack
    tm1637.o(i.TM1637_WRITE_DISPLAY_BYTE_FIX_ADDRESS) refers to tm1637.o(i.TM1637_STOP) for TM1637_STOP
    tm1637.o(i.TM1637_WRITE_DISPLAY_BYTE_FIX_ADDRESS) refers to tm1637.o(.constdata) for .constdata
    tm1637.o(i.delay_us) refers to stm32f4xx_hal_tim.o(i.HAL_TIM_Base_Start) for HAL_TIM_Base_Start
    tm1637.o(i.delay_us) refers to stm32f4xx_hal_tim.o(i.HAL_TIM_Base_Stop) for HAL_TIM_Base_Stop
    tm1637.o(i.delay_us) refers to tim.o(.bss) for htim4
    basicmathfunctions.o(i.arm_abs_f64) refers to fabs.o(i.__hardfp_fabs) for __hardfp_fabs
    basicmathfunctions.o(i.arm_add_f64) refers to dadd.o(.text) for __aeabi_dadd
    basicmathfunctions.o(i.arm_dot_prod_f64) refers to dmul.o(.text) for __aeabi_dmul
    basicmathfunctions.o(i.arm_dot_prod_f64) refers to dadd.o(.text) for __aeabi_dadd
    basicmathfunctions.o(i.arm_mult_f64) refers to dmul.o(.text) for __aeabi_dmul
    basicmathfunctions.o(i.arm_offset_f64) refers to dadd.o(.text) for __aeabi_dadd
    basicmathfunctions.o(i.arm_scale_f64) refers to dmul.o(.text) for __aeabi_dmul
    basicmathfunctions.o(i.arm_shift_q31) refers to llshl.o(.text) for __aeabi_llsl
    basicmathfunctions.o(i.arm_sub_f64) refers to dadd.o(.text) for __aeabi_dsub
    bayesfunctions.o(i.arm_gaussian_naive_bayes_predict_f32) refers to logf.o(i.__hardfp_logf) for __hardfp_logf
    bayesfunctions.o(i.arm_gaussian_naive_bayes_predict_f32) refers to statisticsfunctions.o(i.arm_max_f32) for arm_max_f32
    commontables.o(.constdata) refers to commontables.o(.constdata) for twiddleCoefF64_16
    commontables.o(.constdata) refers to commontables.o(.constdata) for armBitRevIndexTableF64_16
    commontables.o(.constdata) refers to commontables.o(.constdata) for twiddleCoefF64_32
    commontables.o(.constdata) refers to commontables.o(.constdata) for armBitRevIndexTableF64_32
    commontables.o(.constdata) refers to commontables.o(.constdata) for twiddleCoefF64_64
    commontables.o(.constdata) refers to commontables.o(.constdata) for armBitRevIndexTableF64_64
    commontables.o(.constdata) refers to commontables.o(.constdata) for twiddleCoefF64_128
    commontables.o(.constdata) refers to commontables.o(.constdata) for armBitRevIndexTableF64_128
    commontables.o(.constdata) refers to commontables.o(.constdata) for twiddleCoefF64_256
    commontables.o(.constdata) refers to commontables.o(.constdata) for armBitRevIndexTableF64_256
    commontables.o(.constdata) refers to commontables.o(.constdata) for twiddleCoefF64_512
    commontables.o(.constdata) refers to commontables.o(.constdata) for armBitRevIndexTableF64_512
    commontables.o(.constdata) refers to commontables.o(.constdata) for twiddleCoefF64_1024
    commontables.o(.constdata) refers to commontables.o(.constdata) for armBitRevIndexTableF64_1024
    commontables.o(.constdata) refers to commontables.o(.constdata) for twiddleCoefF64_2048
    commontables.o(.constdata) refers to commontables.o(.constdata) for armBitRevIndexTableF64_2048
    commontables.o(.constdata) refers to commontables.o(.constdata) for twiddleCoefF64_4096
    commontables.o(.constdata) refers to commontables.o(.constdata) for armBitRevIndexTableF64_4096
    commontables.o(.constdata) refers to commontables.o(.constdata) for twiddleCoef_16
    commontables.o(.constdata) refers to commontables.o(.constdata) for armBitRevIndexTable16
    commontables.o(.constdata) refers to commontables.o(.constdata) for twiddleCoef_32
    commontables.o(.constdata) refers to commontables.o(.constdata) for armBitRevIndexTable32
    commontables.o(.constdata) refers to commontables.o(.constdata) for twiddleCoef_64
    commontables.o(.constdata) refers to commontables.o(.constdata) for armBitRevIndexTable64
    commontables.o(.constdata) refers to commontables.o(.constdata) for twiddleCoef_128
    commontables.o(.constdata) refers to commontables.o(.constdata) for armBitRevIndexTable128
    commontables.o(.constdata) refers to commontables.o(.constdata) for twiddleCoef_256
    commontables.o(.constdata) refers to commontables.o(.constdata) for armBitRevIndexTable256
    commontables.o(.constdata) refers to commontables.o(.constdata) for twiddleCoef_512
    commontables.o(.constdata) refers to commontables.o(.constdata) for armBitRevIndexTable512
    commontables.o(.constdata) refers to commontables.o(.constdata) for twiddleCoef_1024
    commontables.o(.constdata) refers to commontables.o(.constdata) for armBitRevIndexTable1024
    commontables.o(.constdata) refers to commontables.o(.constdata) for twiddleCoef_2048
    commontables.o(.constdata) refers to commontables.o(.constdata) for armBitRevIndexTable2048
    commontables.o(.constdata) refers to commontables.o(.constdata) for twiddleCoef_4096
    commontables.o(.constdata) refers to commontables.o(.constdata) for armBitRevIndexTable4096
    commontables.o(.constdata) refers to commontables.o(.constdata) for twiddleCoef_16_q31
    commontables.o(.constdata) refers to commontables.o(.constdata) for armBitRevIndexTable_fixed_16
    commontables.o(.constdata) refers to commontables.o(.constdata) for twiddleCoef_32_q31
    commontables.o(.constdata) refers to commontables.o(.constdata) for armBitRevIndexTable_fixed_32
    commontables.o(.constdata) refers to commontables.o(.constdata) for twiddleCoef_64_q31
    commontables.o(.constdata) refers to commontables.o(.constdata) for armBitRevIndexTable_fixed_64
    commontables.o(.constdata) refers to commontables.o(.constdata) for twiddleCoef_128_q31
    commontables.o(.constdata) refers to commontables.o(.constdata) for armBitRevIndexTable_fixed_128
    commontables.o(.constdata) refers to commontables.o(.constdata) for twiddleCoef_256_q31
    commontables.o(.constdata) refers to commontables.o(.constdata) for armBitRevIndexTable_fixed_256
    commontables.o(.constdata) refers to commontables.o(.constdata) for twiddleCoef_512_q31
    commontables.o(.constdata) refers to commontables.o(.constdata) for armBitRevIndexTable_fixed_512
    commontables.o(.constdata) refers to commontables.o(.constdata) for twiddleCoef_1024_q31
    commontables.o(.constdata) refers to commontables.o(.constdata) for armBitRevIndexTable_fixed_1024
    commontables.o(.constdata) refers to commontables.o(.constdata) for twiddleCoef_2048_q31
    commontables.o(.constdata) refers to commontables.o(.constdata) for armBitRevIndexTable_fixed_2048
    commontables.o(.constdata) refers to commontables.o(.constdata) for twiddleCoef_4096_q31
    commontables.o(.constdata) refers to commontables.o(.constdata) for armBitRevIndexTable_fixed_4096
    commontables.o(.constdata) refers to commontables.o(.constdata) for twiddleCoef_16_q15
    commontables.o(.constdata) refers to commontables.o(.constdata) for armBitRevIndexTable_fixed_16
    commontables.o(.constdata) refers to commontables.o(.constdata) for twiddleCoef_32_q15
    commontables.o(.constdata) refers to commontables.o(.constdata) for armBitRevIndexTable_fixed_32
    commontables.o(.constdata) refers to commontables.o(.constdata) for twiddleCoef_64_q15
    commontables.o(.constdata) refers to commontables.o(.constdata) for armBitRevIndexTable_fixed_64
    commontables.o(.constdata) refers to commontables.o(.constdata) for twiddleCoef_128_q15
    commontables.o(.constdata) refers to commontables.o(.constdata) for armBitRevIndexTable_fixed_128
    commontables.o(.constdata) refers to commontables.o(.constdata) for twiddleCoef_256_q15
    commontables.o(.constdata) refers to commontables.o(.constdata) for armBitRevIndexTable_fixed_256
    commontables.o(.constdata) refers to commontables.o(.constdata) for twiddleCoef_512_q15
    commontables.o(.constdata) refers to commontables.o(.constdata) for armBitRevIndexTable_fixed_512
    commontables.o(.constdata) refers to commontables.o(.constdata) for twiddleCoef_1024_q15
    commontables.o(.constdata) refers to commontables.o(.constdata) for armBitRevIndexTable_fixed_1024
    commontables.o(.constdata) refers to commontables.o(.constdata) for twiddleCoef_2048_q15
    commontables.o(.constdata) refers to commontables.o(.constdata) for armBitRevIndexTable_fixed_2048
    commontables.o(.constdata) refers to commontables.o(.constdata) for twiddleCoef_4096_q15
    commontables.o(.constdata) refers to commontables.o(.constdata) for armBitRevIndexTable_fixed_4096
    commontables.o(.constdata) refers to commontables.o(.constdata) for twiddleCoefF64_16
    commontables.o(.constdata) refers to commontables.o(.constdata) for armBitRevIndexTableF64_16
    commontables.o(.constdata) refers to commontables.o(.constdata) for twiddleCoefF64_rfft_32
    commontables.o(.constdata) refers to commontables.o(.constdata) for twiddleCoefF64_32
    commontables.o(.constdata) refers to commontables.o(.constdata) for armBitRevIndexTableF64_32
    commontables.o(.constdata) refers to commontables.o(.constdata) for twiddleCoefF64_rfft_64
    commontables.o(.constdata) refers to commontables.o(.constdata) for twiddleCoefF64_64
    commontables.o(.constdata) refers to commontables.o(.constdata) for armBitRevIndexTableF64_64
    commontables.o(.constdata) refers to commontables.o(.constdata) for twiddleCoefF64_rfft_128
    commontables.o(.constdata) refers to commontables.o(.constdata) for twiddleCoefF64_128
    commontables.o(.constdata) refers to commontables.o(.constdata) for armBitRevIndexTableF64_128
    commontables.o(.constdata) refers to commontables.o(.constdata) for twiddleCoefF64_rfft_256
    commontables.o(.constdata) refers to commontables.o(.constdata) for twiddleCoefF64_256
    commontables.o(.constdata) refers to commontables.o(.constdata) for armBitRevIndexTableF64_256
    commontables.o(.constdata) refers to commontables.o(.constdata) for twiddleCoefF64_rfft_512
    commontables.o(.constdata) refers to commontables.o(.constdata) for twiddleCoefF64_512
    commontables.o(.constdata) refers to commontables.o(.constdata) for armBitRevIndexTableF64_512
    commontables.o(.constdata) refers to commontables.o(.constdata) for twiddleCoefF64_rfft_1024
    commontables.o(.constdata) refers to commontables.o(.constdata) for twiddleCoefF64_1024
    commontables.o(.constdata) refers to commontables.o(.constdata) for armBitRevIndexTableF64_1024
    commontables.o(.constdata) refers to commontables.o(.constdata) for twiddleCoefF64_rfft_2048
    commontables.o(.constdata) refers to commontables.o(.constdata) for twiddleCoefF64_2048
    commontables.o(.constdata) refers to commontables.o(.constdata) for armBitRevIndexTableF64_2048
    commontables.o(.constdata) refers to commontables.o(.constdata) for twiddleCoefF64_rfft_4096
    commontables.o(.constdata) refers to commontables.o(.constdata) for twiddleCoef_16
    commontables.o(.constdata) refers to commontables.o(.constdata) for armBitRevIndexTable16
    commontables.o(.constdata) refers to commontables.o(.constdata) for twiddleCoef_rfft_32
    commontables.o(.constdata) refers to commontables.o(.constdata) for twiddleCoef_32
    commontables.o(.constdata) refers to commontables.o(.constdata) for armBitRevIndexTable32
    commontables.o(.constdata) refers to commontables.o(.constdata) for twiddleCoef_rfft_64
    commontables.o(.constdata) refers to commontables.o(.constdata) for twiddleCoef_64
    commontables.o(.constdata) refers to commontables.o(.constdata) for armBitRevIndexTable64
    commontables.o(.constdata) refers to commontables.o(.constdata) for twiddleCoef_rfft_128
    commontables.o(.constdata) refers to commontables.o(.constdata) for twiddleCoef_128
    commontables.o(.constdata) refers to commontables.o(.constdata) for armBitRevIndexTable128
    commontables.o(.constdata) refers to commontables.o(.constdata) for twiddleCoef_rfft_256
    commontables.o(.constdata) refers to commontables.o(.constdata) for twiddleCoef_256
    commontables.o(.constdata) refers to commontables.o(.constdata) for armBitRevIndexTable256
    commontables.o(.constdata) refers to commontables.o(.constdata) for twiddleCoef_rfft_512
    commontables.o(.constdata) refers to commontables.o(.constdata) for twiddleCoef_512
    commontables.o(.constdata) refers to commontables.o(.constdata) for armBitRevIndexTable512
    commontables.o(.constdata) refers to commontables.o(.constdata) for twiddleCoef_rfft_1024
    commontables.o(.constdata) refers to commontables.o(.constdata) for twiddleCoef_1024
    commontables.o(.constdata) refers to commontables.o(.constdata) for armBitRevIndexTable1024
    commontables.o(.constdata) refers to commontables.o(.constdata) for twiddleCoef_rfft_2048
    commontables.o(.constdata) refers to commontables.o(.constdata) for twiddleCoef_2048
    commontables.o(.constdata) refers to commontables.o(.constdata) for armBitRevIndexTable2048
    commontables.o(.constdata) refers to commontables.o(.constdata) for twiddleCoef_rfft_4096
    commontables.o(.constdata) refers to commontables.o(.constdata) for realCoefAQ31
    commontables.o(.constdata) refers to commontables.o(.constdata) for realCoefBQ31
    commontables.o(.constdata) refers to commontables.o(.constdata) for arm_cfft_sR_q31_len16
    commontables.o(.constdata) refers to commontables.o(.constdata) for realCoefAQ31
    commontables.o(.constdata) refers to commontables.o(.constdata) for realCoefBQ31
    commontables.o(.constdata) refers to commontables.o(.constdata) for arm_cfft_sR_q31_len32
    commontables.o(.constdata) refers to commontables.o(.constdata) for realCoefAQ31
    commontables.o(.constdata) refers to commontables.o(.constdata) for realCoefBQ31
    commontables.o(.constdata) refers to commontables.o(.constdata) for arm_cfft_sR_q31_len64
    commontables.o(.constdata) refers to commontables.o(.constdata) for realCoefAQ31
    commontables.o(.constdata) refers to commontables.o(.constdata) for realCoefBQ31
    commontables.o(.constdata) refers to commontables.o(.constdata) for arm_cfft_sR_q31_len128
    commontables.o(.constdata) refers to commontables.o(.constdata) for realCoefAQ31
    commontables.o(.constdata) refers to commontables.o(.constdata) for realCoefBQ31
    commontables.o(.constdata) refers to commontables.o(.constdata) for arm_cfft_sR_q31_len256
    commontables.o(.constdata) refers to commontables.o(.constdata) for realCoefAQ31
    commontables.o(.constdata) refers to commontables.o(.constdata) for realCoefBQ31
    commontables.o(.constdata) refers to commontables.o(.constdata) for arm_cfft_sR_q31_len512
    commontables.o(.constdata) refers to commontables.o(.constdata) for realCoefAQ31
    commontables.o(.constdata) refers to commontables.o(.constdata) for realCoefBQ31
    commontables.o(.constdata) refers to commontables.o(.constdata) for arm_cfft_sR_q31_len1024
    commontables.o(.constdata) refers to commontables.o(.constdata) for realCoefAQ31
    commontables.o(.constdata) refers to commontables.o(.constdata) for realCoefBQ31
    commontables.o(.constdata) refers to commontables.o(.constdata) for arm_cfft_sR_q31_len2048
    commontables.o(.constdata) refers to commontables.o(.constdata) for realCoefAQ31
    commontables.o(.constdata) refers to commontables.o(.constdata) for realCoefBQ31
    commontables.o(.constdata) refers to commontables.o(.constdata) for arm_cfft_sR_q31_len4096
    commontables.o(.constdata) refers to commontables.o(.constdata) for realCoefAQ15
    commontables.o(.constdata) refers to commontables.o(.constdata) for realCoefBQ15
    commontables.o(.constdata) refers to commontables.o(.constdata) for arm_cfft_sR_q15_len16
    commontables.o(.constdata) refers to commontables.o(.constdata) for realCoefAQ15
    commontables.o(.constdata) refers to commontables.o(.constdata) for realCoefBQ15
    commontables.o(.constdata) refers to commontables.o(.constdata) for arm_cfft_sR_q15_len32
    commontables.o(.constdata) refers to commontables.o(.constdata) for realCoefAQ15
    commontables.o(.constdata) refers to commontables.o(.constdata) for realCoefBQ15
    commontables.o(.constdata) refers to commontables.o(.constdata) for arm_cfft_sR_q15_len64
    commontables.o(.constdata) refers to commontables.o(.constdata) for realCoefAQ15
    commontables.o(.constdata) refers to commontables.o(.constdata) for realCoefBQ15
    commontables.o(.constdata) refers to commontables.o(.constdata) for arm_cfft_sR_q15_len128
    commontables.o(.constdata) refers to commontables.o(.constdata) for realCoefAQ15
    commontables.o(.constdata) refers to commontables.o(.constdata) for realCoefBQ15
    commontables.o(.constdata) refers to commontables.o(.constdata) for arm_cfft_sR_q15_len256
    commontables.o(.constdata) refers to commontables.o(.constdata) for realCoefAQ15
    commontables.o(.constdata) refers to commontables.o(.constdata) for realCoefBQ15
    commontables.o(.constdata) refers to commontables.o(.constdata) for arm_cfft_sR_q15_len512
    commontables.o(.constdata) refers to commontables.o(.constdata) for realCoefAQ15
    commontables.o(.constdata) refers to commontables.o(.constdata) for realCoefBQ15
    commontables.o(.constdata) refers to commontables.o(.constdata) for arm_cfft_sR_q15_len1024
    commontables.o(.constdata) refers to commontables.o(.constdata) for realCoefAQ15
    commontables.o(.constdata) refers to commontables.o(.constdata) for realCoefBQ15
    commontables.o(.constdata) refers to commontables.o(.constdata) for arm_cfft_sR_q15_len2048
    commontables.o(.constdata) refers to commontables.o(.constdata) for realCoefAQ15
    commontables.o(.constdata) refers to commontables.o(.constdata) for realCoefBQ15
    commontables.o(.constdata) refers to commontables.o(.constdata) for arm_cfft_sR_q15_len4096
    complexmathfunctions.o(i.arm_cmplx_mag_f64) refers to dmul.o(.text) for __aeabi_dmul
    complexmathfunctions.o(i.arm_cmplx_mag_f64) refers to dadd.o(.text) for __aeabi_dadd
    complexmathfunctions.o(i.arm_cmplx_mag_f64) refers to sqrt.o(i.__hardfp_sqrt) for __hardfp_sqrt
    complexmathfunctions.o(i.arm_cmplx_mag_fast_q15) refers to fastmathfunctions.o(i.arm_sqrt_q15) for arm_sqrt_q15
    complexmathfunctions.o(i.arm_cmplx_mag_q15) refers to fastmathfunctions.o(i.arm_sqrt_q31) for arm_sqrt_q31
    complexmathfunctions.o(i.arm_cmplx_mag_q31) refers to fastmathfunctions.o(i.arm_sqrt_q31) for arm_sqrt_q31
    complexmathfunctions.o(i.arm_cmplx_mag_squared_f64) refers to dmul.o(.text) for __aeabi_dmul
    complexmathfunctions.o(i.arm_cmplx_mag_squared_f64) refers to dadd.o(.text) for __aeabi_dadd
    complexmathfunctions.o(i.arm_cmplx_mult_cmplx_f64) refers to dmul.o(.text) for __aeabi_dmul
    complexmathfunctions.o(i.arm_cmplx_mult_cmplx_f64) refers to dadd.o(.text) for __aeabi_dsub
    controllerfunctions.o(i.arm_sin_cos_f32) refers to commontables.o(.constdata) for sinTable_f32
    controllerfunctions.o(i.arm_sin_cos_q31) refers to commontables.o(.constdata) for sinTable_q31
    distancefunctions.o(i.arm_chebyshev_distance_f64) refers to dadd.o(.text) for __aeabi_dsub
    distancefunctions.o(i.arm_chebyshev_distance_f64) refers to fabs.o(i.__hardfp_fabs) for __hardfp_fabs
    distancefunctions.o(i.arm_chebyshev_distance_f64) refers to cdrcmple.o(.text) for __aeabi_cdrcmple
    distancefunctions.o(i.arm_cityblock_distance_f64) refers to dadd.o(.text) for __aeabi_dsub
    distancefunctions.o(i.arm_cityblock_distance_f64) refers to fabs.o(i.__hardfp_fabs) for __hardfp_fabs
    distancefunctions.o(i.arm_correlation_distance_f32) refers to statisticsfunctions.o(i.arm_mean_f32) for arm_mean_f32
    distancefunctions.o(i.arm_correlation_distance_f32) refers to basicmathfunctions.o(i.arm_offset_f32) for arm_offset_f32
    distancefunctions.o(i.arm_correlation_distance_f32) refers to statisticsfunctions.o(i.arm_power_f32) for arm_power_f32
    distancefunctions.o(i.arm_correlation_distance_f32) refers to basicmathfunctions.o(i.arm_dot_prod_f32) for arm_dot_prod_f32
    distancefunctions.o(i.arm_cosine_distance_f32) refers to statisticsfunctions.o(i.arm_power_f32) for arm_power_f32
    distancefunctions.o(i.arm_cosine_distance_f32) refers to basicmathfunctions.o(i.arm_dot_prod_f32) for arm_dot_prod_f32
    distancefunctions.o(i.arm_cosine_distance_f64) refers to statisticsfunctions.o(i.arm_power_f64) for arm_power_f64
    distancefunctions.o(i.arm_cosine_distance_f64) refers to basicmathfunctions.o(i.arm_dot_prod_f64) for arm_dot_prod_f64
    distancefunctions.o(i.arm_cosine_distance_f64) refers to dmul.o(.text) for __aeabi_dmul
    distancefunctions.o(i.arm_cosine_distance_f64) refers to sqrt.o(i.__hardfp_sqrt) for __hardfp_sqrt
    distancefunctions.o(i.arm_cosine_distance_f64) refers to ddiv.o(.text) for __aeabi_ddiv
    distancefunctions.o(i.arm_cosine_distance_f64) refers to dadd.o(.text) for __aeabi_drsub
    distancefunctions.o(i.arm_dice_distance) refers to distancefunctions.o(i.arm_boolean_distance_TT_TF_FT) for arm_boolean_distance_TT_TF_FT
    distancefunctions.o(i.arm_dice_distance) refers to dfltui.o(.text) for __aeabi_ui2d
    distancefunctions.o(i.arm_dice_distance) refers to dmul.o(.text) for __aeabi_dmul
    distancefunctions.o(i.arm_dice_distance) refers to dadd.o(.text) for __aeabi_dadd
    distancefunctions.o(i.arm_dice_distance) refers to ddiv.o(.text) for __aeabi_ddiv
    distancefunctions.o(i.arm_dice_distance) refers to d2f.o(.text) for __aeabi_d2f
    distancefunctions.o(i.arm_euclidean_distance_f64) refers to dadd.o(.text) for __aeabi_dsub
    distancefunctions.o(i.arm_euclidean_distance_f64) refers to dmul.o(.text) for __aeabi_dmul
    distancefunctions.o(i.arm_euclidean_distance_f64) refers to sqrt.o(i.__hardfp_sqrt) for __hardfp_sqrt
    distancefunctions.o(i.arm_hamming_distance) refers to distancefunctions.o(i.arm_boolean_distance_TF_FT) for arm_boolean_distance_TF_FT
    distancefunctions.o(i.arm_hamming_distance) refers to dfltui.o(.text) for __aeabi_ui2d
    distancefunctions.o(i.arm_hamming_distance) refers to ddiv.o(.text) for __aeabi_ddiv
    distancefunctions.o(i.arm_hamming_distance) refers to d2f.o(.text) for __aeabi_d2f
    distancefunctions.o(i.arm_jaccard_distance) refers to distancefunctions.o(i.arm_boolean_distance_TT_TF_FT) for arm_boolean_distance_TT_TF_FT
    distancefunctions.o(i.arm_jaccard_distance) refers to dfltui.o(.text) for __aeabi_ui2d
    distancefunctions.o(i.arm_jaccard_distance) refers to ddiv.o(.text) for __aeabi_ddiv
    distancefunctions.o(i.arm_jaccard_distance) refers to d2f.o(.text) for __aeabi_d2f
    distancefunctions.o(i.arm_jensenshannon_distance_f32) refers to distancefunctions.o(i.rel_entr) for rel_entr
    distancefunctions.o(i.arm_kulsinski_distance) refers to distancefunctions.o(i.arm_boolean_distance_TT_TF_FT) for arm_boolean_distance_TT_TF_FT
    distancefunctions.o(i.arm_kulsinski_distance) refers to dfltui.o(.text) for __aeabi_ui2d
    distancefunctions.o(i.arm_kulsinski_distance) refers to ddiv.o(.text) for __aeabi_ddiv
    distancefunctions.o(i.arm_kulsinski_distance) refers to d2f.o(.text) for __aeabi_d2f
    distancefunctions.o(i.arm_minkowski_distance_f32) refers to powf.o(i.__hardfp_powf) for __hardfp_powf
    distancefunctions.o(i.arm_rogerstanimoto_distance) refers to distancefunctions.o(i.arm_boolean_distance_TT_FF_TF_FT) for arm_boolean_distance_TT_FF_TF_FT
    distancefunctions.o(i.arm_rogerstanimoto_distance) refers to dfltui.o(.text) for __aeabi_ui2d
    distancefunctions.o(i.arm_rogerstanimoto_distance) refers to ddiv.o(.text) for __aeabi_ddiv
    distancefunctions.o(i.arm_rogerstanimoto_distance) refers to d2f.o(.text) for __aeabi_d2f
    distancefunctions.o(i.arm_russellrao_distance) refers to distancefunctions.o(i.arm_boolean_distance_TT) for arm_boolean_distance_TT
    distancefunctions.o(i.arm_sokalmichener_distance) refers to distancefunctions.o(i.arm_boolean_distance_TT_FF_TF_FT) for arm_boolean_distance_TT_FF_TF_FT
    distancefunctions.o(i.arm_sokalmichener_distance) refers to dfltui.o(.text) for __aeabi_ui2d
    distancefunctions.o(i.arm_sokalmichener_distance) refers to dmul.o(.text) for __aeabi_dmul
    distancefunctions.o(i.arm_sokalmichener_distance) refers to d2f.o(.text) for __aeabi_d2f
    distancefunctions.o(i.arm_sokalsneath_distance) refers to distancefunctions.o(i.arm_boolean_distance_TT_TF_FT) for arm_boolean_distance_TT_TF_FT
    distancefunctions.o(i.arm_sokalsneath_distance) refers to dfltui.o(.text) for __aeabi_ui2d
    distancefunctions.o(i.arm_sokalsneath_distance) refers to dmul.o(.text) for __aeabi_dmul
    distancefunctions.o(i.arm_sokalsneath_distance) refers to d2f.o(.text) for __aeabi_d2f
    distancefunctions.o(i.arm_yule_distance) refers to distancefunctions.o(i.arm_boolean_distance_TT_FF_TF_FT) for arm_boolean_distance_TT_FF_TF_FT
    distancefunctions.o(i.arm_yule_distance) refers to dfltui.o(.text) for __aeabi_ui2d
    distancefunctions.o(i.arm_yule_distance) refers to dmul.o(.text) for __aeabi_dmul
    distancefunctions.o(i.arm_yule_distance) refers to dadd.o(.text) for __aeabi_dadd
    distancefunctions.o(i.arm_yule_distance) refers to ddiv.o(.text) for __aeabi_ddiv
    distancefunctions.o(i.arm_yule_distance) refers to d2f.o(.text) for __aeabi_d2f
    distancefunctions.o(i.rel_entr) refers to logf.o(i.__hardfp_logf) for __hardfp_logf
    fastmathfunctions.o(i.arm_atan2_f32) refers to fastmathfunctions.o(.constdata) for .constdata
    fastmathfunctions.o(i.arm_atan2_q15) refers to fastmathfunctions.o(i.arm_divide_q15) for arm_divide_q15
    fastmathfunctions.o(i.arm_atan2_q15) refers to fastmathfunctions.o(.constdata) for .constdata
    fastmathfunctions.o(i.arm_atan2_q31) refers to fastmathfunctions.o(i.arm_divide_q31) for arm_divide_q31
    fastmathfunctions.o(i.arm_atan2_q31) refers to llshl.o(.text) for __aeabi_llsl
    fastmathfunctions.o(i.arm_atan2_q31) refers to fastmathfunctions.o(.constdata) for .constdata
    fastmathfunctions.o(i.arm_cos_f32) refers to commontables.o(.constdata) for sinTable_f32
    fastmathfunctions.o(i.arm_cos_q15) refers to commontables.o(.constdata) for sinTable_q15
    fastmathfunctions.o(i.arm_cos_q31) refers to commontables.o(.constdata) for sinTable_q31
    fastmathfunctions.o(i.arm_divide_q15) refers to basicmathfunctions.o(i.arm_abs_q15) for arm_abs_q15
    fastmathfunctions.o(i.arm_divide_q31) refers to basicmathfunctions.o(i.arm_abs_q31) for arm_abs_q31
    fastmathfunctions.o(i.arm_divide_q31) refers to ldiv.o(.text) for __aeabi_ldivmod
    fastmathfunctions.o(i.arm_divide_q31) refers to llsshr.o(.text) for __aeabi_lasr
    fastmathfunctions.o(i.arm_sin_f32) refers to commontables.o(.constdata) for sinTable_f32
    fastmathfunctions.o(i.arm_sin_q15) refers to commontables.o(.constdata) for sinTable_q15
    fastmathfunctions.o(i.arm_sin_q31) refers to commontables.o(.constdata) for sinTable_q31
    fastmathfunctions.o(i.arm_sqrt_q15) refers to commontables.o(.constdata) for sqrt_initial_lut_q15
    fastmathfunctions.o(i.arm_sqrt_q31) refers to commontables.o(.constdata) for sqrt_initial_lut_q31
    fastmathfunctions.o(i.arm_vexp_f32) refers to expf.o(i.__hardfp_expf) for __hardfp_expf
    fastmathfunctions.o(i.arm_vexp_f64) refers to exp.o(i.__hardfp_exp) for __hardfp_exp
    fastmathfunctions.o(i.arm_vlog_f32) refers to logf.o(i.__hardfp_logf) for __hardfp_logf
    fastmathfunctions.o(i.arm_vlog_f64) refers to log.o(i.__hardfp_log) for __hardfp_log
    filteringfunctions.o(i.arm_biquad_cas_df1_32x64_init_q31) refers to memseta.o(.text) for __aeabi_memclr4
    filteringfunctions.o(i.arm_biquad_cas_df1_32x64_q31) refers to llshl.o(.text) for __aeabi_llsl
    filteringfunctions.o(i.arm_biquad_cascade_df1_init_f32) refers to memseta.o(.text) for __aeabi_memclr4
    filteringfunctions.o(i.arm_biquad_cascade_df1_init_q15) refers to memseta.o(.text) for __aeabi_memclr
    filteringfunctions.o(i.arm_biquad_cascade_df1_init_q31) refers to memseta.o(.text) for __aeabi_memclr4
    filteringfunctions.o(i.arm_biquad_cascade_df1_q31) refers to llsshr.o(.text) for __aeabi_lasr
    filteringfunctions.o(i.arm_biquad_cascade_df2T_f64) refers to dmul.o(.text) for __aeabi_dmul
    filteringfunctions.o(i.arm_biquad_cascade_df2T_f64) refers to dadd.o(.text) for __aeabi_dadd
    filteringfunctions.o(i.arm_biquad_cascade_df2T_init_f32) refers to memseta.o(.text) for __aeabi_memclr4
    filteringfunctions.o(i.arm_biquad_cascade_df2T_init_f64) refers to memseta.o(.text) for __aeabi_memclr4
    filteringfunctions.o(i.arm_biquad_cascade_stereo_df2T_init_f32) refers to memseta.o(.text) for __aeabi_memclr4
    filteringfunctions.o(i.arm_conv_fast_opt_q15) refers to supportfunctions.o(i.arm_fill_q15) for arm_fill_q15
    filteringfunctions.o(i.arm_conv_fast_opt_q15) refers to supportfunctions.o(i.arm_copy_q15) for arm_copy_q15
    filteringfunctions.o(i.arm_conv_opt_q15) refers to supportfunctions.o(i.arm_fill_q15) for arm_fill_q15
    filteringfunctions.o(i.arm_conv_opt_q15) refers to supportfunctions.o(i.arm_copy_q15) for arm_copy_q15
    filteringfunctions.o(i.arm_conv_opt_q7) refers to supportfunctions.o(i.arm_fill_q15) for arm_fill_q15
    filteringfunctions.o(i.arm_conv_partial_fast_opt_q15) refers to supportfunctions.o(i.arm_fill_q15) for arm_fill_q15
    filteringfunctions.o(i.arm_conv_partial_fast_opt_q15) refers to supportfunctions.o(i.arm_copy_q15) for arm_copy_q15
    filteringfunctions.o(i.arm_conv_partial_opt_q15) refers to supportfunctions.o(i.arm_fill_q15) for arm_fill_q15
    filteringfunctions.o(i.arm_conv_partial_opt_q15) refers to supportfunctions.o(i.arm_copy_q15) for arm_copy_q15
    filteringfunctions.o(i.arm_conv_partial_opt_q7) refers to supportfunctions.o(i.arm_fill_q15) for arm_fill_q15
    filteringfunctions.o(i.arm_correlate_f64) refers to dmul.o(.text) for __aeabi_dmul
    filteringfunctions.o(i.arm_correlate_f64) refers to dadd.o(.text) for __aeabi_dadd
    filteringfunctions.o(i.arm_correlate_fast_opt_q15) refers to supportfunctions.o(i.arm_fill_q15) for arm_fill_q15
    filteringfunctions.o(i.arm_correlate_fast_opt_q15) refers to supportfunctions.o(i.arm_copy_q15) for arm_copy_q15
    filteringfunctions.o(i.arm_correlate_opt_q15) refers to supportfunctions.o(i.arm_fill_q15) for arm_fill_q15
    filteringfunctions.o(i.arm_correlate_opt_q15) refers to supportfunctions.o(i.arm_copy_q15) for arm_copy_q15
    filteringfunctions.o(i.arm_correlate_opt_q7) refers to supportfunctions.o(i.arm_fill_q15) for arm_fill_q15
    filteringfunctions.o(i.arm_fir_decimate_init_f32) refers to memseta.o(.text) for __aeabi_memclr4
    filteringfunctions.o(i.arm_fir_decimate_init_q15) refers to memseta.o(.text) for __aeabi_memclr
    filteringfunctions.o(i.arm_fir_decimate_init_q31) refers to memseta.o(.text) for __aeabi_memclr4
    filteringfunctions.o(i.arm_fir_f64) refers to dmul.o(.text) for __aeabi_dmul
    filteringfunctions.o(i.arm_fir_f64) refers to dadd.o(.text) for __aeabi_dadd
    filteringfunctions.o(i.arm_fir_init_f32) refers to memseta.o(.text) for __aeabi_memclr4
    filteringfunctions.o(i.arm_fir_init_f64) refers to memseta.o(.text) for __aeabi_memclr4
    filteringfunctions.o(i.arm_fir_init_q15) refers to memseta.o(.text) for __aeabi_memclr
    filteringfunctions.o(i.arm_fir_init_q31) refers to memseta.o(.text) for __aeabi_memclr4
    filteringfunctions.o(i.arm_fir_init_q7) refers to memseta.o(.text) for __aeabi_memclr
    filteringfunctions.o(i.arm_fir_interpolate_init_f32) refers to memseta.o(.text) for __aeabi_memclr4
    filteringfunctions.o(i.arm_fir_interpolate_init_q15) refers to memseta.o(.text) for __aeabi_memclr
    filteringfunctions.o(i.arm_fir_interpolate_init_q31) refers to memseta.o(.text) for __aeabi_memclr4
    filteringfunctions.o(i.arm_fir_lattice_init_f32) refers to memseta.o(.text) for __aeabi_memclr4
    filteringfunctions.o(i.arm_fir_lattice_init_q15) refers to memseta.o(.text) for __aeabi_memclr
    filteringfunctions.o(i.arm_fir_lattice_init_q31) refers to memseta.o(.text) for __aeabi_memclr4
    filteringfunctions.o(i.arm_fir_sparse_init_f32) refers to memseta.o(.text) for __aeabi_memclr4
    filteringfunctions.o(i.arm_fir_sparse_init_q15) refers to memseta.o(.text) for __aeabi_memclr
    filteringfunctions.o(i.arm_fir_sparse_init_q31) refers to memseta.o(.text) for __aeabi_memclr4
    filteringfunctions.o(i.arm_fir_sparse_init_q7) refers to memseta.o(.text) for __aeabi_memclr
    filteringfunctions.o(i.arm_iir_lattice_init_f32) refers to memseta.o(.text) for __aeabi_memclr4
    filteringfunctions.o(i.arm_iir_lattice_init_q15) refers to memseta.o(.text) for __aeabi_memclr
    filteringfunctions.o(i.arm_iir_lattice_init_q31) refers to memseta.o(.text) for __aeabi_memclr4
    filteringfunctions.o(i.arm_levinson_durbin_q31) refers to fastmathfunctions.o(i.arm_divide_q15) for arm_divide_q15
    filteringfunctions.o(i.arm_lms_init_f32) refers to memseta.o(.text) for __aeabi_memclr4
    filteringfunctions.o(i.arm_lms_init_q15) refers to memseta.o(.text) for __aeabi_memclr
    filteringfunctions.o(i.arm_lms_init_q31) refers to memseta.o(.text) for __aeabi_memclr4
    filteringfunctions.o(i.arm_lms_norm_init_f32) refers to memseta.o(.text) for __aeabi_memclr4
    filteringfunctions.o(i.arm_lms_norm_init_q15) refers to memseta.o(.text) for __aeabi_memclr
    filteringfunctions.o(i.arm_lms_norm_init_q15) refers to commontables.o(.constdata) for armRecipTableQ15
    filteringfunctions.o(i.arm_lms_norm_init_q31) refers to memseta.o(.text) for __aeabi_memclr4
    filteringfunctions.o(i.arm_lms_norm_init_q31) refers to commontables.o(.constdata) for armRecipTableQ31
    filteringfunctions.o(i.arm_lms_norm_q31) refers to llsshr.o(.text) for __aeabi_lasr
    matrixfunctions.o(i.arm_householder_f32) refers to basicmathfunctions.o(i.arm_dot_prod_f32) for arm_dot_prod_f32
    matrixfunctions.o(i.arm_householder_f32) refers to memseta.o(.text) for __aeabi_memclr4
    matrixfunctions.o(i.arm_householder_f32) refers to basicmathfunctions.o(i.arm_scale_f32) for arm_scale_f32
    matrixfunctions.o(i.arm_householder_f64) refers to basicmathfunctions.o(i.arm_dot_prod_f64) for arm_dot_prod_f64
    matrixfunctions.o(i.arm_householder_f64) refers to cdcmple.o(.text) for __aeabi_cdcmple
    matrixfunctions.o(i.arm_householder_f64) refers to memseta.o(.text) for __aeabi_memclr4
    matrixfunctions.o(i.arm_householder_f64) refers to dmul.o(.text) for __aeabi_dmul
    matrixfunctions.o(i.arm_householder_f64) refers to dadd.o(.text) for __aeabi_dadd
    matrixfunctions.o(i.arm_householder_f64) refers to sqrt.o(i.__hardfp_sqrt) for __hardfp_sqrt
    matrixfunctions.o(i.arm_householder_f64) refers to cdrcmple.o(.text) for __aeabi_cdrcmple
    matrixfunctions.o(i.arm_householder_f64) refers to ddiv.o(.text) for __aeabi_ddiv
    matrixfunctions.o(i.arm_householder_f64) refers to basicmathfunctions.o(i.arm_scale_f64) for arm_scale_f64
    matrixfunctions.o(i.arm_mat_cholesky_f32) refers to sqrtf.o(i.__hardfp_sqrtf) for __hardfp_sqrtf
    matrixfunctions.o(i.arm_mat_cholesky_f64) refers to dmul.o(.text) for __aeabi_dmul
    matrixfunctions.o(i.arm_mat_cholesky_f64) refers to dadd.o(.text) for __aeabi_drsub
    matrixfunctions.o(i.arm_mat_cholesky_f64) refers to cdcmple.o(.text) for __aeabi_cdcmple
    matrixfunctions.o(i.arm_mat_cholesky_f64) refers to sqrt.o(i.__hardfp_sqrt) for __hardfp_sqrt
    matrixfunctions.o(i.arm_mat_cholesky_f64) refers to ddiv.o(.text) for __aeabi_ddiv
    matrixfunctions.o(i.arm_mat_inverse_f64) refers to fabs.o(i.__hardfp_fabs) for __hardfp_fabs
    matrixfunctions.o(i.arm_mat_inverse_f64) refers to cdrcmple.o(.text) for __aeabi_cdrcmple
    matrixfunctions.o(i.arm_mat_inverse_f64) refers to cdcmple.o(.text) for __aeabi_cdcmpeq
    matrixfunctions.o(i.arm_mat_inverse_f64) refers to ddiv.o(.text) for __aeabi_ddiv
    matrixfunctions.o(i.arm_mat_inverse_f64) refers to dmul.o(.text) for __aeabi_dmul
    matrixfunctions.o(i.arm_mat_inverse_f64) refers to dadd.o(.text) for __aeabi_drsub
    matrixfunctions.o(i.arm_mat_ldlt_f32) refers to memseta.o(.text) for __aeabi_memclr4
    matrixfunctions.o(i.arm_mat_ldlt_f32) refers to memcpya.o(.text) for __aeabi_memcpy4
    matrixfunctions.o(i.arm_mat_ldlt_f64) refers to memseta.o(.text) for __aeabi_memclr4
    matrixfunctions.o(i.arm_mat_ldlt_f64) refers to memcpya.o(.text) for __aeabi_memcpy4
    matrixfunctions.o(i.arm_mat_ldlt_f64) refers to cdrcmple.o(.text) for __aeabi_cdrcmple
    matrixfunctions.o(i.arm_mat_ldlt_f64) refers to fabs.o(i.__hardfp_fabs) for __hardfp_fabs
    matrixfunctions.o(i.arm_mat_ldlt_f64) refers to cdcmple.o(.text) for __aeabi_cdcmple
    matrixfunctions.o(i.arm_mat_ldlt_f64) refers to dmul.o(.text) for __aeabi_dmul
    matrixfunctions.o(i.arm_mat_ldlt_f64) refers to ddiv.o(.text) for __aeabi_ddiv
    matrixfunctions.o(i.arm_mat_ldlt_f64) refers to dadd.o(.text) for __aeabi_drsub
    matrixfunctions.o(i.arm_mat_mult_f64) refers to dmul.o(.text) for __aeabi_dmul
    matrixfunctions.o(i.arm_mat_mult_f64) refers to dadd.o(.text) for __aeabi_dadd
    matrixfunctions.o(i.arm_mat_mult_q15) refers to matrixfunctions.o(i.arm_mat_trans_q15) for arm_mat_trans_q15
    matrixfunctions.o(i.arm_mat_qr_f32) refers to memcpya.o(.text) for __aeabi_memcpy4
    matrixfunctions.o(i.arm_mat_qr_f32) refers to matrixfunctions.o(i.arm_householder_f32) for arm_householder_f32
    matrixfunctions.o(i.arm_mat_qr_f32) refers to memseta.o(.text) for __aeabi_memclr4
    matrixfunctions.o(i.arm_mat_qr_f64) refers to memcpya.o(.text) for __aeabi_memcpy4
    matrixfunctions.o(i.arm_mat_qr_f64) refers to matrixfunctions.o(i.arm_householder_f64) for arm_householder_f64
    matrixfunctions.o(i.arm_mat_qr_f64) refers to dmul.o(.text) for __aeabi_dmul
    matrixfunctions.o(i.arm_mat_qr_f64) refers to dadd.o(.text) for __aeabi_dadd
    matrixfunctions.o(i.arm_mat_qr_f64) refers to memseta.o(.text) for __aeabi_memclr4
    matrixfunctions.o(i.arm_mat_solve_lower_triangular_f64) refers to dmul.o(.text) for __aeabi_dmul
    matrixfunctions.o(i.arm_mat_solve_lower_triangular_f64) refers to dadd.o(.text) for __aeabi_drsub
    matrixfunctions.o(i.arm_mat_solve_lower_triangular_f64) refers to cdcmple.o(.text) for __aeabi_cdcmpeq
    matrixfunctions.o(i.arm_mat_solve_lower_triangular_f64) refers to ddiv.o(.text) for __aeabi_ddiv
    matrixfunctions.o(i.arm_mat_solve_upper_triangular_f64) refers to dmul.o(.text) for __aeabi_dmul
    matrixfunctions.o(i.arm_mat_solve_upper_triangular_f64) refers to dadd.o(.text) for __aeabi_drsub
    matrixfunctions.o(i.arm_mat_solve_upper_triangular_f64) refers to cdcmple.o(.text) for __aeabi_cdcmpeq
    matrixfunctions.o(i.arm_mat_solve_upper_triangular_f64) refers to ddiv.o(.text) for __aeabi_ddiv
    matrixfunctions.o(i.arm_mat_sub_f64) refers to dadd.o(.text) for __aeabi_dsub
    quaternionmathfunctions.o(i.arm_quaternion_norm_f32) refers to sqrtf.o(i.__hardfp_sqrtf) for __hardfp_sqrtf
    quaternionmathfunctions.o(i.arm_quaternion_normalize_f32) refers to sqrtf.o(i.__hardfp_sqrtf) for __hardfp_sqrtf
    quaternionmathfunctions.o(i.arm_quaternion_product_f32) refers to quaternionmathfunctions.o(i.arm_quaternion_product_single_f32) for arm_quaternion_product_single_f32
    quaternionmathfunctions.o(i.arm_rotation2quaternion_f32) refers to sqrtf.o(i.__hardfp_sqrtf) for __hardfp_sqrtf
    svmfunctions.o(i.arm_svm_rbf_predict_f32) refers to expf.o(i.__hardfp_expf) for __hardfp_expf
    svmfunctions.o(i.arm_svm_sigmoid_predict_f32) refers to tanhf.o(i.__hardfp_tanhf) for __hardfp_tanhf
    statisticsfunctions.o(i.arm_absmax_f64) refers to fabs.o(i.__hardfp_fabs) for __hardfp_fabs
    statisticsfunctions.o(i.arm_absmax_f64) refers to cdcmple.o(.text) for __aeabi_cdcmple
    statisticsfunctions.o(i.arm_absmax_no_idx_f64) refers to fabs.o(i.__hardfp_fabs) for __hardfp_fabs
    statisticsfunctions.o(i.arm_absmax_no_idx_f64) refers to cdcmple.o(.text) for __aeabi_cdcmple
    statisticsfunctions.o(i.arm_absmin_f64) refers to fabs.o(i.__hardfp_fabs) for __hardfp_fabs
    statisticsfunctions.o(i.arm_absmin_f64) refers to cdrcmple.o(.text) for __aeabi_cdrcmple
    statisticsfunctions.o(i.arm_absmin_no_idx_f64) refers to fabs.o(i.__hardfp_fabs) for __hardfp_fabs
    statisticsfunctions.o(i.arm_absmin_no_idx_f64) refers to cdrcmple.o(.text) for __aeabi_cdrcmple
    statisticsfunctions.o(i.arm_accumulate_f64) refers to dadd.o(.text) for __aeabi_dadd
    statisticsfunctions.o(i.arm_entropy_f32) refers to logf.o(i.__hardfp_logf) for __hardfp_logf
    statisticsfunctions.o(i.arm_entropy_f64) refers to log.o(i.__hardfp_log) for __hardfp_log
    statisticsfunctions.o(i.arm_entropy_f64) refers to dmul.o(.text) for __aeabi_dmul
    statisticsfunctions.o(i.arm_entropy_f64) refers to dadd.o(.text) for __aeabi_dadd
    statisticsfunctions.o(i.arm_kullback_leibler_f32) refers to logf.o(i.__hardfp_logf) for __hardfp_logf
    statisticsfunctions.o(i.arm_kullback_leibler_f64) refers to ddiv.o(.text) for __aeabi_ddiv
    statisticsfunctions.o(i.arm_kullback_leibler_f64) refers to log.o(i.__hardfp_log) for __hardfp_log
    statisticsfunctions.o(i.arm_kullback_leibler_f64) refers to dmul.o(.text) for __aeabi_dmul
    statisticsfunctions.o(i.arm_kullback_leibler_f64) refers to dadd.o(.text) for __aeabi_dadd
    statisticsfunctions.o(i.arm_logsumexp_dot_prod_f32) refers to basicmathfunctions.o(i.arm_add_f32) for arm_add_f32
    statisticsfunctions.o(i.arm_logsumexp_dot_prod_f32) refers to statisticsfunctions.o(i.arm_logsumexp_f32) for arm_logsumexp_f32
    statisticsfunctions.o(i.arm_logsumexp_f32) refers to expf.o(i.__hardfp_expf) for __hardfp_expf
    statisticsfunctions.o(i.arm_logsumexp_f32) refers to logf.o(i.__hardfp_logf) for __hardfp_logf
    statisticsfunctions.o(i.arm_max_f64) refers to cdcmple.o(.text) for __aeabi_cdcmple
    statisticsfunctions.o(i.arm_max_no_idx_f64) refers to cdcmple.o(.text) for __aeabi_cdcmple
    statisticsfunctions.o(i.arm_mean_f64) refers to dadd.o(.text) for __aeabi_dadd
    statisticsfunctions.o(i.arm_mean_f64) refers to dfltui.o(.text) for __aeabi_ui2d
    statisticsfunctions.o(i.arm_mean_f64) refers to ddiv.o(.text) for __aeabi_ddiv
    statisticsfunctions.o(i.arm_mean_q31) refers to ldiv.o(.text) for __aeabi_ldivmod
    statisticsfunctions.o(i.arm_min_f64) refers to cdrcmple.o(.text) for __aeabi_cdrcmple
    statisticsfunctions.o(i.arm_min_no_idx_f64) refers to cdrcmple.o(.text) for __aeabi_cdrcmple
    statisticsfunctions.o(i.arm_mse_f64) refers to dadd.o(.text) for __aeabi_dsub
    statisticsfunctions.o(i.arm_mse_f64) refers to dmul.o(.text) for __aeabi_dmul
    statisticsfunctions.o(i.arm_mse_f64) refers to dfltui.o(.text) for __aeabi_ui2d
    statisticsfunctions.o(i.arm_mse_f64) refers to ddiv.o(.text) for __aeabi_ddiv
    statisticsfunctions.o(i.arm_mse_q15) refers to ldiv.o(.text) for __aeabi_ldivmod
    statisticsfunctions.o(i.arm_mse_q31) refers to ldiv.o(.text) for __aeabi_ldivmod
    statisticsfunctions.o(i.arm_power_f64) refers to dmul.o(.text) for __aeabi_dmul
    statisticsfunctions.o(i.arm_power_f64) refers to dadd.o(.text) for __aeabi_dadd
    statisticsfunctions.o(i.arm_rms_q15) refers to ldiv.o(.text) for __aeabi_ldivmod
    statisticsfunctions.o(i.arm_rms_q15) refers to fastmathfunctions.o(i.arm_sqrt_q15) for arm_sqrt_q15
    statisticsfunctions.o(i.arm_rms_q31) refers to uldiv.o(.text) for __aeabi_uldivmod
    statisticsfunctions.o(i.arm_rms_q31) refers to fastmathfunctions.o(i.arm_sqrt_q31) for arm_sqrt_q31
    statisticsfunctions.o(i.arm_std_f32) refers to statisticsfunctions.o(i.arm_var_f32) for arm_var_f32
    statisticsfunctions.o(i.arm_std_f64) refers to statisticsfunctions.o(i.arm_var_f64) for arm_var_f64
    statisticsfunctions.o(i.arm_std_f64) refers to sqrt.o(i.__hardfp_sqrt) for __hardfp_sqrt
    statisticsfunctions.o(i.arm_std_q15) refers to ldiv.o(.text) for __aeabi_ldivmod
    statisticsfunctions.o(i.arm_std_q15) refers to fastmathfunctions.o(i.arm_sqrt_q15) for arm_sqrt_q15
    statisticsfunctions.o(i.arm_std_q31) refers to ldiv.o(.text) for __aeabi_ldivmod
    statisticsfunctions.o(i.arm_std_q31) refers to fastmathfunctions.o(i.arm_sqrt_q31) for arm_sqrt_q31
    statisticsfunctions.o(i.arm_var_f64) refers to statisticsfunctions.o(i.arm_mean_f64) for arm_mean_f64
    statisticsfunctions.o(i.arm_var_f64) refers to dadd.o(.text) for __aeabi_dsub
    statisticsfunctions.o(i.arm_var_f64) refers to dmul.o(.text) for __aeabi_dmul
    statisticsfunctions.o(i.arm_var_f64) refers to dfltui.o(.text) for __aeabi_ui2d
    statisticsfunctions.o(i.arm_var_f64) refers to ddiv.o(.text) for __aeabi_ddiv
    statisticsfunctions.o(i.arm_var_q15) refers to ldiv.o(.text) for __aeabi_ldivmod
    statisticsfunctions.o(i.arm_var_q31) refers to ldiv.o(.text) for __aeabi_ldivmod
    supportfunctions.o(i.arm_bitonic_sort_f32) refers to memcpya.o(.text) for __aeabi_memcpy4
    supportfunctions.o(i.arm_bitonic_sort_f32) refers to supportfunctions.o(i.arm_bitonic_sort_core_f32) for arm_bitonic_sort_core_f32
    supportfunctions.o(i.arm_bubble_sort_f32) refers to memcpya.o(.text) for __aeabi_memcpy4
    supportfunctions.o(i.arm_f64_to_float) refers to d2f.o(.text) for __aeabi_d2f
    supportfunctions.o(i.arm_f64_to_q15) refers to dmul.o(.text) for __aeabi_dmul
    supportfunctions.o(i.arm_f64_to_q15) refers to dfixi.o(.text) for __aeabi_d2iz
    supportfunctions.o(i.arm_f64_to_q31) refers to dmul.o(.text) for __aeabi_dmul
    supportfunctions.o(i.arm_f64_to_q31) refers to dfixl.o(.text) for __aeabi_d2lz
    supportfunctions.o(i.arm_f64_to_q7) refers to dmul.o(.text) for __aeabi_dmul
    supportfunctions.o(i.arm_f64_to_q7) refers to dfixi.o(.text) for __aeabi_d2iz
    supportfunctions.o(i.arm_float_to_f64) refers to f2d.o(.text) for __aeabi_f2d
    supportfunctions.o(i.arm_float_to_q31) refers to ffixl.o(.text) for __aeabi_f2lz
    supportfunctions.o(i.arm_heap_sort_f32) refers to memcpya.o(.text) for __aeabi_memcpy4
    supportfunctions.o(i.arm_heap_sort_f32) refers to supportfunctions.o(i.arm_heapify) for arm_heapify
    supportfunctions.o(i.arm_insertion_sort_f32) refers to memcpya.o(.text) for __aeabi_memcpy4
    supportfunctions.o(i.arm_merge_sort_f32) refers to memcpya.o(.text) for __aeabi_memcpy4
    supportfunctions.o(i.arm_merge_sort_f32) refers to supportfunctions.o(i.arm_merge_sort_core_f32) for arm_merge_sort_core_f32
    supportfunctions.o(i.arm_q15_to_f64) refers to dflti.o(.text) for __aeabi_i2d
    supportfunctions.o(i.arm_q15_to_f64) refers to dmul.o(.text) for __aeabi_dmul
    supportfunctions.o(i.arm_q31_to_f64) refers to dflti.o(.text) for __aeabi_i2d
    supportfunctions.o(i.arm_q31_to_f64) refers to dmul.o(.text) for __aeabi_dmul
    supportfunctions.o(i.arm_q7_to_f64) refers to dflti.o(.text) for __aeabi_i2d
    supportfunctions.o(i.arm_q7_to_f64) refers to dmul.o(.text) for __aeabi_dmul
    supportfunctions.o(i.arm_quick_sort_f32) refers to memcpya.o(.text) for __aeabi_memcpy4
    supportfunctions.o(i.arm_quick_sort_f32) refers to supportfunctions.o(i.arm_quick_sort_core_f32) for arm_quick_sort_core_f32
    supportfunctions.o(i.arm_selection_sort_f32) refers to memcpya.o(.text) for __aeabi_memcpy4
    supportfunctions.o(i.arm_sort_f32) refers to supportfunctions.o(i.arm_bitonic_sort_f32) for arm_bitonic_sort_f32
    supportfunctions.o(i.arm_sort_f32) refers to supportfunctions.o(i.arm_bubble_sort_f32) for arm_bubble_sort_f32
    supportfunctions.o(i.arm_sort_f32) refers to supportfunctions.o(i.arm_heap_sort_f32) for arm_heap_sort_f32
    supportfunctions.o(i.arm_sort_f32) refers to supportfunctions.o(i.arm_insertion_sort_f32) for arm_insertion_sort_f32
    supportfunctions.o(i.arm_sort_f32) refers to supportfunctions.o(i.arm_quick_sort_f32) for arm_quick_sort_f32
    supportfunctions.o(i.arm_sort_f32) refers to supportfunctions.o(i.arm_selection_sort_f32) for arm_selection_sort_f32
    transformfunctions.o(i.arm_cfft_f32) refers to transformfunctions.o(i.arm_cfft_radix8by2_f32) for arm_cfft_radix8by2_f32
    transformfunctions.o(i.arm_cfft_f32) refers to transformfunctions.o(i.arm_cfft_radix8by4_f32) for arm_cfft_radix8by4_f32
    transformfunctions.o(i.arm_cfft_f32) refers to transformfunctions.o(i.arm_radix8_butterfly_f32) for arm_radix8_butterfly_f32
    transformfunctions.o(i.arm_cfft_f32) refers to transformfunctions.o(i.arm_bitreversal_32) for arm_bitreversal_32
    transformfunctions.o(i.arm_cfft_f64) refers to transformfunctions.o(i.arm_radix4_butterfly_f64) for arm_radix4_butterfly_f64
    transformfunctions.o(i.arm_cfft_f64) refers to transformfunctions.o(i.arm_cfft_radix4by2_f64) for arm_cfft_radix4by2_f64
    transformfunctions.o(i.arm_cfft_f64) refers to transformfunctions.o(i.arm_bitreversal_64) for arm_bitreversal_64
    transformfunctions.o(i.arm_cfft_f64) refers to dfltui.o(.text) for __aeabi_ui2d
    transformfunctions.o(i.arm_cfft_f64) refers to ddiv.o(.text) for __aeabi_ddiv
    transformfunctions.o(i.arm_cfft_f64) refers to dmul.o(.text) for __aeabi_dmul
    transformfunctions.o(i.arm_cfft_init_f32) refers to commontables.o(.constdata) for arm_cfft_sR_f32_len4096
    transformfunctions.o(i.arm_cfft_init_f32) refers to commontables.o(.constdata) for arm_cfft_sR_f32_len2048
    transformfunctions.o(i.arm_cfft_init_f32) refers to commontables.o(.constdata) for arm_cfft_sR_f32_len1024
    transformfunctions.o(i.arm_cfft_init_f32) refers to commontables.o(.constdata) for arm_cfft_sR_f32_len512
    transformfunctions.o(i.arm_cfft_init_f32) refers to commontables.o(.constdata) for arm_cfft_sR_f32_len256
    transformfunctions.o(i.arm_cfft_init_f32) refers to commontables.o(.constdata) for arm_cfft_sR_f32_len128
    transformfunctions.o(i.arm_cfft_init_f32) refers to commontables.o(.constdata) for arm_cfft_sR_f32_len64
    transformfunctions.o(i.arm_cfft_init_f32) refers to commontables.o(.constdata) for arm_cfft_sR_f32_len32
    transformfunctions.o(i.arm_cfft_init_f32) refers to commontables.o(.constdata) for arm_cfft_sR_f32_len16
    transformfunctions.o(i.arm_cfft_init_f64) refers to commontables.o(.constdata) for arm_cfft_sR_f64_len4096
    transformfunctions.o(i.arm_cfft_init_f64) refers to commontables.o(.constdata) for arm_cfft_sR_f64_len2048
    transformfunctions.o(i.arm_cfft_init_f64) refers to commontables.o(.constdata) for arm_cfft_sR_f64_len1024
    transformfunctions.o(i.arm_cfft_init_f64) refers to commontables.o(.constdata) for arm_cfft_sR_f64_len512
    transformfunctions.o(i.arm_cfft_init_f64) refers to commontables.o(.constdata) for arm_cfft_sR_f64_len256
    transformfunctions.o(i.arm_cfft_init_f64) refers to commontables.o(.constdata) for arm_cfft_sR_f64_len128
    transformfunctions.o(i.arm_cfft_init_f64) refers to commontables.o(.constdata) for arm_cfft_sR_f64_len64
    transformfunctions.o(i.arm_cfft_init_f64) refers to commontables.o(.constdata) for arm_cfft_sR_f64_len32
    transformfunctions.o(i.arm_cfft_init_f64) refers to commontables.o(.constdata) for arm_cfft_sR_f64_len16
    transformfunctions.o(i.arm_cfft_init_q15) refers to commontables.o(.constdata) for arm_cfft_sR_q15_len4096
    transformfunctions.o(i.arm_cfft_init_q15) refers to commontables.o(.constdata) for arm_cfft_sR_q15_len2048
    transformfunctions.o(i.arm_cfft_init_q15) refers to commontables.o(.constdata) for arm_cfft_sR_q15_len1024
    transformfunctions.o(i.arm_cfft_init_q15) refers to commontables.o(.constdata) for arm_cfft_sR_q15_len512
    transformfunctions.o(i.arm_cfft_init_q15) refers to commontables.o(.constdata) for arm_cfft_sR_q15_len256
    transformfunctions.o(i.arm_cfft_init_q15) refers to commontables.o(.constdata) for arm_cfft_sR_q15_len128
    transformfunctions.o(i.arm_cfft_init_q15) refers to commontables.o(.constdata) for arm_cfft_sR_q15_len64
    transformfunctions.o(i.arm_cfft_init_q15) refers to commontables.o(.constdata) for arm_cfft_sR_q15_len32
    transformfunctions.o(i.arm_cfft_init_q15) refers to commontables.o(.constdata) for arm_cfft_sR_q15_len16
    transformfunctions.o(i.arm_cfft_init_q31) refers to commontables.o(.constdata) for arm_cfft_sR_q31_len4096
    transformfunctions.o(i.arm_cfft_init_q31) refers to commontables.o(.constdata) for arm_cfft_sR_q31_len2048
    transformfunctions.o(i.arm_cfft_init_q31) refers to commontables.o(.constdata) for arm_cfft_sR_q31_len1024
    transformfunctions.o(i.arm_cfft_init_q31) refers to commontables.o(.constdata) for arm_cfft_sR_q31_len512
    transformfunctions.o(i.arm_cfft_init_q31) refers to commontables.o(.constdata) for arm_cfft_sR_q31_len256
    transformfunctions.o(i.arm_cfft_init_q31) refers to commontables.o(.constdata) for arm_cfft_sR_q31_len128
    transformfunctions.o(i.arm_cfft_init_q31) refers to commontables.o(.constdata) for arm_cfft_sR_q31_len64
    transformfunctions.o(i.arm_cfft_init_q31) refers to commontables.o(.constdata) for arm_cfft_sR_q31_len32
    transformfunctions.o(i.arm_cfft_init_q31) refers to commontables.o(.constdata) for arm_cfft_sR_q31_len16
    transformfunctions.o(i.arm_cfft_q15) refers to transformfunctions.o(i.arm_radix4_butterfly_inverse_q15) for arm_radix4_butterfly_inverse_q15
    transformfunctions.o(i.arm_cfft_q15) refers to transformfunctions.o(i.arm_cfft_radix4by2_inverse_q15) for arm_cfft_radix4by2_inverse_q15
    transformfunctions.o(i.arm_cfft_q15) refers to transformfunctions.o(i.arm_radix4_butterfly_q15) for arm_radix4_butterfly_q15
    transformfunctions.o(i.arm_cfft_q15) refers to transformfunctions.o(i.arm_cfft_radix4by2_q15) for arm_cfft_radix4by2_q15
    transformfunctions.o(i.arm_cfft_q15) refers to transformfunctions.o(i.arm_bitreversal_16) for arm_bitreversal_16
    transformfunctions.o(i.arm_cfft_q31) refers to transformfunctions.o(i.arm_radix4_butterfly_inverse_q31) for arm_radix4_butterfly_inverse_q31
    transformfunctions.o(i.arm_cfft_q31) refers to transformfunctions.o(i.arm_cfft_radix4by2_inverse_q31) for arm_cfft_radix4by2_inverse_q31
    transformfunctions.o(i.arm_cfft_q31) refers to transformfunctions.o(i.arm_radix4_butterfly_q31) for arm_radix4_butterfly_q31
    transformfunctions.o(i.arm_cfft_q31) refers to transformfunctions.o(i.arm_cfft_radix4by2_q31) for arm_cfft_radix4by2_q31
    transformfunctions.o(i.arm_cfft_q31) refers to transformfunctions.o(i.arm_bitreversal_32) for arm_bitreversal_32
    transformfunctions.o(i.arm_cfft_radix2_f32) refers to transformfunctions.o(i.arm_radix2_butterfly_f32) for arm_radix2_butterfly_f32
    transformfunctions.o(i.arm_cfft_radix2_f32) refers to transformfunctions.o(i.arm_bitreversal_f32) for arm_bitreversal_f32
    transformfunctions.o(i.arm_cfft_radix2_f32) refers to transformfunctions.o(i.arm_radix2_butterfly_inverse_f32) for arm_radix2_butterfly_inverse_f32
    transformfunctions.o(i.arm_cfft_radix2_init_f32) refers to commontables.o(.constdata) for twiddleCoef_4096
    transformfunctions.o(i.arm_cfft_radix2_init_f32) refers to commontables.o(.constdata) for armBitRevTable
    transformfunctions.o(i.arm_cfft_radix2_init_q15) refers to commontables.o(.constdata) for twiddleCoef_4096_q15
    transformfunctions.o(i.arm_cfft_radix2_init_q15) refers to commontables.o(.constdata) for armBitRevTable
    transformfunctions.o(i.arm_cfft_radix2_init_q31) refers to commontables.o(.constdata) for twiddleCoef_4096_q31
    transformfunctions.o(i.arm_cfft_radix2_init_q31) refers to commontables.o(.constdata) for armBitRevTable
    transformfunctions.o(i.arm_cfft_radix2_q15) refers to transformfunctions.o(i.arm_radix2_butterfly_q15) for arm_radix2_butterfly_q15
    transformfunctions.o(i.arm_cfft_radix2_q15) refers to transformfunctions.o(i.arm_bitreversal_q15) for arm_bitreversal_q15
    transformfunctions.o(i.arm_cfft_radix2_q15) refers to transformfunctions.o(i.arm_radix2_butterfly_inverse_q15) for arm_radix2_butterfly_inverse_q15
    transformfunctions.o(i.arm_cfft_radix2_q31) refers to transformfunctions.o(i.arm_radix2_butterfly_q31) for arm_radix2_butterfly_q31
    transformfunctions.o(i.arm_cfft_radix2_q31) refers to transformfunctions.o(i.arm_bitreversal_q31) for arm_bitreversal_q31
    transformfunctions.o(i.arm_cfft_radix2_q31) refers to transformfunctions.o(i.arm_radix2_butterfly_inverse_q31) for arm_radix2_butterfly_inverse_q31
    transformfunctions.o(i.arm_cfft_radix4_f32) refers to transformfunctions.o(i.arm_radix4_butterfly_f32) for arm_radix4_butterfly_f32
    transformfunctions.o(i.arm_cfft_radix4_f32) refers to transformfunctions.o(i.arm_bitreversal_f32) for arm_bitreversal_f32
    transformfunctions.o(i.arm_cfft_radix4_f32) refers to transformfunctions.o(i.arm_radix4_butterfly_inverse_f32) for arm_radix4_butterfly_inverse_f32
    transformfunctions.o(i.arm_cfft_radix4_init_f32) refers to commontables.o(.constdata) for twiddleCoef_4096
    transformfunctions.o(i.arm_cfft_radix4_init_f32) refers to commontables.o(.constdata) for armBitRevTable
    transformfunctions.o(i.arm_cfft_radix4_init_q15) refers to commontables.o(.constdata) for twiddleCoef_4096_q15
    transformfunctions.o(i.arm_cfft_radix4_init_q15) refers to commontables.o(.constdata) for armBitRevTable
    transformfunctions.o(i.arm_cfft_radix4_init_q31) refers to commontables.o(.constdata) for twiddleCoef_4096_q31
    transformfunctions.o(i.arm_cfft_radix4_init_q31) refers to commontables.o(.constdata) for armBitRevTable
    transformfunctions.o(i.arm_cfft_radix4_q15) refers to transformfunctions.o(i.arm_radix4_butterfly_q15) for arm_radix4_butterfly_q15
    transformfunctions.o(i.arm_cfft_radix4_q15) refers to transformfunctions.o(i.arm_bitreversal_q15) for arm_bitreversal_q15
    transformfunctions.o(i.arm_cfft_radix4_q15) refers to transformfunctions.o(i.arm_radix4_butterfly_inverse_q15) for arm_radix4_butterfly_inverse_q15
    transformfunctions.o(i.arm_cfft_radix4_q31) refers to transformfunctions.o(i.arm_radix4_butterfly_q31) for arm_radix4_butterfly_q31
    transformfunctions.o(i.arm_cfft_radix4_q31) refers to transformfunctions.o(i.arm_bitreversal_q31) for arm_bitreversal_q31
    transformfunctions.o(i.arm_cfft_radix4_q31) refers to transformfunctions.o(i.arm_radix4_butterfly_inverse_q31) for arm_radix4_butterfly_inverse_q31
    transformfunctions.o(i.arm_cfft_radix4by2_f64) refers to dadd.o(.text) for __aeabi_dadd
    transformfunctions.o(i.arm_cfft_radix4by2_f64) refers to dmul.o(.text) for __aeabi_dmul
    transformfunctions.o(i.arm_cfft_radix4by2_f64) refers to transformfunctions.o(i.arm_radix4_butterfly_f64) for arm_radix4_butterfly_f64
    transformfunctions.o(i.arm_cfft_radix4by2_inverse_q15) refers to transformfunctions.o(i.arm_radix4_butterfly_inverse_q15) for arm_radix4_butterfly_inverse_q15
    transformfunctions.o(i.arm_cfft_radix4by2_inverse_q31) refers to transformfunctions.o(i.arm_radix4_butterfly_inverse_q31) for arm_radix4_butterfly_inverse_q31
    transformfunctions.o(i.arm_cfft_radix4by2_q15) refers to transformfunctions.o(i.arm_radix4_butterfly_q15) for arm_radix4_butterfly_q15
    transformfunctions.o(i.arm_cfft_radix4by2_q31) refers to transformfunctions.o(i.arm_radix4_butterfly_q31) for arm_radix4_butterfly_q31
    transformfunctions.o(i.arm_cfft_radix8by2_f32) refers to transformfunctions.o(i.arm_radix8_butterfly_f32) for arm_radix8_butterfly_f32
    transformfunctions.o(i.arm_cfft_radix8by4_f32) refers to transformfunctions.o(i.arm_radix8_butterfly_f32) for arm_radix8_butterfly_f32
    transformfunctions.o(i.arm_dct4_f32) refers to basicmathfunctions.o(i.arm_scale_f32) for arm_scale_f32
    transformfunctions.o(i.arm_dct4_f32) refers to basicmathfunctions.o(i.arm_mult_f32) for arm_mult_f32
    transformfunctions.o(i.arm_dct4_f32) refers to transformfunctions.o(i.arm_rfft_f32) for arm_rfft_f32
    transformfunctions.o(i.arm_dct4_f32) refers to complexmathfunctions.o(i.arm_cmplx_mult_cmplx_f32) for arm_cmplx_mult_cmplx_f32
    transformfunctions.o(i.arm_dct4_init_f32) refers to transformfunctions.o(i.arm_rfft_init_f32) for arm_rfft_init_f32
    transformfunctions.o(i.arm_dct4_init_f32) refers to commontables.o(.constdata) for Weights_8192
    transformfunctions.o(i.arm_dct4_init_f32) refers to commontables.o(.constdata) for cos_factors_8192
    transformfunctions.o(i.arm_dct4_init_f32) refers to commontables.o(.constdata) for Weights_2048
    transformfunctions.o(i.arm_dct4_init_f32) refers to commontables.o(.constdata) for cos_factors_2048
    transformfunctions.o(i.arm_dct4_init_f32) refers to commontables.o(.constdata) for Weights_512
    transformfunctions.o(i.arm_dct4_init_f32) refers to commontables.o(.constdata) for cos_factors_512
    transformfunctions.o(i.arm_dct4_init_f32) refers to commontables.o(.constdata) for Weights_128
    transformfunctions.o(i.arm_dct4_init_f32) refers to commontables.o(.constdata) for cos_factors_128
    transformfunctions.o(i.arm_dct4_init_q15) refers to transformfunctions.o(i.arm_rfft_init_q15) for arm_rfft_init_q15
    transformfunctions.o(i.arm_dct4_init_q15) refers to commontables.o(.constdata) for WeightsQ15_8192
    transformfunctions.o(i.arm_dct4_init_q15) refers to commontables.o(.constdata) for cos_factorsQ15_8192
    transformfunctions.o(i.arm_dct4_init_q15) refers to commontables.o(.constdata) for WeightsQ15_2048
    transformfunctions.o(i.arm_dct4_init_q15) refers to commontables.o(.constdata) for cos_factorsQ15_2048
    transformfunctions.o(i.arm_dct4_init_q15) refers to commontables.o(.constdata) for WeightsQ15_512
    transformfunctions.o(i.arm_dct4_init_q15) refers to commontables.o(.constdata) for cos_factorsQ15_512
    transformfunctions.o(i.arm_dct4_init_q15) refers to commontables.o(.constdata) for WeightsQ15_128
    transformfunctions.o(i.arm_dct4_init_q15) refers to commontables.o(.constdata) for cos_factorsQ15_128
    transformfunctions.o(i.arm_dct4_init_q31) refers to transformfunctions.o(i.arm_rfft_init_q31) for arm_rfft_init_q31
    transformfunctions.o(i.arm_dct4_init_q31) refers to commontables.o(.constdata) for WeightsQ31_8192
    transformfunctions.o(i.arm_dct4_init_q31) refers to commontables.o(.constdata) for cos_factorsQ31_8192
    transformfunctions.o(i.arm_dct4_init_q31) refers to commontables.o(.constdata) for WeightsQ31_2048
    transformfunctions.o(i.arm_dct4_init_q31) refers to commontables.o(.constdata) for cos_factorsQ31_2048
    transformfunctions.o(i.arm_dct4_init_q31) refers to commontables.o(.constdata) for WeightsQ31_512
    transformfunctions.o(i.arm_dct4_init_q31) refers to commontables.o(.constdata) for cos_factorsQ31_512
    transformfunctions.o(i.arm_dct4_init_q31) refers to commontables.o(.constdata) for WeightsQ31_128
    transformfunctions.o(i.arm_dct4_init_q31) refers to commontables.o(.constdata) for cos_factorsQ31_128
    transformfunctions.o(i.arm_dct4_q15) refers to basicmathfunctions.o(i.arm_mult_q15) for arm_mult_q15
    transformfunctions.o(i.arm_dct4_q15) refers to basicmathfunctions.o(i.arm_shift_q15) for arm_shift_q15
    transformfunctions.o(i.arm_dct4_q15) refers to transformfunctions.o(i.arm_rfft_q15) for arm_rfft_q15
    transformfunctions.o(i.arm_dct4_q15) refers to complexmathfunctions.o(i.arm_cmplx_mult_cmplx_q15) for arm_cmplx_mult_cmplx_q15
    transformfunctions.o(i.arm_dct4_q31) refers to basicmathfunctions.o(i.arm_mult_q31) for arm_mult_q31
    transformfunctions.o(i.arm_dct4_q31) refers to basicmathfunctions.o(i.arm_shift_q31) for arm_shift_q31
    transformfunctions.o(i.arm_dct4_q31) refers to transformfunctions.o(i.arm_rfft_q31) for arm_rfft_q31
    transformfunctions.o(i.arm_dct4_q31) refers to complexmathfunctions.o(i.arm_cmplx_mult_cmplx_q31) for arm_cmplx_mult_cmplx_q31
    transformfunctions.o(i.arm_mfcc_f32) refers to statisticsfunctions.o(i.arm_absmax_f32) for arm_absmax_f32
    transformfunctions.o(i.arm_mfcc_f32) refers to basicmathfunctions.o(i.arm_scale_f32) for arm_scale_f32
    transformfunctions.o(i.arm_mfcc_f32) refers to basicmathfunctions.o(i.arm_mult_f32) for arm_mult_f32
    transformfunctions.o(i.arm_mfcc_f32) refers to transformfunctions.o(i.arm_rfft_fast_f32) for arm_rfft_fast_f32
    transformfunctions.o(i.arm_mfcc_f32) refers to complexmathfunctions.o(i.arm_cmplx_mag_f32) for arm_cmplx_mag_f32
    transformfunctions.o(i.arm_mfcc_f32) refers to basicmathfunctions.o(i.arm_dot_prod_f32) for arm_dot_prod_f32
    transformfunctions.o(i.arm_mfcc_f32) refers to basicmathfunctions.o(i.arm_offset_f32) for arm_offset_f32
    transformfunctions.o(i.arm_mfcc_f32) refers to fastmathfunctions.o(i.arm_vlog_f32) for arm_vlog_f32
    transformfunctions.o(i.arm_mfcc_f32) refers to matrixfunctions.o(i.arm_mat_vec_mult_f32) for arm_mat_vec_mult_f32
    transformfunctions.o(i.arm_mfcc_init_f32) refers to transformfunctions.o(i.arm_rfft_fast_init_f32) for arm_rfft_fast_init_f32
    transformfunctions.o(i.arm_mfcc_init_q15) refers to transformfunctions.o(i.arm_rfft_init_q15) for arm_rfft_init_q15
    transformfunctions.o(i.arm_mfcc_init_q31) refers to transformfunctions.o(i.arm_rfft_init_q31) for arm_rfft_init_q31
    transformfunctions.o(i.arm_mfcc_q15) refers to statisticsfunctions.o(i.arm_absmax_q15) for arm_absmax_q15
    transformfunctions.o(i.arm_mfcc_q15) refers to fastmathfunctions.o(i.arm_divide_q15) for arm_divide_q15
    transformfunctions.o(i.arm_mfcc_q15) refers to basicmathfunctions.o(i.arm_scale_q15) for arm_scale_q15
    transformfunctions.o(i.arm_mfcc_q15) refers to basicmathfunctions.o(i.arm_mult_q15) for arm_mult_q15
    transformfunctions.o(i.arm_mfcc_q15) refers to transformfunctions.o(i.arm_rfft_q15) for arm_rfft_q15
    transformfunctions.o(i.arm_mfcc_q15) refers to complexmathfunctions.o(i.arm_cmplx_mag_q15) for arm_cmplx_mag_q15
    transformfunctions.o(i.arm_mfcc_q15) refers to basicmathfunctions.o(i.arm_dot_prod_q15) for arm_dot_prod_q15
    transformfunctions.o(i.arm_mfcc_q15) refers to fastmathfunctions.o(i.arm_vlog_q31) for arm_vlog_q31
    transformfunctions.o(i.arm_mfcc_q15) refers to basicmathfunctions.o(i.arm_offset_q31) for arm_offset_q31
    transformfunctions.o(i.arm_mfcc_q15) refers to basicmathfunctions.o(i.arm_shift_q31) for arm_shift_q31
    transformfunctions.o(i.arm_mfcc_q15) refers to matrixfunctions.o(i.arm_mat_vec_mult_q15) for arm_mat_vec_mult_q15
    transformfunctions.o(i.arm_mfcc_q31) refers to statisticsfunctions.o(i.arm_absmax_q31) for arm_absmax_q31
    transformfunctions.o(i.arm_mfcc_q31) refers to fastmathfunctions.o(i.arm_divide_q31) for arm_divide_q31
    transformfunctions.o(i.arm_mfcc_q31) refers to basicmathfunctions.o(i.arm_scale_q31) for arm_scale_q31
    transformfunctions.o(i.arm_mfcc_q31) refers to basicmathfunctions.o(i.arm_mult_q31) for arm_mult_q31
    transformfunctions.o(i.arm_mfcc_q31) refers to transformfunctions.o(i.arm_rfft_q31) for arm_rfft_q31
    transformfunctions.o(i.arm_mfcc_q31) refers to complexmathfunctions.o(i.arm_cmplx_mag_q31) for arm_cmplx_mag_q31
    transformfunctions.o(i.arm_mfcc_q31) refers to basicmathfunctions.o(i.arm_dot_prod_q31) for arm_dot_prod_q31
    transformfunctions.o(i.arm_mfcc_q31) refers to fastmathfunctions.o(i.arm_vlog_q31) for arm_vlog_q31
    transformfunctions.o(i.arm_mfcc_q31) refers to basicmathfunctions.o(i.arm_offset_q31) for arm_offset_q31
    transformfunctions.o(i.arm_mfcc_q31) refers to basicmathfunctions.o(i.arm_shift_q31) for arm_shift_q31
    transformfunctions.o(i.arm_mfcc_q31) refers to matrixfunctions.o(i.arm_mat_vec_mult_q31) for arm_mat_vec_mult_q31
    transformfunctions.o(i.arm_radix4_butterfly_f64) refers to dadd.o(.text) for __aeabi_dadd
    transformfunctions.o(i.arm_radix4_butterfly_f64) refers to dmul.o(.text) for __aeabi_dmul
    transformfunctions.o(i.arm_rfft_1024_fast_init_f32) refers to transformfunctions.o(i.arm_cfft_init_f32) for arm_cfft_init_f32
    transformfunctions.o(i.arm_rfft_1024_fast_init_f32) refers to commontables.o(.constdata) for twiddleCoef_rfft_1024
    transformfunctions.o(i.arm_rfft_1024_fast_init_f64) refers to commontables.o(.constdata) for armBitRevIndexTableF64_512
    transformfunctions.o(i.arm_rfft_1024_fast_init_f64) refers to commontables.o(.constdata) for twiddleCoefF64_512
    transformfunctions.o(i.arm_rfft_1024_fast_init_f64) refers to commontables.o(.constdata) for twiddleCoefF64_rfft_1024
    transformfunctions.o(i.arm_rfft_128_fast_init_f32) refers to transformfunctions.o(i.arm_cfft_init_f32) for arm_cfft_init_f32
    transformfunctions.o(i.arm_rfft_128_fast_init_f32) refers to commontables.o(.constdata) for twiddleCoef_rfft_128
    transformfunctions.o(i.arm_rfft_128_fast_init_f64) refers to commontables.o(.constdata) for armBitRevIndexTableF64_64
    transformfunctions.o(i.arm_rfft_128_fast_init_f64) refers to commontables.o(.constdata) for twiddleCoefF64_64
    transformfunctions.o(i.arm_rfft_128_fast_init_f64) refers to commontables.o(.constdata) for twiddleCoefF64_rfft_128
    transformfunctions.o(i.arm_rfft_2048_fast_init_f32) refers to transformfunctions.o(i.arm_cfft_init_f32) for arm_cfft_init_f32
    transformfunctions.o(i.arm_rfft_2048_fast_init_f32) refers to commontables.o(.constdata) for twiddleCoef_rfft_2048
    transformfunctions.o(i.arm_rfft_2048_fast_init_f64) refers to commontables.o(.constdata) for armBitRevIndexTableF64_1024
    transformfunctions.o(i.arm_rfft_2048_fast_init_f64) refers to commontables.o(.constdata) for twiddleCoefF64_1024
    transformfunctions.o(i.arm_rfft_2048_fast_init_f64) refers to commontables.o(.constdata) for twiddleCoefF64_rfft_2048
    transformfunctions.o(i.arm_rfft_256_fast_init_f32) refers to transformfunctions.o(i.arm_cfft_init_f32) for arm_cfft_init_f32
    transformfunctions.o(i.arm_rfft_256_fast_init_f32) refers to commontables.o(.constdata) for twiddleCoef_rfft_256
    transformfunctions.o(i.arm_rfft_256_fast_init_f64) refers to commontables.o(.constdata) for armBitRevIndexTableF64_128
    transformfunctions.o(i.arm_rfft_256_fast_init_f64) refers to commontables.o(.constdata) for twiddleCoefF64_128
    transformfunctions.o(i.arm_rfft_256_fast_init_f64) refers to commontables.o(.constdata) for twiddleCoefF64_rfft_256
    transformfunctions.o(i.arm_rfft_32_fast_init_f32) refers to transformfunctions.o(i.arm_cfft_init_f32) for arm_cfft_init_f32
    transformfunctions.o(i.arm_rfft_32_fast_init_f32) refers to commontables.o(.constdata) for twiddleCoef_rfft_32
    transformfunctions.o(i.arm_rfft_32_fast_init_f64) refers to commontables.o(.constdata) for armBitRevIndexTableF64_16
    transformfunctions.o(i.arm_rfft_32_fast_init_f64) refers to commontables.o(.constdata) for twiddleCoefF64_16
    transformfunctions.o(i.arm_rfft_32_fast_init_f64) refers to commontables.o(.constdata) for twiddleCoefF64_rfft_32
    transformfunctions.o(i.arm_rfft_4096_fast_init_f32) refers to transformfunctions.o(i.arm_cfft_init_f32) for arm_cfft_init_f32
    transformfunctions.o(i.arm_rfft_4096_fast_init_f32) refers to commontables.o(.constdata) for twiddleCoef_rfft_4096
    transformfunctions.o(i.arm_rfft_4096_fast_init_f64) refers to commontables.o(.constdata) for armBitRevIndexTableF64_2048
    transformfunctions.o(i.arm_rfft_4096_fast_init_f64) refers to commontables.o(.constdata) for twiddleCoefF64_2048
    transformfunctions.o(i.arm_rfft_4096_fast_init_f64) refers to commontables.o(.constdata) for twiddleCoefF64_rfft_4096
    transformfunctions.o(i.arm_rfft_512_fast_init_f32) refers to transformfunctions.o(i.arm_cfft_init_f32) for arm_cfft_init_f32
    transformfunctions.o(i.arm_rfft_512_fast_init_f32) refers to commontables.o(.constdata) for twiddleCoef_rfft_512
    transformfunctions.o(i.arm_rfft_512_fast_init_f64) refers to commontables.o(.constdata) for armBitRevIndexTableF64_256
    transformfunctions.o(i.arm_rfft_512_fast_init_f64) refers to commontables.o(.constdata) for twiddleCoefF64_256
    transformfunctions.o(i.arm_rfft_512_fast_init_f64) refers to commontables.o(.constdata) for twiddleCoefF64_rfft_512
    transformfunctions.o(i.arm_rfft_64_fast_init_f32) refers to transformfunctions.o(i.arm_cfft_init_f32) for arm_cfft_init_f32
    transformfunctions.o(i.arm_rfft_64_fast_init_f32) refers to commontables.o(.constdata) for twiddleCoef_rfft_64
    transformfunctions.o(i.arm_rfft_64_fast_init_f64) refers to commontables.o(.constdata) for armBitRevIndexTableF64_32
    transformfunctions.o(i.arm_rfft_64_fast_init_f64) refers to commontables.o(.constdata) for twiddleCoefF64_32
    transformfunctions.o(i.arm_rfft_64_fast_init_f64) refers to commontables.o(.constdata) for twiddleCoefF64_rfft_64
    transformfunctions.o(i.arm_rfft_f32) refers to transformfunctions.o(i.arm_radix4_butterfly_f32) for arm_radix4_butterfly_f32
    transformfunctions.o(i.arm_rfft_f32) refers to transformfunctions.o(i.arm_split_rifft_f32) for arm_split_rifft_f32
    transformfunctions.o(i.arm_rfft_f32) refers to transformfunctions.o(i.arm_radix4_butterfly_inverse_f32) for arm_radix4_butterfly_inverse_f32
    transformfunctions.o(i.arm_rfft_f32) refers to transformfunctions.o(i.arm_bitreversal_f32) for arm_bitreversal_f32
    transformfunctions.o(i.arm_rfft_f32) refers to transformfunctions.o(i.arm_split_rfft_f32) for arm_split_rfft_f32
    transformfunctions.o(i.arm_rfft_fast_f32) refers to transformfunctions.o(i.merge_rfft_f32) for merge_rfft_f32
    transformfunctions.o(i.arm_rfft_fast_f32) refers to transformfunctions.o(i.arm_cfft_f32) for arm_cfft_f32
    transformfunctions.o(i.arm_rfft_fast_f32) refers to transformfunctions.o(i.stage_rfft_f32) for stage_rfft_f32
    transformfunctions.o(i.arm_rfft_fast_f64) refers to transformfunctions.o(i.merge_rfft_f64) for merge_rfft_f64
    transformfunctions.o(i.arm_rfft_fast_f64) refers to transformfunctions.o(i.arm_cfft_f64) for arm_cfft_f64
    transformfunctions.o(i.arm_rfft_fast_f64) refers to transformfunctions.o(i.stage_rfft_f64) for stage_rfft_f64
    transformfunctions.o(i.arm_rfft_fast_init_f32) refers to transformfunctions.o(i.arm_rfft_4096_fast_init_f32) for arm_rfft_4096_fast_init_f32
    transformfunctions.o(i.arm_rfft_fast_init_f32) refers to transformfunctions.o(i.arm_rfft_2048_fast_init_f32) for arm_rfft_2048_fast_init_f32
    transformfunctions.o(i.arm_rfft_fast_init_f32) refers to transformfunctions.o(i.arm_rfft_1024_fast_init_f32) for arm_rfft_1024_fast_init_f32
    transformfunctions.o(i.arm_rfft_fast_init_f32) refers to transformfunctions.o(i.arm_rfft_512_fast_init_f32) for arm_rfft_512_fast_init_f32
    transformfunctions.o(i.arm_rfft_fast_init_f32) refers to transformfunctions.o(i.arm_rfft_256_fast_init_f32) for arm_rfft_256_fast_init_f32
    transformfunctions.o(i.arm_rfft_fast_init_f32) refers to transformfunctions.o(i.arm_rfft_128_fast_init_f32) for arm_rfft_128_fast_init_f32
    transformfunctions.o(i.arm_rfft_fast_init_f32) refers to transformfunctions.o(i.arm_rfft_64_fast_init_f32) for arm_rfft_64_fast_init_f32
    transformfunctions.o(i.arm_rfft_fast_init_f32) refers to transformfunctions.o(i.arm_rfft_32_fast_init_f32) for arm_rfft_32_fast_init_f32
    transformfunctions.o(i.arm_rfft_fast_init_f64) refers to transformfunctions.o(i.arm_rfft_4096_fast_init_f64) for arm_rfft_4096_fast_init_f64
    transformfunctions.o(i.arm_rfft_fast_init_f64) refers to transformfunctions.o(i.arm_rfft_2048_fast_init_f64) for arm_rfft_2048_fast_init_f64
    transformfunctions.o(i.arm_rfft_fast_init_f64) refers to transformfunctions.o(i.arm_rfft_1024_fast_init_f64) for arm_rfft_1024_fast_init_f64
    transformfunctions.o(i.arm_rfft_fast_init_f64) refers to transformfunctions.o(i.arm_rfft_512_fast_init_f64) for arm_rfft_512_fast_init_f64
    transformfunctions.o(i.arm_rfft_fast_init_f64) refers to transformfunctions.o(i.arm_rfft_256_fast_init_f64) for arm_rfft_256_fast_init_f64
    transformfunctions.o(i.arm_rfft_fast_init_f64) refers to transformfunctions.o(i.arm_rfft_128_fast_init_f64) for arm_rfft_128_fast_init_f64
    transformfunctions.o(i.arm_rfft_fast_init_f64) refers to transformfunctions.o(i.arm_rfft_64_fast_init_f64) for arm_rfft_64_fast_init_f64
    transformfunctions.o(i.arm_rfft_fast_init_f64) refers to transformfunctions.o(i.arm_rfft_32_fast_init_f64) for arm_rfft_32_fast_init_f64
    transformfunctions.o(i.arm_rfft_init_f32) refers to transformfunctions.o(i.arm_cfft_radix4_init_f32) for arm_cfft_radix4_init_f32
    transformfunctions.o(i.arm_rfft_init_f32) refers to commontables.o(.constdata) for realCoefA
    transformfunctions.o(i.arm_rfft_init_f32) refers to commontables.o(.constdata) for realCoefB
    transformfunctions.o(i.arm_rfft_init_q15) refers to commontables.o(.constdata) for realCoefAQ15
    transformfunctions.o(i.arm_rfft_init_q15) refers to commontables.o(.constdata) for realCoefBQ15
    transformfunctions.o(i.arm_rfft_init_q15) refers to commontables.o(.constdata) for arm_cfft_sR_q15_len128
    transformfunctions.o(i.arm_rfft_init_q15) refers to commontables.o(.constdata) for arm_cfft_sR_q15_len4096
    transformfunctions.o(i.arm_rfft_init_q15) refers to commontables.o(.constdata) for arm_cfft_sR_q15_len2048
    transformfunctions.o(i.arm_rfft_init_q15) refers to commontables.o(.constdata) for arm_cfft_sR_q15_len1024
    transformfunctions.o(i.arm_rfft_init_q15) refers to commontables.o(.constdata) for arm_cfft_sR_q15_len512
    transformfunctions.o(i.arm_rfft_init_q15) refers to commontables.o(.constdata) for arm_cfft_sR_q15_len256
    transformfunctions.o(i.arm_rfft_init_q15) refers to commontables.o(.constdata) for arm_cfft_sR_q15_len64
    transformfunctions.o(i.arm_rfft_init_q15) refers to commontables.o(.constdata) for arm_cfft_sR_q15_len32
    transformfunctions.o(i.arm_rfft_init_q15) refers to commontables.o(.constdata) for arm_cfft_sR_q15_len16
    transformfunctions.o(i.arm_rfft_init_q31) refers to commontables.o(.constdata) for realCoefAQ31
    transformfunctions.o(i.arm_rfft_init_q31) refers to commontables.o(.constdata) for realCoefBQ31
    transformfunctions.o(i.arm_rfft_init_q31) refers to commontables.o(.constdata) for arm_cfft_sR_q31_len128
    transformfunctions.o(i.arm_rfft_init_q31) refers to commontables.o(.constdata) for arm_cfft_sR_q31_len4096
    transformfunctions.o(i.arm_rfft_init_q31) refers to commontables.o(.constdata) for arm_cfft_sR_q31_len2048
    transformfunctions.o(i.arm_rfft_init_q31) refers to commontables.o(.constdata) for arm_cfft_sR_q31_len1024
    transformfunctions.o(i.arm_rfft_init_q31) refers to commontables.o(.constdata) for arm_cfft_sR_q31_len512
    transformfunctions.o(i.arm_rfft_init_q31) refers to commontables.o(.constdata) for arm_cfft_sR_q31_len256
    transformfunctions.o(i.arm_rfft_init_q31) refers to commontables.o(.constdata) for arm_cfft_sR_q31_len64
    transformfunctions.o(i.arm_rfft_init_q31) refers to commontables.o(.constdata) for arm_cfft_sR_q31_len32
    transformfunctions.o(i.arm_rfft_init_q31) refers to commontables.o(.constdata) for arm_cfft_sR_q31_len16
    transformfunctions.o(i.arm_rfft_q15) refers to transformfunctions.o(i.arm_cfft_q15) for arm_cfft_q15
    transformfunctions.o(i.arm_rfft_q15) refers to transformfunctions.o(i.arm_split_rfft_q15) for arm_split_rfft_q15
    transformfunctions.o(i.arm_rfft_q15) refers to transformfunctions.o(i.arm_split_rifft_q15) for arm_split_rifft_q15
    transformfunctions.o(i.arm_rfft_q15) refers to basicmathfunctions.o(i.arm_shift_q15) for arm_shift_q15
    transformfunctions.o(i.arm_rfft_q31) refers to transformfunctions.o(i.arm_cfft_q31) for arm_cfft_q31
    transformfunctions.o(i.arm_rfft_q31) refers to transformfunctions.o(i.arm_split_rfft_q31) for arm_split_rfft_q31
    transformfunctions.o(i.arm_rfft_q31) refers to transformfunctions.o(i.arm_split_rifft_q31) for arm_split_rifft_q31
    transformfunctions.o(i.arm_rfft_q31) refers to basicmathfunctions.o(i.arm_shift_q31) for arm_shift_q31
    transformfunctions.o(i.merge_rfft_f64) refers to dadd.o(.text) for __aeabi_dadd
    transformfunctions.o(i.merge_rfft_f64) refers to dmul.o(.text) for __aeabi_dmul
    transformfunctions.o(i.stage_rfft_f64) refers to dadd.o(.text) for __aeabi_dadd
    transformfunctions.o(i.stage_rfft_f64) refers to dmul.o(.text) for __aeabi_dmul
    cosf.o(i.__hardfp_cosf) refers (Special) to iusefp.o(.text) for __I$use$fp
    cosf.o(i.__hardfp_cosf) refers to rredf.o(i.__mathlib_rredf2) for __mathlib_rredf2
    cosf.o(i.__hardfp_cosf) refers to errno.o(i.__set_errno) for __set_errno
    cosf.o(i.__hardfp_cosf) refers to funder.o(i.__mathlib_flt_invalid) for __mathlib_flt_invalid
    cosf.o(i.__hardfp_cosf) refers to funder.o(i.__mathlib_flt_infnan) for __mathlib_flt_infnan
    cosf.o(i.__softfp_cosf) refers (Special) to iusefp.o(.text) for __I$use$fp
    cosf.o(i.__softfp_cosf) refers to cosf.o(i.__hardfp_cosf) for __hardfp_cosf
    cosf.o(i.cosf) refers (Special) to iusefp.o(.text) for __I$use$fp
    cosf.o(i.cosf) refers to cosf.o(i.__hardfp_cosf) for __hardfp_cosf
    cosf_x.o(i.____hardfp_cosf$lsc) refers (Special) to iusefp.o(.text) for __I$use$fp
    cosf_x.o(i.____hardfp_cosf$lsc) refers to rredf.o(i.__mathlib_rredf2) for __mathlib_rredf2
    cosf_x.o(i.____hardfp_cosf$lsc) refers to errno.o(i.__set_errno) for __set_errno
    cosf_x.o(i.____hardfp_cosf$lsc) refers to funder.o(i.__mathlib_flt_infnan) for __mathlib_flt_infnan
    cosf_x.o(i.____softfp_cosf$lsc) refers (Special) to iusefp.o(.text) for __I$use$fp
    cosf_x.o(i.____softfp_cosf$lsc) refers to cosf_x.o(i.____hardfp_cosf$lsc) for ____hardfp_cosf$lsc
    cosf_x.o(i.__cosf$lsc) refers (Special) to iusefp.o(.text) for __I$use$fp
    cosf_x.o(i.__cosf$lsc) refers to cosf_x.o(i.____hardfp_cosf$lsc) for ____hardfp_cosf$lsc
    exp.o(i.__hardfp_exp) refers (Special) to iusefp.o(.text) for __I$use$fp
    exp.o(i.__hardfp_exp) refers to dunder.o(i.__mathlib_dbl_infnan) for __mathlib_dbl_infnan
    exp.o(i.__hardfp_exp) refers to cdrcmple.o(.text) for __aeabi_cdrcmple
    exp.o(i.__hardfp_exp) refers to errno.o(i.__set_errno) for __set_errno
    exp.o(i.__hardfp_exp) refers to dunder.o(i.__mathlib_dbl_overflow) for __mathlib_dbl_overflow
    exp.o(i.__hardfp_exp) refers to cdcmple.o(.text) for __aeabi_cdcmple
    exp.o(i.__hardfp_exp) refers to dunder.o(i.__mathlib_dbl_underflow) for __mathlib_dbl_underflow
    exp.o(i.__hardfp_exp) refers to dadd.o(.text) for __aeabi_dsub
    exp.o(i.__hardfp_exp) refers to dmul.o(.text) for __aeabi_dmul
    exp.o(i.__hardfp_exp) refers to dfixi.o(.text) for __aeabi_d2iz
    exp.o(i.__hardfp_exp) refers to dflti.o(.text) for __aeabi_i2d
    exp.o(i.__hardfp_exp) refers to poly.o(i.__kernel_poly) for __kernel_poly
    exp.o(i.__hardfp_exp) refers to ddiv.o(.text) for __aeabi_ddiv
    exp.o(i.__hardfp_exp) refers to fpclassify.o(i.__ARM_fpclassify) for __ARM_fpclassify
    exp.o(i.__hardfp_exp) refers to exp.o(.constdata) for .constdata
    exp.o(i.__softfp_exp) refers (Special) to iusefp.o(.text) for __I$use$fp
    exp.o(i.__softfp_exp) refers to exp.o(i.__hardfp_exp) for __hardfp_exp
    exp.o(i.exp) refers (Special) to iusefp.o(.text) for __I$use$fp
    exp.o(i.exp) refers to exp.o(i.__hardfp_exp) for __hardfp_exp
    exp.o(.constdata) refers (Special) to iusefp.o(.text) for __I$use$fp
    exp_x.o(i.____hardfp_exp$lsc) refers (Special) to iusefp.o(.text) for __I$use$fp
    exp_x.o(i.____hardfp_exp$lsc) refers to dunder.o(i.__mathlib_dbl_infnan) for __mathlib_dbl_infnan
    exp_x.o(i.____hardfp_exp$lsc) refers to cdrcmple.o(.text) for __aeabi_cdrcmple
    exp_x.o(i.____hardfp_exp$lsc) refers to errno.o(i.__set_errno) for __set_errno
    exp_x.o(i.____hardfp_exp$lsc) refers to cdcmple.o(.text) for __aeabi_cdcmple
    exp_x.o(i.____hardfp_exp$lsc) refers to dadd.o(.text) for __aeabi_dsub
    exp_x.o(i.____hardfp_exp$lsc) refers to dmul.o(.text) for __aeabi_dmul
    exp_x.o(i.____hardfp_exp$lsc) refers to dfixi.o(.text) for __aeabi_d2iz
    exp_x.o(i.____hardfp_exp$lsc) refers to dflti.o(.text) for __aeabi_i2d
    exp_x.o(i.____hardfp_exp$lsc) refers to poly.o(i.__kernel_poly) for __kernel_poly
    exp_x.o(i.____hardfp_exp$lsc) refers to ddiv.o(.text) for __aeabi_ddiv
    exp_x.o(i.____hardfp_exp$lsc) refers to qnan.o(.constdata) for __mathlib_zero
    exp_x.o(i.____hardfp_exp$lsc) refers to exp_x.o(.constdata) for .constdata
    exp_x.o(i.____softfp_exp$lsc) refers (Special) to iusefp.o(.text) for __I$use$fp
    exp_x.o(i.____softfp_exp$lsc) refers to exp_x.o(i.____hardfp_exp$lsc) for ____hardfp_exp$lsc
    exp_x.o(i.__exp$lsc) refers (Special) to iusefp.o(.text) for __I$use$fp
    exp_x.o(i.__exp$lsc) refers to exp_x.o(i.____hardfp_exp$lsc) for ____hardfp_exp$lsc
    exp_x.o(.constdata) refers (Special) to iusefp.o(.text) for __I$use$fp
    expf.o(i.__hardfp_expf) refers (Special) to iusefp.o(.text) for __I$use$fp
    expf.o(i.__hardfp_expf) refers to errno.o(i.__set_errno) for __set_errno
    expf.o(i.__hardfp_expf) refers to funder.o(i.__mathlib_flt_underflow) for __mathlib_flt_underflow
    expf.o(i.__hardfp_expf) refers to funder.o(i.__mathlib_flt_infnan) for __mathlib_flt_infnan
    expf.o(i.__hardfp_expf) refers to funder.o(i.__mathlib_flt_overflow) for __mathlib_flt_overflow
    expf.o(i.__hardfp_expf) refers to fpclassifyf.o(i.__ARM_fpclassifyf) for __ARM_fpclassifyf
    expf.o(i.__hardfp_expf) refers to expf.o(.constdata) for .constdata
    expf.o(i.__softfp_expf) refers (Special) to iusefp.o(.text) for __I$use$fp
    expf.o(i.__softfp_expf) refers to expf.o(i.__hardfp_expf) for __hardfp_expf
    expf.o(i.expf) refers (Special) to iusefp.o(.text) for __I$use$fp
    expf.o(i.expf) refers to expf.o(i.__hardfp_expf) for __hardfp_expf
    expf.o(.constdata) refers (Special) to iusefp.o(.text) for __I$use$fp
    expf_x.o(i.____hardfp_expf$lsc) refers (Special) to iusefp.o(.text) for __I$use$fp
    expf_x.o(i.____hardfp_expf$lsc) refers to errno.o(i.__set_errno) for __set_errno
    expf_x.o(i.____hardfp_expf$lsc) refers to funder.o(i.__mathlib_flt_infnan) for __mathlib_flt_infnan
    expf_x.o(i.____hardfp_expf$lsc) refers to fpstat.o(.text) for __ieee_status
    expf_x.o(i.____hardfp_expf$lsc) refers to expf_x.o(.constdata) for .constdata
    expf_x.o(i.____softfp_expf$lsc) refers (Special) to iusefp.o(.text) for __I$use$fp
    expf_x.o(i.____softfp_expf$lsc) refers to expf_x.o(i.____hardfp_expf$lsc) for ____hardfp_expf$lsc
    expf_x.o(i.__expf$lsc) refers (Special) to iusefp.o(.text) for __I$use$fp
    expf_x.o(i.__expf$lsc) refers to expf_x.o(i.____hardfp_expf$lsc) for ____hardfp_expf$lsc
    expf_x.o(.constdata) refers (Special) to iusefp.o(.text) for __I$use$fp
    fabs.o(i.__hardfp_fabs) refers (Special) to iusefp.o(.text) for __I$use$fp
    fabs.o(i.__softfp_fabs) refers (Special) to iusefp.o(.text) for __I$use$fp
    fabs.o(i.fabs) refers (Special) to iusefp.o(.text) for __I$use$fp
    fmodf.o(i.__hardfp_fmodf) refers (Special) to iusefp.o(.text) for __I$use$fp
    fmodf.o(i.__hardfp_fmodf) refers to frem.o(.text) for _frem
    fmodf.o(i.__hardfp_fmodf) refers to errno.o(i.__set_errno) for __set_errno
    fmodf.o(i.__hardfp_fmodf) refers to funder.o(i.__mathlib_flt_invalid) for __mathlib_flt_invalid
    fmodf.o(i.__softfp_fmodf) refers (Special) to iusefp.o(.text) for __I$use$fp
    fmodf.o(i.__softfp_fmodf) refers to fmodf.o(i.__hardfp_fmodf) for __hardfp_fmodf
    fmodf.o(i.fmodf) refers (Special) to iusefp.o(.text) for __I$use$fp
    fmodf.o(i.fmodf) refers to fmodf.o(i.__hardfp_fmodf) for __hardfp_fmodf
    fmodf_x.o(i.____hardfp_fmodf$lsc) refers (Special) to iusefp.o(.text) for __I$use$fp
    fmodf_x.o(i.____hardfp_fmodf$lsc) refers to frem.o(.text) for _frem
    fmodf_x.o(i.____hardfp_fmodf$lsc) refers to errno.o(i.__set_errno) for __set_errno
    fmodf_x.o(i.____softfp_fmodf$lsc) refers (Special) to iusefp.o(.text) for __I$use$fp
    fmodf_x.o(i.____softfp_fmodf$lsc) refers to fmodf_x.o(i.____hardfp_fmodf$lsc) for ____hardfp_fmodf$lsc
    fmodf_x.o(i.__fmodf$lsc) refers (Special) to iusefp.o(.text) for __I$use$fp
    fmodf_x.o(i.__fmodf$lsc) refers to fmodf_x.o(i.____hardfp_fmodf$lsc) for ____hardfp_fmodf$lsc
    log.o(i.__hardfp_log) refers (Special) to iusefp.o(.text) for __I$use$fp
    log.o(i.__hardfp_log) refers to dunder.o(i.__mathlib_dbl_infnan) for __mathlib_dbl_infnan
    log.o(i.__hardfp_log) refers to errno.o(i.__set_errno) for __set_errno
    log.o(i.__hardfp_log) refers to dunder.o(i.__mathlib_dbl_invalid) for __mathlib_dbl_invalid
    log.o(i.__hardfp_log) refers to dunder.o(i.__mathlib_dbl_divzero) for __mathlib_dbl_divzero
    log.o(i.__hardfp_log) refers to dmul.o(.text) for __aeabi_dmul
    log.o(i.__hardfp_log) refers to dadd.o(.text) for __aeabi_dsub
    log.o(i.__hardfp_log) refers to cdcmple.o(.text) for __aeabi_cdcmpeq
    log.o(i.__hardfp_log) refers to dflti.o(.text) for __aeabi_i2d
    log.o(i.__hardfp_log) refers to ddiv.o(.text) for __aeabi_ddiv
    log.o(i.__hardfp_log) refers to poly.o(i.__kernel_poly) for __kernel_poly
    log.o(i.__hardfp_log) refers to qnan.o(.constdata) for __mathlib_zero
    log.o(i.__hardfp_log) refers to log.o(.constdata) for .constdata
    log.o(i.__softfp_log) refers (Special) to iusefp.o(.text) for __I$use$fp
    log.o(i.__softfp_log) refers to log.o(i.__hardfp_log) for __hardfp_log
    log.o(i.log) refers (Special) to iusefp.o(.text) for __I$use$fp
    log.o(i.log) refers to log.o(i.__hardfp_log) for __hardfp_log
    log.o(.constdata) refers (Special) to iusefp.o(.text) for __I$use$fp
    log_x.o(i.____hardfp_log$lsc) refers (Special) to iusefp.o(.text) for __I$use$fp
    log_x.o(i.____hardfp_log$lsc) refers to dunder.o(i.__mathlib_dbl_infnan) for __mathlib_dbl_infnan
    log_x.o(i.____hardfp_log$lsc) refers to errno.o(i.__set_errno) for __set_errno
    log_x.o(i.____hardfp_log$lsc) refers to dmul.o(.text) for __aeabi_dmul
    log_x.o(i.____hardfp_log$lsc) refers to dadd.o(.text) for __aeabi_dsub
    log_x.o(i.____hardfp_log$lsc) refers to cdcmple.o(.text) for __aeabi_cdcmpeq
    log_x.o(i.____hardfp_log$lsc) refers to dflti.o(.text) for __aeabi_i2d
    log_x.o(i.____hardfp_log$lsc) refers to ddiv.o(.text) for __aeabi_ddiv
    log_x.o(i.____hardfp_log$lsc) refers to poly.o(i.__kernel_poly) for __kernel_poly
    log_x.o(i.____hardfp_log$lsc) refers to qnan.o(.constdata) for __mathlib_zero
    log_x.o(i.____hardfp_log$lsc) refers to log_x.o(.constdata) for .constdata
    log_x.o(i.____softfp_log$lsc) refers (Special) to iusefp.o(.text) for __I$use$fp
    log_x.o(i.____softfp_log$lsc) refers to log_x.o(i.____hardfp_log$lsc) for ____hardfp_log$lsc
    log_x.o(i.__log$lsc) refers (Special) to iusefp.o(.text) for __I$use$fp
    log_x.o(i.__log$lsc) refers to log_x.o(i.____hardfp_log$lsc) for ____hardfp_log$lsc
    log_x.o(.constdata) refers (Special) to iusefp.o(.text) for __I$use$fp
    logf.o(i.__hardfp_logf) refers (Special) to iusefp.o(.text) for __I$use$fp
    logf.o(i.__hardfp_logf) refers to funder.o(i.__mathlib_flt_infnan) for __mathlib_flt_infnan
    logf.o(i.__hardfp_logf) refers to errno.o(i.__set_errno) for __set_errno
    logf.o(i.__hardfp_logf) refers to funder.o(i.__mathlib_flt_invalid) for __mathlib_flt_invalid
    logf.o(i.__hardfp_logf) refers to funder.o(i.__mathlib_flt_divzero) for __mathlib_flt_divzero
    logf.o(i.__hardfp_logf) refers to logf.o(.constdata) for .constdata
    logf.o(i.__softfp_logf) refers (Special) to iusefp.o(.text) for __I$use$fp
    logf.o(i.__softfp_logf) refers to logf.o(i.__hardfp_logf) for __hardfp_logf
    logf.o(i.logf) refers (Special) to iusefp.o(.text) for __I$use$fp
    logf.o(i.logf) refers to logf.o(i.__hardfp_logf) for __hardfp_logf
    logf.o(.constdata) refers (Special) to iusefp.o(.text) for __I$use$fp
    logf_x.o(i.____hardfp_logf$lsc) refers (Special) to iusefp.o(.text) for __I$use$fp
    logf_x.o(i.____hardfp_logf$lsc) refers to funder.o(i.__mathlib_flt_infnan) for __mathlib_flt_infnan
    logf_x.o(i.____hardfp_logf$lsc) refers to errno.o(i.__set_errno) for __set_errno
    logf_x.o(i.____hardfp_logf$lsc) refers to logf_x.o(.constdata) for .constdata
    logf_x.o(i.____softfp_logf$lsc) refers (Special) to iusefp.o(.text) for __I$use$fp
    logf_x.o(i.____softfp_logf$lsc) refers to logf_x.o(i.____hardfp_logf$lsc) for ____hardfp_logf$lsc
    logf_x.o(i.__logf$lsc) refers (Special) to iusefp.o(.text) for __I$use$fp
    logf_x.o(i.__logf$lsc) refers to logf_x.o(i.____hardfp_logf$lsc) for ____hardfp_logf$lsc
    logf_x.o(.constdata) refers (Special) to iusefp.o(.text) for __I$use$fp
    pow.o(i.__hardfp_pow) refers (Special) to iusefp.o(.text) for __I$use$fp
    pow.o(i.__hardfp_pow) refers to dunder.o(i.__mathlib_dbl_infnan2) for __mathlib_dbl_infnan2
    pow.o(i.__hardfp_pow) refers to errno.o(i.__set_errno) for __set_errno
    pow.o(i.__hardfp_pow) refers to dunder.o(i.__mathlib_dbl_divzero) for __mathlib_dbl_divzero
    pow.o(i.__hardfp_pow) refers to dunder.o(i.__mathlib_dbl_overflow) for __mathlib_dbl_overflow
    pow.o(i.__hardfp_pow) refers to ddiv.o(.text) for __aeabi_ddiv
    pow.o(i.__hardfp_pow) refers to sqrt.o(i.sqrt) for sqrt
    pow.o(i.__hardfp_pow) refers to fabs.o(i.fabs) for fabs
    pow.o(i.__hardfp_pow) refers to dflti.o(.text) for __aeabi_i2d
    pow.o(i.__hardfp_pow) refers to dunder.o(i.__mathlib_dbl_invalid) for __mathlib_dbl_invalid
    pow.o(i.__hardfp_pow) refers to dmul.o(.text) for __aeabi_dmul
    pow.o(i.__hardfp_pow) refers to dunder.o(i.__mathlib_dbl_underflow) for __mathlib_dbl_underflow
    pow.o(i.__hardfp_pow) refers to dadd.o(.text) for __aeabi_dsub
    pow.o(i.__hardfp_pow) refers to qnan.o(.constdata) for __mathlib_zero
    pow.o(i.__hardfp_pow) refers to poly.o(i.__kernel_poly) for __kernel_poly
    pow.o(i.__hardfp_pow) refers to pow.o(.constdata) for .constdata
    pow.o(i.__hardfp_pow) refers to cdrcmple.o(.text) for __aeabi_cdrcmple
    pow.o(i.__hardfp_pow) refers to dscalb.o(.text) for __ARM_scalbn
    pow.o(i.__hardfp_pow) refers to fpclassify.o(i.__ARM_fpclassify) for __ARM_fpclassify
    pow.o(i.__softfp_pow) refers (Special) to iusefp.o(.text) for __I$use$fp
    pow.o(i.__softfp_pow) refers to pow.o(i.__hardfp_pow) for __hardfp_pow
    pow.o(i.pow) refers (Special) to iusefp.o(.text) for __I$use$fp
    pow.o(i.pow) refers to pow.o(i.__hardfp_pow) for __hardfp_pow
    pow.o(.constdata) refers (Special) to iusefp.o(.text) for __I$use$fp
    pow_x.o(i.____hardfp_pow$lsc) refers (Special) to iusefp.o(.text) for __I$use$fp
    pow_x.o(i.____hardfp_pow$lsc) refers to dunder.o(i.__mathlib_dbl_infnan2) for __mathlib_dbl_infnan2
    pow_x.o(i.____hardfp_pow$lsc) refers to errno.o(i.__set_errno) for __set_errno
    pow_x.o(i.____hardfp_pow$lsc) refers to ddiv.o(.text) for __aeabi_ddiv
    pow_x.o(i.____hardfp_pow$lsc) refers to sqrt.o(i.sqrt) for sqrt
    pow_x.o(i.____hardfp_pow$lsc) refers to fabs.o(i.fabs) for fabs
    pow_x.o(i.____hardfp_pow$lsc) refers to dflti.o(.text) for __aeabi_i2d
    pow_x.o(i.____hardfp_pow$lsc) refers to dmul.o(.text) for __aeabi_dmul
    pow_x.o(i.____hardfp_pow$lsc) refers to dadd.o(.text) for __aeabi_dsub
    pow_x.o(i.____hardfp_pow$lsc) refers to qnan.o(.constdata) for __mathlib_zero
    pow_x.o(i.____hardfp_pow$lsc) refers to poly.o(i.__kernel_poly) for __kernel_poly
    pow_x.o(i.____hardfp_pow$lsc) refers to pow_x.o(.constdata) for .constdata
    pow_x.o(i.____hardfp_pow$lsc) refers to cdrcmple.o(.text) for __aeabi_cdrcmple
    pow_x.o(i.____hardfp_pow$lsc) refers to dscalb.o(.text) for __ARM_scalbn
    pow_x.o(i.____softfp_pow$lsc) refers (Special) to iusefp.o(.text) for __I$use$fp
    pow_x.o(i.____softfp_pow$lsc) refers to pow_x.o(i.____hardfp_pow$lsc) for ____hardfp_pow$lsc
    pow_x.o(i.__pow$lsc) refers (Special) to iusefp.o(.text) for __I$use$fp
    pow_x.o(i.__pow$lsc) refers to pow_x.o(i.____hardfp_pow$lsc) for ____hardfp_pow$lsc
    pow_x.o(.constdata) refers (Special) to iusefp.o(.text) for __I$use$fp
    powf.o(i.__hardfp_powf) refers (Special) to iusefp.o(.text) for __I$use$fp
    powf.o(i.__hardfp_powf) refers to fpstat.o(.text) for __ieee_status
    powf.o(i.__hardfp_powf) refers to errno.o(i.__set_errno) for __set_errno
    powf.o(i.__hardfp_powf) refers to funder.o(i.__mathlib_flt_overflow) for __mathlib_flt_overflow
    powf.o(i.__hardfp_powf) refers to fpclassifyf.o(i.__ARM_fpclassifyf) for __ARM_fpclassifyf
    powf.o(i.__hardfp_powf) refers to funder.o(i.__mathlib_flt_underflow) for __mathlib_flt_underflow
    powf.o(i.__hardfp_powf) refers to funder.o(i.__mathlib_flt_invalid) for __mathlib_flt_invalid
    powf.o(i.__hardfp_powf) refers to powf.o(.constdata) for .constdata
    powf.o(i.__hardfp_powf) refers to funder.o(i.__mathlib_flt_infnan2) for __mathlib_flt_infnan2
    powf.o(i.__hardfp_powf) refers to funder.o(i.__mathlib_flt_divzero) for __mathlib_flt_divzero
    powf.o(i.__softfp_powf) refers (Special) to iusefp.o(.text) for __I$use$fp
    powf.o(i.__softfp_powf) refers to powf.o(i.__hardfp_powf) for __hardfp_powf
    powf.o(i.powf) refers (Special) to iusefp.o(.text) for __I$use$fp
    powf.o(i.powf) refers to powf.o(i.__hardfp_powf) for __hardfp_powf
    powf.o(.constdata) refers (Special) to iusefp.o(.text) for __I$use$fp
    powf_x.o(i.____hardfp_powf$lsc) refers (Special) to iusefp.o(.text) for __I$use$fp
    powf_x.o(i.____hardfp_powf$lsc) refers to fpstat.o(.text) for __ieee_status
    powf_x.o(i.____hardfp_powf$lsc) refers to errno.o(i.__set_errno) for __set_errno
    powf_x.o(i.____hardfp_powf$lsc) refers to powf_x.o(.constdata) for .constdata
    powf_x.o(i.____hardfp_powf$lsc) refers to funder.o(i.__mathlib_flt_infnan2) for __mathlib_flt_infnan2
    powf_x.o(i.____softfp_powf$lsc) refers (Special) to iusefp.o(.text) for __I$use$fp
    powf_x.o(i.____softfp_powf$lsc) refers to powf_x.o(i.____hardfp_powf$lsc) for ____hardfp_powf$lsc
    powf_x.o(i.__powf$lsc) refers (Special) to iusefp.o(.text) for __I$use$fp
    powf_x.o(i.__powf$lsc) refers to powf_x.o(i.____hardfp_powf$lsc) for ____hardfp_powf$lsc
    powf_x.o(.constdata) refers (Special) to iusefp.o(.text) for __I$use$fp
    sqrt.o(i.__hardfp_sqrt) refers (Special) to iusefp.o(.text) for __I$use$fp
    sqrt.o(i.__hardfp_sqrt) refers to dsqrt.o(.text) for _dsqrt
    sqrt.o(i.__hardfp_sqrt) refers to errno.o(i.__set_errno) for __set_errno
    sqrt.o(i.__softfp_sqrt) refers (Special) to iusefp.o(.text) for __I$use$fp
    sqrt.o(i.__softfp_sqrt) refers to dsqrt.o(.text) for _dsqrt
    sqrt.o(i.__softfp_sqrt) refers to errno.o(i.__set_errno) for __set_errno
    sqrt.o(i.sqrt) refers (Special) to iusefp.o(.text) for __I$use$fp
    sqrt.o(i.sqrt) refers to dsqrt.o(.text) for _dsqrt
    sqrt.o(i.sqrt) refers to errno.o(i.__set_errno) for __set_errno
    sqrt_x.o(i.____hardfp_sqrt$lsc) refers (Special) to iusefp.o(.text) for __I$use$fp
    sqrt_x.o(i.____hardfp_sqrt$lsc) refers to cdcmple.o(.text) for __aeabi_cdcmple
    sqrt_x.o(i.____hardfp_sqrt$lsc) refers to errno.o(i.__set_errno) for __set_errno
    sqrt_x.o(i.____hardfp_sqrt$lsc) refers to dsqrt.o(.text) for _dsqrt
    sqrt_x.o(i.____softfp_sqrt$lsc) refers (Special) to iusefp.o(.text) for __I$use$fp
    sqrt_x.o(i.____softfp_sqrt$lsc) refers to cdcmple.o(.text) for __aeabi_cdcmple
    sqrt_x.o(i.____softfp_sqrt$lsc) refers to errno.o(i.__set_errno) for __set_errno
    sqrt_x.o(i.____softfp_sqrt$lsc) refers to dsqrt.o(.text) for _dsqrt
    sqrt_x.o(i.__sqrt$lsc) refers (Special) to iusefp.o(.text) for __I$use$fp
    sqrt_x.o(i.__sqrt$lsc) refers to cdcmple.o(.text) for __aeabi_cdcmple
    sqrt_x.o(i.__sqrt$lsc) refers to errno.o(i.__set_errno) for __set_errno
    sqrt_x.o(i.__sqrt$lsc) refers to dsqrt.o(.text) for _dsqrt
    sqrtf.o(i.__hardfp_sqrtf) refers (Special) to iusefp.o(.text) for __I$use$fp
    sqrtf.o(i.__hardfp_sqrtf) refers to errno.o(i.__set_errno) for __set_errno
    sqrtf.o(i.__softfp_sqrtf) refers (Special) to iusefp.o(.text) for __I$use$fp
    sqrtf.o(i.__softfp_sqrtf) refers to errno.o(i.__set_errno) for __set_errno
    sqrtf.o(i.sqrtf) refers (Special) to iusefp.o(.text) for __I$use$fp
    sqrtf.o(i.sqrtf) refers to errno.o(i.__set_errno) for __set_errno
    sqrtf_x.o(i.____hardfp_sqrtf$lsc) refers (Special) to iusefp.o(.text) for __I$use$fp
    sqrtf_x.o(i.____hardfp_sqrtf$lsc) refers to errno.o(i.__set_errno) for __set_errno
    sqrtf_x.o(i.____softfp_sqrtf$lsc) refers (Special) to iusefp.o(.text) for __I$use$fp
    sqrtf_x.o(i.____softfp_sqrtf$lsc) refers to errno.o(i.__set_errno) for __set_errno
    sqrtf_x.o(i.__sqrtf$lsc) refers (Special) to iusefp.o(.text) for __I$use$fp
    sqrtf_x.o(i.__sqrtf$lsc) refers to errno.o(i.__set_errno) for __set_errno
    tanhf.o(i.__hardfp_tanhf) refers (Special) to iusefp.o(.text) for __I$use$fp
    tanhf.o(i.__hardfp_tanhf) refers to expf.o(i.__hardfp_expf) for __hardfp_expf
    tanhf.o(i.__hardfp_tanhf) refers to fpclassifyf.o(i.__ARM_fpclassifyf) for __ARM_fpclassifyf
    tanhf.o(i.__hardfp_tanhf) refers to funder.o(i.__mathlib_flt_underflow) for __mathlib_flt_underflow
    tanhf.o(i.__hardfp_tanhf) refers to funder.o(i.__mathlib_flt_infnan) for __mathlib_flt_infnan
    tanhf.o(i.__softfp_tanhf) refers (Special) to iusefp.o(.text) for __I$use$fp
    tanhf.o(i.__softfp_tanhf) refers to tanhf.o(i.__hardfp_tanhf) for __hardfp_tanhf
    tanhf.o(i.tanhf) refers (Special) to iusefp.o(.text) for __I$use$fp
    tanhf.o(i.tanhf) refers to tanhf.o(i.__hardfp_tanhf) for __hardfp_tanhf
    tanhf_x.o(i.____hardfp_tanhf$lsc) refers (Special) to iusefp.o(.text) for __I$use$fp
    tanhf_x.o(i.____hardfp_tanhf$lsc) refers to expf.o(i.__hardfp_expf) for __hardfp_expf
    tanhf_x.o(i.____hardfp_tanhf$lsc) refers to funder.o(i.__mathlib_flt_infnan) for __mathlib_flt_infnan
    tanhf_x.o(i.____softfp_tanhf$lsc) refers (Special) to iusefp.o(.text) for __I$use$fp
    tanhf_x.o(i.____softfp_tanhf$lsc) refers to tanhf_x.o(i.____hardfp_tanhf$lsc) for ____hardfp_tanhf$lsc
    tanhf_x.o(i.__tanhf$lsc) refers (Special) to iusefp.o(.text) for __I$use$fp
    tanhf_x.o(i.__tanhf$lsc) refers to tanhf_x.o(i.____hardfp_tanhf$lsc) for ____hardfp_tanhf$lsc
    entry.o(.ARM.Collect$$$$00000000) refers (Special) to entry10a.o(.ARM.Collect$$$$0000000D) for __rt_final_cpp
    entry.o(.ARM.Collect$$$$00000000) refers (Special) to entry11a.o(.ARM.Collect$$$$0000000F) for __rt_final_exit
    entry.o(.ARM.Collect$$$$00000000) refers (Special) to entry7b.o(.ARM.Collect$$$$00000008) for _main_clock
    entry.o(.ARM.Collect$$$$00000000) refers (Special) to entry8b.o(.ARM.Collect$$$$0000000A) for _main_cpp_init
    entry.o(.ARM.Collect$$$$00000000) refers (Special) to entry9a.o(.ARM.Collect$$$$0000000B) for _main_init
    entry.o(.ARM.Collect$$$$00000000) refers (Special) to entry5.o(.ARM.Collect$$$$00000004) for _main_scatterload
    entry.o(.ARM.Collect$$$$00000000) refers (Special) to entry2.o(.ARM.Collect$$$$00000001) for _main_stk
    uldiv.o(.text) refers to llushr.o(.text) for __aeabi_llsr
    uldiv.o(.text) refers to llshl.o(.text) for __aeabi_llsl
    ldiv.o(.text) refers to uldiv.o(.text) for __aeabi_uldivmod
    printfb.o(i.__0fprintf$bare) refers to printfb.o(i._printf_core) for _printf_core
    printfb.o(i.__0fprintf$bare) refers to usart.o(i.fputc) for fputc
    printfb.o(i.__0printf$bare) refers to printfb.o(i._printf_core) for _printf_core
    printfb.o(i.__0printf$bare) refers to usart.o(i.fputc) for fputc
    printfb.o(i.__0printf$bare) refers to stdout.o(.data) for __stdout
    printfb.o(i.__0snprintf$bare) refers to printfb.o(i._printf_core) for _printf_core
    printfb.o(i.__0snprintf$bare) refers to printfb.o(i._snputc) for _snputc
    printfb.o(i.__0sprintf$bare) refers to printfb.o(i._printf_core) for _printf_core
    printfb.o(i.__0sprintf$bare) refers to printfb.o(i._sputc) for _sputc
    printfb.o(i.__0vfprintf$bare) refers to printfb.o(i._printf_core) for _printf_core
    printfb.o(i.__0vfprintf$bare) refers to usart.o(i.fputc) for fputc
    printfb.o(i.__0vprintf$bare) refers to printfb.o(i._printf_core) for _printf_core
    printfb.o(i.__0vprintf$bare) refers to usart.o(i.fputc) for fputc
    printfb.o(i.__0vprintf$bare) refers to stdout.o(.data) for __stdout
    printfb.o(i.__0vsnprintf$bare) refers to printfb.o(i._printf_core) for _printf_core
    printfb.o(i.__0vsnprintf$bare) refers to printfb.o(i._snputc) for _snputc
    printfb.o(i.__0vsprintf$bare) refers to printfb.o(i._printf_core) for _printf_core
    printfb.o(i.__0vsprintf$bare) refers to printfb.o(i._sputc) for _sputc
    printf0.o(i.__0fprintf$0) refers to printf0.o(i._printf_core) for _printf_core
    printf0.o(i.__0fprintf$0) refers to usart.o(i.fputc) for fputc
    printf0.o(i.__0printf$0) refers to printf0.o(i._printf_core) for _printf_core
    printf0.o(i.__0printf$0) refers to usart.o(i.fputc) for fputc
    printf0.o(i.__0printf$0) refers to stdout.o(.data) for __stdout
    printf0.o(i.__0snprintf$0) refers to printf0.o(i._printf_core) for _printf_core
    printf0.o(i.__0snprintf$0) refers to printf0.o(i._snputc) for _snputc
    printf0.o(i.__0sprintf$0) refers to printf0.o(i._printf_core) for _printf_core
    printf0.o(i.__0sprintf$0) refers to printf0.o(i._sputc) for _sputc
    printf0.o(i.__0vfprintf$0) refers to printf0.o(i._printf_core) for _printf_core
    printf0.o(i.__0vfprintf$0) refers to usart.o(i.fputc) for fputc
    printf0.o(i.__0vprintf$0) refers to printf0.o(i._printf_core) for _printf_core
    printf0.o(i.__0vprintf$0) refers to usart.o(i.fputc) for fputc
    printf0.o(i.__0vprintf$0) refers to stdout.o(.data) for __stdout
    printf0.o(i.__0vsnprintf$0) refers to printf0.o(i._printf_core) for _printf_core
    printf0.o(i.__0vsnprintf$0) refers to printf0.o(i._snputc) for _snputc
    printf0.o(i.__0vsprintf$0) refers to printf0.o(i._printf_core) for _printf_core
    printf0.o(i.__0vsprintf$0) refers to printf0.o(i._sputc) for _sputc
    printf1.o(i.__0fprintf$1) refers to printf1.o(i._printf_core) for _printf_core
    printf1.o(i.__0fprintf$1) refers to usart.o(i.fputc) for fputc
    printf1.o(i.__0printf$1) refers to printf1.o(i._printf_core) for _printf_core
    printf1.o(i.__0printf$1) refers to usart.o(i.fputc) for fputc
    printf1.o(i.__0printf$1) refers to stdout.o(.data) for __stdout
    printf1.o(i.__0snprintf$1) refers to printf1.o(i._printf_core) for _printf_core
    printf1.o(i.__0snprintf$1) refers to printf1.o(i._snputc) for _snputc
    printf1.o(i.__0sprintf$1) refers to printf1.o(i._printf_core) for _printf_core
    printf1.o(i.__0sprintf$1) refers to printf1.o(i._sputc) for _sputc
    printf1.o(i.__0vfprintf$1) refers to printf1.o(i._printf_core) for _printf_core
    printf1.o(i.__0vfprintf$1) refers to usart.o(i.fputc) for fputc
    printf1.o(i.__0vprintf$1) refers to printf1.o(i._printf_core) for _printf_core
    printf1.o(i.__0vprintf$1) refers to usart.o(i.fputc) for fputc
    printf1.o(i.__0vprintf$1) refers to stdout.o(.data) for __stdout
    printf1.o(i.__0vsnprintf$1) refers to printf1.o(i._printf_core) for _printf_core
    printf1.o(i.__0vsnprintf$1) refers to printf1.o(i._snputc) for _snputc
    printf1.o(i.__0vsprintf$1) refers to printf1.o(i._printf_core) for _printf_core
    printf1.o(i.__0vsprintf$1) refers to printf1.o(i._sputc) for _sputc
    printf1.o(i._printf_core) refers to uidiv.o(.text) for __aeabi_uidivmod
    printf2.o(i.__0fprintf$2) refers to printf2.o(i._printf_core) for _printf_core
    printf2.o(i.__0fprintf$2) refers to usart.o(i.fputc) for fputc
    printf2.o(i.__0printf$2) refers to printf2.o(i._printf_core) for _printf_core
    printf2.o(i.__0printf$2) refers to usart.o(i.fputc) for fputc
    printf2.o(i.__0printf$2) refers to stdout.o(.data) for __stdout
    printf2.o(i.__0snprintf$2) refers to printf2.o(i._printf_core) for _printf_core
    printf2.o(i.__0snprintf$2) refers to printf2.o(i._snputc) for _snputc
    printf2.o(i.__0sprintf$2) refers to printf2.o(i._printf_core) for _printf_core
    printf2.o(i.__0sprintf$2) refers to printf2.o(i._sputc) for _sputc
    printf2.o(i.__0vfprintf$2) refers to printf2.o(i._printf_core) for _printf_core
    printf2.o(i.__0vfprintf$2) refers to usart.o(i.fputc) for fputc
    printf2.o(i.__0vprintf$2) refers to printf2.o(i._printf_core) for _printf_core
    printf2.o(i.__0vprintf$2) refers to usart.o(i.fputc) for fputc
    printf2.o(i.__0vprintf$2) refers to stdout.o(.data) for __stdout
    printf2.o(i.__0vsnprintf$2) refers to printf2.o(i._printf_core) for _printf_core
    printf2.o(i.__0vsnprintf$2) refers to printf2.o(i._snputc) for _snputc
    printf2.o(i.__0vsprintf$2) refers to printf2.o(i._printf_core) for _printf_core
    printf2.o(i.__0vsprintf$2) refers to printf2.o(i._sputc) for _sputc
    printf3.o(i.__0fprintf$3) refers to printf3.o(i._printf_core) for _printf_core
    printf3.o(i.__0fprintf$3) refers to usart.o(i.fputc) for fputc
    printf3.o(i.__0printf$3) refers to printf3.o(i._printf_core) for _printf_core
    printf3.o(i.__0printf$3) refers to usart.o(i.fputc) for fputc
    printf3.o(i.__0printf$3) refers to stdout.o(.data) for __stdout
    printf3.o(i.__0snprintf$3) refers to printf3.o(i._printf_core) for _printf_core
    printf3.o(i.__0snprintf$3) refers to printf3.o(i._snputc) for _snputc
    printf3.o(i.__0sprintf$3) refers to printf3.o(i._printf_core) for _printf_core
    printf3.o(i.__0sprintf$3) refers to printf3.o(i._sputc) for _sputc
    printf3.o(i.__0vfprintf$3) refers to printf3.o(i._printf_core) for _printf_core
    printf3.o(i.__0vfprintf$3) refers to usart.o(i.fputc) for fputc
    printf3.o(i.__0vprintf$3) refers to printf3.o(i._printf_core) for _printf_core
    printf3.o(i.__0vprintf$3) refers to usart.o(i.fputc) for fputc
    printf3.o(i.__0vprintf$3) refers to stdout.o(.data) for __stdout
    printf3.o(i.__0vsnprintf$3) refers to printf3.o(i._printf_core) for _printf_core
    printf3.o(i.__0vsnprintf$3) refers to printf3.o(i._snputc) for _snputc
    printf3.o(i.__0vsprintf$3) refers to printf3.o(i._printf_core) for _printf_core
    printf3.o(i.__0vsprintf$3) refers to printf3.o(i._sputc) for _sputc
    printf3.o(i._printf_core) refers to uidiv.o(.text) for __aeabi_uidivmod
    printf4.o(i.__0fprintf$4) refers to printf4.o(i._printf_core) for _printf_core
    printf4.o(i.__0fprintf$4) refers to usart.o(i.fputc) for fputc
    printf4.o(i.__0printf$4) refers to printf4.o(i._printf_core) for _printf_core
    printf4.o(i.__0printf$4) refers to usart.o(i.fputc) for fputc
    printf4.o(i.__0printf$4) refers to stdout.o(.data) for __stdout
    printf4.o(i.__0snprintf$4) refers to printf4.o(i._printf_core) for _printf_core
    printf4.o(i.__0snprintf$4) refers to printf4.o(i._snputc) for _snputc
    printf4.o(i.__0sprintf$4) refers to printf4.o(i._printf_core) for _printf_core
    printf4.o(i.__0sprintf$4) refers to printf4.o(i._sputc) for _sputc
    printf4.o(i.__0vfprintf$4) refers to printf4.o(i._printf_core) for _printf_core
    printf4.o(i.__0vfprintf$4) refers to usart.o(i.fputc) for fputc
    printf4.o(i.__0vprintf$4) refers to printf4.o(i._printf_core) for _printf_core
    printf4.o(i.__0vprintf$4) refers to usart.o(i.fputc) for fputc
    printf4.o(i.__0vprintf$4) refers to stdout.o(.data) for __stdout
    printf4.o(i.__0vsnprintf$4) refers to printf4.o(i._printf_core) for _printf_core
    printf4.o(i.__0vsnprintf$4) refers to printf4.o(i._snputc) for _snputc
    printf4.o(i.__0vsprintf$4) refers to printf4.o(i._printf_core) for _printf_core
    printf4.o(i.__0vsprintf$4) refers to printf4.o(i._sputc) for _sputc
    printf4.o(i._printf_core) refers to uldiv.o(.text) for __aeabi_uldivmod
    printf5.o(i.__0fprintf$5) refers to printf5.o(i._printf_core) for _printf_core
    printf5.o(i.__0fprintf$5) refers to usart.o(i.fputc) for fputc
    printf5.o(i.__0printf$5) refers to printf5.o(i._printf_core) for _printf_core
    printf5.o(i.__0printf$5) refers to usart.o(i.fputc) for fputc
    printf5.o(i.__0printf$5) refers to stdout.o(.data) for __stdout
    printf5.o(i.__0snprintf$5) refers to printf5.o(i._printf_core) for _printf_core
    printf5.o(i.__0snprintf$5) refers to printf5.o(i._snputc) for _snputc
    printf5.o(i.__0sprintf$5) refers to printf5.o(i._printf_core) for _printf_core
    printf5.o(i.__0sprintf$5) refers to printf5.o(i._sputc) for _sputc
    printf5.o(i.__0vfprintf$5) refers to printf5.o(i._printf_core) for _printf_core
    printf5.o(i.__0vfprintf$5) refers to usart.o(i.fputc) for fputc
    printf5.o(i.__0vprintf$5) refers to printf5.o(i._printf_core) for _printf_core
    printf5.o(i.__0vprintf$5) refers to usart.o(i.fputc) for fputc
    printf5.o(i.__0vprintf$5) refers to stdout.o(.data) for __stdout
    printf5.o(i.__0vsnprintf$5) refers to printf5.o(i._printf_core) for _printf_core
    printf5.o(i.__0vsnprintf$5) refers to printf5.o(i._snputc) for _snputc
    printf5.o(i.__0vsprintf$5) refers to printf5.o(i._printf_core) for _printf_core
    printf5.o(i.__0vsprintf$5) refers to printf5.o(i._sputc) for _sputc
    printf5.o(i._printf_core) refers to uldiv.o(.text) for __aeabi_uldivmod
    printf6.o(i.__0fprintf$6) refers to printf6.o(i._printf_core) for _printf_core
    printf6.o(i.__0fprintf$6) refers to usart.o(i.fputc) for fputc
    printf6.o(i.__0printf$6) refers to printf6.o(i._printf_core) for _printf_core
    printf6.o(i.__0printf$6) refers to usart.o(i.fputc) for fputc
    printf6.o(i.__0printf$6) refers to stdout.o(.data) for __stdout
    printf6.o(i.__0snprintf$6) refers to printf6.o(i._printf_core) for _printf_core
    printf6.o(i.__0snprintf$6) refers to printf6.o(i._snputc) for _snputc
    printf6.o(i.__0sprintf$6) refers to printf6.o(i._printf_core) for _printf_core
    printf6.o(i.__0sprintf$6) refers to printf6.o(i._sputc) for _sputc
    printf6.o(i.__0vfprintf$6) refers to printf6.o(i._printf_core) for _printf_core
    printf6.o(i.__0vfprintf$6) refers to usart.o(i.fputc) for fputc
    printf6.o(i.__0vprintf$6) refers to printf6.o(i._printf_core) for _printf_core
    printf6.o(i.__0vprintf$6) refers to usart.o(i.fputc) for fputc
    printf6.o(i.__0vprintf$6) refers to stdout.o(.data) for __stdout
    printf6.o(i.__0vsnprintf$6) refers to printf6.o(i._printf_core) for _printf_core
    printf6.o(i.__0vsnprintf$6) refers to printf6.o(i._snputc) for _snputc
    printf6.o(i.__0vsprintf$6) refers to printf6.o(i._printf_core) for _printf_core
    printf6.o(i.__0vsprintf$6) refers to printf6.o(i._sputc) for _sputc
    printf6.o(i._printf_core) refers to printf6.o(i._printf_pre_padding) for _printf_pre_padding
    printf6.o(i._printf_core) refers to uidiv.o(.text) for __aeabi_uidivmod
    printf6.o(i._printf_core) refers to printf6.o(i._printf_post_padding) for _printf_post_padding
    printf7.o(i.__0fprintf$7) refers to printf7.o(i._printf_core) for _printf_core
    printf7.o(i.__0fprintf$7) refers to usart.o(i.fputc) for fputc
    printf7.o(i.__0printf$7) refers to printf7.o(i._printf_core) for _printf_core
    printf7.o(i.__0printf$7) refers to usart.o(i.fputc) for fputc
    printf7.o(i.__0printf$7) refers to stdout.o(.data) for __stdout
    printf7.o(i.__0snprintf$7) refers to printf7.o(i._printf_core) for _printf_core
    printf7.o(i.__0snprintf$7) refers to printf7.o(i._snputc) for _snputc
    printf7.o(i.__0sprintf$7) refers to printf7.o(i._printf_core) for _printf_core
    printf7.o(i.__0sprintf$7) refers to printf7.o(i._sputc) for _sputc
    printf7.o(i.__0vfprintf$7) refers to printf7.o(i._printf_core) for _printf_core
    printf7.o(i.__0vfprintf$7) refers to usart.o(i.fputc) for fputc
    printf7.o(i.__0vprintf$7) refers to printf7.o(i._printf_core) for _printf_core
    printf7.o(i.__0vprintf$7) refers to usart.o(i.fputc) for fputc
    printf7.o(i.__0vprintf$7) refers to stdout.o(.data) for __stdout
    printf7.o(i.__0vsnprintf$7) refers to printf7.o(i._printf_core) for _printf_core
    printf7.o(i.__0vsnprintf$7) refers to printf7.o(i._snputc) for _snputc
    printf7.o(i.__0vsprintf$7) refers to printf7.o(i._printf_core) for _printf_core
    printf7.o(i.__0vsprintf$7) refers to printf7.o(i._sputc) for _sputc
    printf7.o(i._printf_core) refers to printf7.o(i._printf_pre_padding) for _printf_pre_padding
    printf7.o(i._printf_core) refers to uldiv.o(.text) for __aeabi_uldivmod
    printf7.o(i._printf_core) refers to printf7.o(i._printf_post_padding) for _printf_post_padding
    printf8.o(i.__0fprintf$8) refers to printf8.o(i._printf_core) for _printf_core
    printf8.o(i.__0fprintf$8) refers to usart.o(i.fputc) for fputc
    printf8.o(i.__0printf$8) refers to printf8.o(i._printf_core) for _printf_core
    printf8.o(i.__0printf$8) refers to usart.o(i.fputc) for fputc
    printf8.o(i.__0printf$8) refers to stdout.o(.data) for __stdout
    printf8.o(i.__0snprintf$8) refers to printf8.o(i._printf_core) for _printf_core
    printf8.o(i.__0snprintf$8) refers to printf8.o(i._snputc) for _snputc
    printf8.o(i.__0sprintf$8) refers to printf8.o(i._printf_core) for _printf_core
    printf8.o(i.__0sprintf$8) refers to printf8.o(i._sputc) for _sputc
    printf8.o(i.__0vfprintf$8) refers to printf8.o(i._printf_core) for _printf_core
    printf8.o(i.__0vfprintf$8) refers to usart.o(i.fputc) for fputc
    printf8.o(i.__0vprintf$8) refers to printf8.o(i._printf_core) for _printf_core
    printf8.o(i.__0vprintf$8) refers to usart.o(i.fputc) for fputc
    printf8.o(i.__0vprintf$8) refers to stdout.o(.data) for __stdout
    printf8.o(i.__0vsnprintf$8) refers to printf8.o(i._printf_core) for _printf_core
    printf8.o(i.__0vsnprintf$8) refers to printf8.o(i._snputc) for _snputc
    printf8.o(i.__0vsprintf$8) refers to printf8.o(i._printf_core) for _printf_core
    printf8.o(i.__0vsprintf$8) refers to printf8.o(i._sputc) for _sputc
    printf8.o(i._printf_core) refers to printf8.o(i._printf_pre_padding) for _printf_pre_padding
    printf8.o(i._printf_core) refers to uldiv.o(.text) for __aeabi_uldivmod
    printf8.o(i._printf_core) refers to printf8.o(i._printf_post_padding) for _printf_post_padding
    printfa.o(i.__0fprintf) refers (Special) to iusefp.o(.text) for __I$use$fp
    printfa.o(i.__0fprintf) refers to printfa.o(i._printf_core) for _printf_core
    printfa.o(i.__0fprintf) refers to usart.o(i.fputc) for fputc
    printfa.o(i.__0printf) refers (Special) to iusefp.o(.text) for __I$use$fp
    printfa.o(i.__0printf) refers to printfa.o(i._printf_core) for _printf_core
    printfa.o(i.__0printf) refers to usart.o(i.fputc) for fputc
    printfa.o(i.__0printf) refers to stdout.o(.data) for __stdout
    printfa.o(i.__0snprintf) refers (Special) to iusefp.o(.text) for __I$use$fp
    printfa.o(i.__0snprintf) refers to printfa.o(i._printf_core) for _printf_core
    printfa.o(i.__0snprintf) refers to printfa.o(i._snputc) for _snputc
    printfa.o(i.__0sprintf) refers (Special) to iusefp.o(.text) for __I$use$fp
    printfa.o(i.__0sprintf) refers to printfa.o(i._printf_core) for _printf_core
    printfa.o(i.__0sprintf) refers to printfa.o(i._sputc) for _sputc
    printfa.o(i.__0vfprintf) refers (Special) to iusefp.o(.text) for __I$use$fp
    printfa.o(i.__0vfprintf) refers to printfa.o(i._printf_core) for _printf_core
    printfa.o(i.__0vfprintf) refers to usart.o(i.fputc) for fputc
    printfa.o(i.__0vprintf) refers (Special) to iusefp.o(.text) for __I$use$fp
    printfa.o(i.__0vprintf) refers to printfa.o(i._printf_core) for _printf_core
    printfa.o(i.__0vprintf) refers to usart.o(i.fputc) for fputc
    printfa.o(i.__0vprintf) refers to stdout.o(.data) for __stdout
    printfa.o(i.__0vsnprintf) refers (Special) to iusefp.o(.text) for __I$use$fp
    printfa.o(i.__0vsnprintf) refers to printfa.o(i._printf_core) for _printf_core
    printfa.o(i.__0vsnprintf) refers to printfa.o(i._snputc) for _snputc
    printfa.o(i.__0vsprintf) refers (Special) to iusefp.o(.text) for __I$use$fp
    printfa.o(i.__0vsprintf) refers to printfa.o(i._printf_core) for _printf_core
    printfa.o(i.__0vsprintf) refers to printfa.o(i._sputc) for _sputc
    printfa.o(i._fp_digits) refers (Special) to iusefp.o(.text) for __I$use$fp
    printfa.o(i._fp_digits) refers to dmul.o(.text) for __aeabi_dmul
    printfa.o(i._fp_digits) refers to ddiv.o(.text) for __aeabi_ddiv
    printfa.o(i._fp_digits) refers to cdrcmple.o(.text) for __aeabi_cdrcmple
    printfa.o(i._fp_digits) refers to dadd.o(.text) for __aeabi_dadd
    printfa.o(i._fp_digits) refers to dfixul.o(.text) for __aeabi_d2ulz
    printfa.o(i._fp_digits) refers to uldiv.o(.text) for __aeabi_uldivmod
    printfa.o(i._printf_core) refers (Special) to iusefp.o(.text) for __I$use$fp
    printfa.o(i._printf_core) refers to printfa.o(i._printf_pre_padding) for _printf_pre_padding
    printfa.o(i._printf_core) refers to uldiv.o(.text) for __aeabi_uldivmod
    printfa.o(i._printf_core) refers to printfa.o(i._printf_post_padding) for _printf_post_padding
    printfa.o(i._printf_core) refers to printfa.o(i._fp_digits) for _fp_digits
    printfa.o(i._printf_core) refers to uidiv.o(.text) for __aeabi_uidivmod
    printfa.o(i._printf_post_padding) refers (Special) to iusefp.o(.text) for __I$use$fp
    printfa.o(i._printf_pre_padding) refers (Special) to iusefp.o(.text) for __I$use$fp
    printfa.o(i._snputc) refers (Special) to iusefp.o(.text) for __I$use$fp
    printfa.o(i._sputc) refers (Special) to iusefp.o(.text) for __I$use$fp
    dadd.o(.text) refers (Special) to iusefp.o(.text) for __I$use$fp
    dadd.o(.text) refers to llshl.o(.text) for __aeabi_llsl
    dadd.o(.text) refers to llsshr.o(.text) for __aeabi_lasr
    dadd.o(.text) refers to depilogue.o(.text) for _double_epilogue
    dmul.o(.text) refers (Special) to iusefp.o(.text) for __I$use$fp
    dmul.o(.text) refers to depilogue.o(.text) for _double_epilogue
    ddiv.o(.text) refers (Special) to iusefp.o(.text) for __I$use$fp
    ddiv.o(.text) refers to depilogue.o(.text) for _double_round
    dneg.o(.text) refers (Special) to iusefp.o(.text) for __I$use$fp
    dflti.o(.text) refers (Special) to iusefp.o(.text) for __I$use$fp
    dflti.o(.text) refers to depilogue.o(.text) for _double_epilogue
    dfltui.o(.text) refers (Special) to iusefp.o(.text) for __I$use$fp
    dfltui.o(.text) refers to depilogue.o(.text) for _double_epilogue
    ffixl.o(.text) refers (Special) to iusefp.o(.text) for __I$use$fp
    ffixl.o(.text) refers to llshl.o(.text) for __aeabi_llsl
    dfixi.o(.text) refers (Special) to iusefp.o(.text) for __I$use$fp
    dfixi.o(.text) refers to llushr.o(.text) for __aeabi_llsr
    dfixui.o(.text) refers (Special) to iusefp.o(.text) for __I$use$fp
    dfixui.o(.text) refers to llushr.o(.text) for __aeabi_llsr
    dfixl.o(.text) refers (Special) to iusefp.o(.text) for __I$use$fp
    dfixl.o(.text) refers to llushr.o(.text) for __aeabi_llsr
    dfixl.o(.text) refers to llshl.o(.text) for __aeabi_llsl
    f2d.o(.text) refers (Special) to iusefp.o(.text) for __I$use$fp
    cdcmple.o(.text) refers (Special) to iusefp.o(.text) for __I$use$fp
    cdrcmple.o(.text) refers (Special) to iusefp.o(.text) for __I$use$fp
    d2f.o(.text) refers (Special) to iusefp.o(.text) for __I$use$fp
    d2f.o(.text) refers to fepilogue.o(.text) for _float_round
    dunder.o(i.__mathlib_dbl_divzero) refers to ddiv.o(.text) for __aeabi_ddiv
    dunder.o(i.__mathlib_dbl_infnan) refers to dadd.o(.text) for __aeabi_dadd
    dunder.o(i.__mathlib_dbl_infnan2) refers to dadd.o(.text) for __aeabi_dadd
    dunder.o(i.__mathlib_dbl_invalid) refers to ddiv.o(.text) for __aeabi_ddiv
    dunder.o(i.__mathlib_dbl_overflow) refers to dmul.o(.text) for __aeabi_dmul
    dunder.o(i.__mathlib_dbl_posinfnan) refers to dmul.o(.text) for __aeabi_dmul
    dunder.o(i.__mathlib_dbl_underflow) refers to dmul.o(.text) for __aeabi_dmul
    fpclassify.o(i.__ARM_fpclassify) refers (Special) to iusefp.o(.text) for __I$use$fp
    fpclassifyf.o(i.__ARM_fpclassifyf) refers (Special) to iusefp.o(.text) for __I$use$fp
    poly.o(i.__kernel_poly) refers (Special) to iusefp.o(.text) for __I$use$fp
    poly.o(i.__kernel_poly) refers to dmul.o(.text) for __aeabi_dmul
    poly.o(i.__kernel_poly) refers to dadd.o(.text) for __aeabi_dadd
    qnan.o(.constdata) refers (Special) to iusefp.o(.text) for __I$use$fp
    rredf.o(i.__mathlib_rredf2) refers (Special) to iusefp.o(.text) for __I$use$fp
    rredf.o(i.__mathlib_rredf2) refers to rredf.o(.constdata) for .constdata
    rredf.o(.constdata) refers (Special) to iusefp.o(.text) for __I$use$fp
    entry2.o(.ARM.Collect$$$$00000001) refers to entry2.o(.ARM.Collect$$$$00002712) for __lit__00000000
    entry2.o(.ARM.Collect$$$$00002712) refers to startup_stm32f407xx.o(STACK) for __initial_sp
    entry2.o(__vectab_stack_and_reset_area) refers to startup_stm32f407xx.o(STACK) for __initial_sp
    entry2.o(__vectab_stack_and_reset_area) refers to entry.o(.ARM.Collect$$$$00000000) for __main
    entry5.o(.ARM.Collect$$$$00000004) refers to init.o(.text) for __scatterload
    entry9a.o(.ARM.Collect$$$$0000000B) refers to main.o(i.main) for main
    entry9b.o(.ARM.Collect$$$$0000000C) refers to main.o(i.main) for main
    errno.o(i.__aeabi_errno_addr) refers to errno.o(.data) for .data
    errno.o(i.__read_errno) refers to errno.o(.data) for .data
    errno.o(i.__set_errno) refers to errno.o(.data) for .data
    frem.o(.text) refers to fepilogue.o(.text) for _float_epilogue
    depilogue.o(.text) refers to llshl.o(.text) for __aeabi_llsl
    depilogue.o(.text) refers to llushr.o(.text) for __aeabi_llsr
    dsqrt.o(.text) refers to llushr.o(.text) for __aeabi_llsr
    dsqrt.o(.text) refers to depilogue.o(.text) for _double_round
    dfixul.o(.text) refers to llushr.o(.text) for __aeabi_llsr
    dfixul.o(.text) refers to llshl.o(.text) for __aeabi_llsl
    init.o(.text) refers to entry5.o(.ARM.Collect$$$$00000004) for __main_after_scatterload


==============================================================================

Removing Unused input sections from the image.

    Removing startup_stm32f407xx.o(HEAP), (512 bytes).
    Removing main.o(.rev16_text), (4 bytes).
    Removing main.o(.revsh_text), (4 bytes).
    Removing main.o(.rrx_text), (6 bytes).
    Removing main.o(.bss), (4096 bytes).
    Removing gpio.o(.rev16_text), (4 bytes).
    Removing gpio.o(.revsh_text), (4 bytes).
    Removing gpio.o(.rrx_text), (6 bytes).
    Removing adc.o(.rev16_text), (4 bytes).
    Removing adc.o(.revsh_text), (4 bytes).
    Removing adc.o(.rrx_text), (6 bytes).
    Removing adc.o(i.HAL_ADC_MspDeInit), (56 bytes).
    Removing dma.o(.rev16_text), (4 bytes).
    Removing dma.o(.revsh_text), (4 bytes).
    Removing dma.o(.rrx_text), (6 bytes).
    Removing tim.o(.rev16_text), (4 bytes).
    Removing tim.o(.revsh_text), (4 bytes).
    Removing tim.o(.rrx_text), (6 bytes).
    Removing tim.o(i.HAL_TIM_Base_MspDeInit), (48 bytes).
    Removing usart.o(.rev16_text), (4 bytes).
    Removing usart.o(.revsh_text), (4 bytes).
    Removing usart.o(.rrx_text), (6 bytes).
    Removing usart.o(i.HAL_UART_MspDeInit), (44 bytes).
    Removing usart.o(i.fgetc), (32 bytes).
    Removing stm32f4xx_it.o(.rev16_text), (4 bytes).
    Removing stm32f4xx_it.o(.revsh_text), (4 bytes).
    Removing stm32f4xx_it.o(.rrx_text), (6 bytes).
    Removing stm32f4xx_hal_msp.o(.rev16_text), (4 bytes).
    Removing stm32f4xx_hal_msp.o(.revsh_text), (4 bytes).
    Removing stm32f4xx_hal_msp.o(.rrx_text), (6 bytes).
    Removing stm32f4xx_hal_adc.o(.rev16_text), (4 bytes).
    Removing stm32f4xx_hal_adc.o(.revsh_text), (4 bytes).
    Removing stm32f4xx_hal_adc.o(.rrx_text), (6 bytes).
    Removing stm32f4xx_hal_adc.o(i.HAL_ADC_AnalogWDGConfig), (112 bytes).
    Removing stm32f4xx_hal_adc.o(i.HAL_ADC_ConvCpltCallback), (2 bytes).
    Removing stm32f4xx_hal_adc.o(i.HAL_ADC_DeInit), (60 bytes).
    Removing stm32f4xx_hal_adc.o(i.HAL_ADC_GetError), (4 bytes).
    Removing stm32f4xx_hal_adc.o(i.HAL_ADC_GetState), (4 bytes).
    Removing stm32f4xx_hal_adc.o(i.HAL_ADC_GetValue), (6 bytes).
    Removing stm32f4xx_hal_adc.o(i.HAL_ADC_IRQHandler), (302 bytes).
    Removing stm32f4xx_hal_adc.o(i.HAL_ADC_LevelOutOfWindowCallback), (2 bytes).
    Removing stm32f4xx_hal_adc.o(i.HAL_ADC_MspDeInit), (2 bytes).
    Removing stm32f4xx_hal_adc.o(i.HAL_ADC_MspInit), (2 bytes).
    Removing stm32f4xx_hal_adc.o(i.HAL_ADC_PollForConversion), (170 bytes).
    Removing stm32f4xx_hal_adc.o(i.HAL_ADC_PollForEvent), (118 bytes).
    Removing stm32f4xx_hal_adc.o(i.HAL_ADC_Start), (252 bytes).
    Removing stm32f4xx_hal_adc.o(i.HAL_ADC_Start_IT), (268 bytes).
    Removing stm32f4xx_hal_adc.o(i.HAL_ADC_Stop), (60 bytes).
    Removing stm32f4xx_hal_adc.o(i.HAL_ADC_Stop_DMA), (108 bytes).
    Removing stm32f4xx_hal_adc.o(i.HAL_ADC_Stop_IT), (72 bytes).
    Removing stm32f4xx_hal_adc_ex.o(.rev16_text), (4 bytes).
    Removing stm32f4xx_hal_adc_ex.o(.revsh_text), (4 bytes).
    Removing stm32f4xx_hal_adc_ex.o(.rrx_text), (6 bytes).
    Removing stm32f4xx_hal_adc_ex.o(i.ADC_MultiModeDMAConvCplt), (90 bytes).
    Removing stm32f4xx_hal_adc_ex.o(i.ADC_MultiModeDMAError), (18 bytes).
    Removing stm32f4xx_hal_adc_ex.o(i.ADC_MultiModeDMAHalfConvCplt), (6 bytes).
    Removing stm32f4xx_hal_adc_ex.o(i.HAL_ADCEx_InjectedConfigChannel), (444 bytes).
    Removing stm32f4xx_hal_adc_ex.o(i.HAL_ADCEx_InjectedConvCpltCallback), (2 bytes).
    Removing stm32f4xx_hal_adc_ex.o(i.HAL_ADCEx_InjectedGetValue), (58 bytes).
    Removing stm32f4xx_hal_adc_ex.o(i.HAL_ADCEx_InjectedPollForConversion), (144 bytes).
    Removing stm32f4xx_hal_adc_ex.o(i.HAL_ADCEx_InjectedStart), (220 bytes).
    Removing stm32f4xx_hal_adc_ex.o(i.HAL_ADCEx_InjectedStart_IT), (228 bytes).
    Removing stm32f4xx_hal_adc_ex.o(i.HAL_ADCEx_InjectedStop), (86 bytes).
    Removing stm32f4xx_hal_adc_ex.o(i.HAL_ADCEx_InjectedStop_IT), (94 bytes).
    Removing stm32f4xx_hal_adc_ex.o(i.HAL_ADCEx_MultiModeConfigChannel), (84 bytes).
    Removing stm32f4xx_hal_adc_ex.o(i.HAL_ADCEx_MultiModeGetValue), (12 bytes).
    Removing stm32f4xx_hal_adc_ex.o(i.HAL_ADCEx_MultiModeStart_DMA), (272 bytes).
    Removing stm32f4xx_hal_adc_ex.o(i.HAL_ADCEx_MultiModeStop_DMA), (96 bytes).
    Removing stm32f4xx_hal_rcc.o(.rev16_text), (4 bytes).
    Removing stm32f4xx_hal_rcc.o(.revsh_text), (4 bytes).
    Removing stm32f4xx_hal_rcc.o(.rrx_text), (6 bytes).
    Removing stm32f4xx_hal_rcc.o(i.HAL_RCC_CSSCallback), (2 bytes).
    Removing stm32f4xx_hal_rcc.o(i.HAL_RCC_DeInit), (4 bytes).
    Removing stm32f4xx_hal_rcc.o(i.HAL_RCC_DisableCSS), (12 bytes).
    Removing stm32f4xx_hal_rcc.o(i.HAL_RCC_EnableCSS), (12 bytes).
    Removing stm32f4xx_hal_rcc.o(i.HAL_RCC_GetClockConfig), (64 bytes).
    Removing stm32f4xx_hal_rcc.o(i.HAL_RCC_GetHCLKFreq), (12 bytes).
    Removing stm32f4xx_hal_rcc.o(i.HAL_RCC_GetOscConfig), (180 bytes).
    Removing stm32f4xx_hal_rcc.o(i.HAL_RCC_MCOConfig), (140 bytes).
    Removing stm32f4xx_hal_rcc.o(i.HAL_RCC_NMI_IRQHandler), (28 bytes).
    Removing stm32f4xx_hal_rcc_ex.o(.rev16_text), (4 bytes).
    Removing stm32f4xx_hal_rcc_ex.o(.revsh_text), (4 bytes).
    Removing stm32f4xx_hal_rcc_ex.o(.rrx_text), (6 bytes).
    Removing stm32f4xx_hal_rcc_ex.o(i.HAL_RCCEx_DisablePLLI2S), (52 bytes).
    Removing stm32f4xx_hal_rcc_ex.o(i.HAL_RCCEx_EnablePLLI2S), (104 bytes).
    Removing stm32f4xx_hal_rcc_ex.o(i.HAL_RCCEx_GetPeriphCLKConfig), (52 bytes).
    Removing stm32f4xx_hal_rcc_ex.o(i.HAL_RCCEx_GetPeriphCLKFreq), (96 bytes).
    Removing stm32f4xx_hal_rcc_ex.o(i.HAL_RCCEx_PeriphCLKConfig), (316 bytes).
    Removing stm32f4xx_hal_rcc_ex.o(i.HAL_RCC_DeInit), (308 bytes).
    Removing stm32f4xx_hal_flash.o(.rev16_text), (4 bytes).
    Removing stm32f4xx_hal_flash.o(.revsh_text), (4 bytes).
    Removing stm32f4xx_hal_flash.o(.rrx_text), (6 bytes).
    Removing stm32f4xx_hal_flash.o(i.FLASH_Program_Byte), (32 bytes).
    Removing stm32f4xx_hal_flash.o(i.FLASH_Program_DoubleWord), (44 bytes).
    Removing stm32f4xx_hal_flash.o(i.FLASH_Program_HalfWord), (36 bytes).
    Removing stm32f4xx_hal_flash.o(i.FLASH_Program_Word), (36 bytes).
    Removing stm32f4xx_hal_flash.o(i.FLASH_SetErrorCode), (104 bytes).
    Removing stm32f4xx_hal_flash.o(i.FLASH_WaitForLastOperation), (80 bytes).
    Removing stm32f4xx_hal_flash.o(i.HAL_FLASH_EndOfOperationCallback), (2 bytes).
    Removing stm32f4xx_hal_flash.o(i.HAL_FLASH_GetError), (12 bytes).
    Removing stm32f4xx_hal_flash.o(i.HAL_FLASH_IRQHandler), (200 bytes).
    Removing stm32f4xx_hal_flash.o(i.HAL_FLASH_Lock), (20 bytes).
    Removing stm32f4xx_hal_flash.o(i.HAL_FLASH_OB_Launch), (24 bytes).
    Removing stm32f4xx_hal_flash.o(i.HAL_FLASH_OB_Lock), (20 bytes).
    Removing stm32f4xx_hal_flash.o(i.HAL_FLASH_OB_Unlock), (40 bytes).
    Removing stm32f4xx_hal_flash.o(i.HAL_FLASH_OperationErrorCallback), (2 bytes).
    Removing stm32f4xx_hal_flash.o(i.HAL_FLASH_Program), (124 bytes).
    Removing stm32f4xx_hal_flash.o(i.HAL_FLASH_Program_IT), (92 bytes).
    Removing stm32f4xx_hal_flash.o(i.HAL_FLASH_Unlock), (44 bytes).
    Removing stm32f4xx_hal_flash.o(.bss), (32 bytes).
    Removing stm32f4xx_hal_flash_ex.o(.rev16_text), (4 bytes).
    Removing stm32f4xx_hal_flash_ex.o(.revsh_text), (4 bytes).
    Removing stm32f4xx_hal_flash_ex.o(.rrx_text), (6 bytes).
    Removing stm32f4xx_hal_flash_ex.o(i.FLASH_Erase_Sector), (80 bytes).
    Removing stm32f4xx_hal_flash_ex.o(i.FLASH_FlushCaches), (84 bytes).
    Removing stm32f4xx_hal_flash_ex.o(i.FLASH_MassErase), (40 bytes).
    Removing stm32f4xx_hal_flash_ex.o(i.HAL_FLASHEx_Erase), (152 bytes).
    Removing stm32f4xx_hal_flash_ex.o(i.HAL_FLASHEx_Erase_IT), (92 bytes).
    Removing stm32f4xx_hal_flash_ex.o(i.HAL_FLASHEx_OBGetConfig), (64 bytes).
    Removing stm32f4xx_hal_flash_ex.o(i.HAL_FLASHEx_OBProgram), (204 bytes).
    Removing stm32f4xx_hal_flash_ramfunc.o(.rev16_text), (4 bytes).
    Removing stm32f4xx_hal_flash_ramfunc.o(.revsh_text), (4 bytes).
    Removing stm32f4xx_hal_flash_ramfunc.o(.rrx_text), (6 bytes).
    Removing stm32f4xx_hal_gpio.o(.rev16_text), (4 bytes).
    Removing stm32f4xx_hal_gpio.o(.revsh_text), (4 bytes).
    Removing stm32f4xx_hal_gpio.o(.rrx_text), (6 bytes).
    Removing stm32f4xx_hal_gpio.o(i.HAL_GPIO_DeInit), (360 bytes).
    Removing stm32f4xx_hal_gpio.o(i.HAL_GPIO_EXTI_Callback), (2 bytes).
    Removing stm32f4xx_hal_gpio.o(i.HAL_GPIO_EXTI_IRQHandler), (24 bytes).
    Removing stm32f4xx_hal_gpio.o(i.HAL_GPIO_LockPin), (34 bytes).
    Removing stm32f4xx_hal_gpio.o(i.HAL_GPIO_ReadPin), (10 bytes).
    Removing stm32f4xx_hal_gpio.o(i.HAL_GPIO_TogglePin), (16 bytes).
    Removing stm32f4xx_hal_dma_ex.o(.rev16_text), (4 bytes).
    Removing stm32f4xx_hal_dma_ex.o(.revsh_text), (4 bytes).
    Removing stm32f4xx_hal_dma_ex.o(.rrx_text), (6 bytes).
    Removing stm32f4xx_hal_dma_ex.o(i.DMA_MultiBufferSetConfig), (30 bytes).
    Removing stm32f4xx_hal_dma_ex.o(i.HAL_DMAEx_ChangeMemory), (14 bytes).
    Removing stm32f4xx_hal_dma_ex.o(i.HAL_DMAEx_MultiBufferStart), (100 bytes).
    Removing stm32f4xx_hal_dma_ex.o(i.HAL_DMAEx_MultiBufferStart_IT), (2432 bytes).
    Removing stm32f4xx_hal_dma.o(.rev16_text), (4 bytes).
    Removing stm32f4xx_hal_dma.o(.revsh_text), (4 bytes).
    Removing stm32f4xx_hal_dma.o(.rrx_text), (6 bytes).
    Removing stm32f4xx_hal_dma.o(i.HAL_DMA_Abort), (146 bytes).
    Removing stm32f4xx_hal_dma.o(i.HAL_DMA_Abort_IT), (36 bytes).
    Removing stm32f4xx_hal_dma.o(i.HAL_DMA_DeInit), (98 bytes).
    Removing stm32f4xx_hal_dma.o(i.HAL_DMA_GetError), (4 bytes).
    Removing stm32f4xx_hal_dma.o(i.HAL_DMA_GetState), (6 bytes).
    Removing stm32f4xx_hal_dma.o(i.HAL_DMA_PollForTransfer), (288 bytes).
    Removing stm32f4xx_hal_dma.o(i.HAL_DMA_RegisterCallback), (82 bytes).
    Removing stm32f4xx_hal_dma.o(i.HAL_DMA_Start), (70 bytes).
    Removing stm32f4xx_hal_dma.o(i.HAL_DMA_UnRegisterCallback), (96 bytes).
    Removing stm32f4xx_hal_pwr.o(.rev16_text), (4 bytes).
    Removing stm32f4xx_hal_pwr.o(.revsh_text), (4 bytes).
    Removing stm32f4xx_hal_pwr.o(.rrx_text), (6 bytes).
    Removing stm32f4xx_hal_pwr.o(i.HAL_PWR_ConfigPVD), (124 bytes).
    Removing stm32f4xx_hal_pwr.o(i.HAL_PWR_DeInit), (24 bytes).
    Removing stm32f4xx_hal_pwr.o(i.HAL_PWR_DisableBkUpAccess), (24 bytes).
    Removing stm32f4xx_hal_pwr.o(i.HAL_PWR_DisablePVD), (12 bytes).
    Removing stm32f4xx_hal_pwr.o(i.HAL_PWR_DisableSEVOnPend), (16 bytes).
    Removing stm32f4xx_hal_pwr.o(i.HAL_PWR_DisableSleepOnExit), (16 bytes).
    Removing stm32f4xx_hal_pwr.o(i.HAL_PWR_DisableWakeUpPin), (16 bytes).
    Removing stm32f4xx_hal_pwr.o(i.HAL_PWR_EnableBkUpAccess), (24 bytes).
    Removing stm32f4xx_hal_pwr.o(i.HAL_PWR_EnablePVD), (12 bytes).
    Removing stm32f4xx_hal_pwr.o(i.HAL_PWR_EnableSEVOnPend), (16 bytes).
    Removing stm32f4xx_hal_pwr.o(i.HAL_PWR_EnableSleepOnExit), (16 bytes).
    Removing stm32f4xx_hal_pwr.o(i.HAL_PWR_EnableWakeUpPin), (16 bytes).
    Removing stm32f4xx_hal_pwr.o(i.HAL_PWR_EnterSLEEPMode), (36 bytes).
    Removing stm32f4xx_hal_pwr.o(i.HAL_PWR_EnterSTANDBYMode), (32 bytes).
    Removing stm32f4xx_hal_pwr.o(i.HAL_PWR_EnterSTOPMode), (60 bytes).
    Removing stm32f4xx_hal_pwr.o(i.HAL_PWR_PVDCallback), (2 bytes).
    Removing stm32f4xx_hal_pwr.o(i.HAL_PWR_PVD_IRQHandler), (28 bytes).
    Removing stm32f4xx_hal_pwr_ex.o(.rev16_text), (4 bytes).
    Removing stm32f4xx_hal_pwr_ex.o(.revsh_text), (4 bytes).
    Removing stm32f4xx_hal_pwr_ex.o(.rrx_text), (6 bytes).
    Removing stm32f4xx_hal_pwr_ex.o(i.HAL_PWREx_ControlVoltageScaling), (88 bytes).
    Removing stm32f4xx_hal_pwr_ex.o(i.HAL_PWREx_DisableBkUpReg), (60 bytes).
    Removing stm32f4xx_hal_pwr_ex.o(i.HAL_PWREx_DisableFlashPowerDown), (12 bytes).
    Removing stm32f4xx_hal_pwr_ex.o(i.HAL_PWREx_EnableBkUpReg), (60 bytes).
    Removing stm32f4xx_hal_pwr_ex.o(i.HAL_PWREx_EnableFlashPowerDown), (12 bytes).
    Removing stm32f4xx_hal_pwr_ex.o(i.HAL_PWREx_GetVoltageRange), (16 bytes).
    Removing stm32f4xx_hal_cortex.o(.rev16_text), (4 bytes).
    Removing stm32f4xx_hal_cortex.o(.revsh_text), (4 bytes).
    Removing stm32f4xx_hal_cortex.o(.rrx_text), (6 bytes).
    Removing stm32f4xx_hal_cortex.o(i.HAL_CORTEX_ClearEvent), (6 bytes).
    Removing stm32f4xx_hal_cortex.o(i.HAL_MPU_ConfigRegion), (84 bytes).
    Removing stm32f4xx_hal_cortex.o(i.HAL_MPU_Disable), (28 bytes).
    Removing stm32f4xx_hal_cortex.o(i.HAL_MPU_DisableRegion), (24 bytes).
    Removing stm32f4xx_hal_cortex.o(i.HAL_MPU_Enable), (36 bytes).
    Removing stm32f4xx_hal_cortex.o(i.HAL_MPU_EnableRegion), (24 bytes).
    Removing stm32f4xx_hal_cortex.o(i.HAL_NVIC_ClearPendingIRQ), (26 bytes).
    Removing stm32f4xx_hal_cortex.o(i.HAL_NVIC_DisableIRQ), (34 bytes).
    Removing stm32f4xx_hal_cortex.o(i.HAL_NVIC_GetActive), (36 bytes).
    Removing stm32f4xx_hal_cortex.o(i.HAL_NVIC_GetPendingIRQ), (36 bytes).
    Removing stm32f4xx_hal_cortex.o(i.HAL_NVIC_GetPriority), (82 bytes).
    Removing stm32f4xx_hal_cortex.o(i.HAL_NVIC_GetPriorityGrouping), (16 bytes).
    Removing stm32f4xx_hal_cortex.o(i.HAL_NVIC_SetPendingIRQ), (26 bytes).
    Removing stm32f4xx_hal_cortex.o(i.HAL_NVIC_SystemReset), (36 bytes).
    Removing stm32f4xx_hal_cortex.o(i.HAL_SYSTICK_CLKSourceConfig), (24 bytes).
    Removing stm32f4xx_hal_cortex.o(i.HAL_SYSTICK_Callback), (2 bytes).
    Removing stm32f4xx_hal_cortex.o(i.HAL_SYSTICK_IRQHandler), (8 bytes).
    Removing stm32f4xx_hal.o(.rev16_text), (4 bytes).
    Removing stm32f4xx_hal.o(.revsh_text), (4 bytes).
    Removing stm32f4xx_hal.o(.rrx_text), (6 bytes).
    Removing stm32f4xx_hal.o(i.HAL_DBGMCU_DisableDBGSleepMode), (16 bytes).
    Removing stm32f4xx_hal.o(i.HAL_DBGMCU_DisableDBGStandbyMode), (16 bytes).
    Removing stm32f4xx_hal.o(i.HAL_DBGMCU_DisableDBGStopMode), (16 bytes).
    Removing stm32f4xx_hal.o(i.HAL_DBGMCU_EnableDBGSleepMode), (16 bytes).
    Removing stm32f4xx_hal.o(i.HAL_DBGMCU_EnableDBGStandbyMode), (16 bytes).
    Removing stm32f4xx_hal.o(i.HAL_DBGMCU_EnableDBGStopMode), (16 bytes).
    Removing stm32f4xx_hal.o(i.HAL_DeInit), (72 bytes).
    Removing stm32f4xx_hal.o(i.HAL_Delay), (36 bytes).
    Removing stm32f4xx_hal.o(i.HAL_DisableCompensationCell), (12 bytes).
    Removing stm32f4xx_hal.o(i.HAL_EnableCompensationCell), (12 bytes).
    Removing stm32f4xx_hal.o(i.HAL_GetDEVID), (16 bytes).
    Removing stm32f4xx_hal.o(i.HAL_GetHalVersion), (8 bytes).
    Removing stm32f4xx_hal.o(i.HAL_GetREVID), (12 bytes).
    Removing stm32f4xx_hal.o(i.HAL_GetTickFreq), (12 bytes).
    Removing stm32f4xx_hal.o(i.HAL_GetTickPrio), (12 bytes).
    Removing stm32f4xx_hal.o(i.HAL_GetUIDw0), (12 bytes).
    Removing stm32f4xx_hal.o(i.HAL_GetUIDw1), (12 bytes).
    Removing stm32f4xx_hal.o(i.HAL_GetUIDw2), (12 bytes).
    Removing stm32f4xx_hal.o(i.HAL_MspDeInit), (2 bytes).
    Removing stm32f4xx_hal.o(i.HAL_MspInit), (2 bytes).
    Removing stm32f4xx_hal.o(i.HAL_ResumeTick), (14 bytes).
    Removing stm32f4xx_hal.o(i.HAL_SetTickFreq), (36 bytes).
    Removing stm32f4xx_hal.o(i.HAL_SuspendTick), (14 bytes).
    Removing stm32f4xx_hal_exti.o(.rev16_text), (4 bytes).
    Removing stm32f4xx_hal_exti.o(.revsh_text), (4 bytes).
    Removing stm32f4xx_hal_exti.o(.rrx_text), (6 bytes).
    Removing stm32f4xx_hal_exti.o(i.HAL_EXTI_ClearConfigLine), (108 bytes).
    Removing stm32f4xx_hal_exti.o(i.HAL_EXTI_ClearPending), (20 bytes).
    Removing stm32f4xx_hal_exti.o(i.HAL_EXTI_GenerateSWI), (20 bytes).
    Removing stm32f4xx_hal_exti.o(i.HAL_EXTI_GetConfigLine), (144 bytes).
    Removing stm32f4xx_hal_exti.o(i.HAL_EXTI_GetHandle), (12 bytes).
    Removing stm32f4xx_hal_exti.o(i.HAL_EXTI_GetPending), (24 bytes).
    Removing stm32f4xx_hal_exti.o(i.HAL_EXTI_IRQHandler), (36 bytes).
    Removing stm32f4xx_hal_exti.o(i.HAL_EXTI_RegisterCallback), (14 bytes).
    Removing stm32f4xx_hal_exti.o(i.HAL_EXTI_SetConfigLine), (168 bytes).
    Removing stm32f4xx_hal_tim.o(.rev16_text), (4 bytes).
    Removing stm32f4xx_hal_tim.o(.revsh_text), (4 bytes).
    Removing stm32f4xx_hal_tim.o(.rrx_text), (6 bytes).
    Removing stm32f4xx_hal_tim.o(i.HAL_TIM_Base_DeInit), (92 bytes).
    Removing stm32f4xx_hal_tim.o(i.HAL_TIM_Base_GetState), (6 bytes).
    Removing stm32f4xx_hal_tim.o(i.HAL_TIM_Base_MspDeInit), (2 bytes).
    Removing stm32f4xx_hal_tim.o(i.HAL_TIM_Base_MspInit), (2 bytes).
    Removing stm32f4xx_hal_tim.o(i.HAL_TIM_Base_Start_DMA), (192 bytes).
    Removing stm32f4xx_hal_tim.o(i.HAL_TIM_Base_Start_IT), (128 bytes).
    Removing stm32f4xx_hal_tim.o(i.HAL_TIM_Base_Stop), (38 bytes).
    Removing stm32f4xx_hal_tim.o(i.HAL_TIM_Base_Stop_DMA), (58 bytes).
    Removing stm32f4xx_hal_tim.o(i.HAL_TIM_Base_Stop_IT), (48 bytes).
    Removing stm32f4xx_hal_tim.o(i.HAL_TIM_ConfigOCrefClear), (216 bytes).
    Removing stm32f4xx_hal_tim.o(i.HAL_TIM_ConfigTI1Input), (16 bytes).
    Removing stm32f4xx_hal_tim.o(i.HAL_TIM_DMABurstState), (6 bytes).
    Removing stm32f4xx_hal_tim.o(i.HAL_TIM_DMABurst_MultiReadStart), (332 bytes).
    Removing stm32f4xx_hal_tim.o(i.HAL_TIM_DMABurst_MultiWriteStart), (332 bytes).
    Removing stm32f4xx_hal_tim.o(i.HAL_TIM_DMABurst_ReadStart), (18 bytes).
    Removing stm32f4xx_hal_tim.o(i.HAL_TIM_DMABurst_ReadStop), (106 bytes).
    Removing stm32f4xx_hal_tim.o(i.HAL_TIM_DMABurst_WriteStart), (18 bytes).
    Removing stm32f4xx_hal_tim.o(i.HAL_TIM_DMABurst_WriteStop), (106 bytes).
    Removing stm32f4xx_hal_tim.o(i.HAL_TIM_Encoder_DeInit), (76 bytes).
    Removing stm32f4xx_hal_tim.o(i.HAL_TIM_Encoder_GetState), (6 bytes).
    Removing stm32f4xx_hal_tim.o(i.HAL_TIM_Encoder_Init), (164 bytes).
    Removing stm32f4xx_hal_tim.o(i.HAL_TIM_Encoder_MspDeInit), (2 bytes).
    Removing stm32f4xx_hal_tim.o(i.HAL_TIM_Encoder_MspInit), (2 bytes).
    Removing stm32f4xx_hal_tim.o(i.HAL_TIM_Encoder_Start), (142 bytes).
    Removing stm32f4xx_hal_tim.o(i.HAL_TIM_Encoder_Start_DMA), (428 bytes).
    Removing stm32f4xx_hal_tim.o(i.HAL_TIM_Encoder_Start_IT), (182 bytes).
    Removing stm32f4xx_hal_tim.o(i.HAL_TIM_Encoder_Stop), (102 bytes).
    Removing stm32f4xx_hal_tim.o(i.HAL_TIM_Encoder_Stop_DMA), (172 bytes).
    Removing stm32f4xx_hal_tim.o(i.HAL_TIM_Encoder_Stop_IT), (144 bytes).
    Removing stm32f4xx_hal_tim.o(i.HAL_TIM_ErrorCallback), (2 bytes).
    Removing stm32f4xx_hal_tim.o(i.HAL_TIM_GenerateEvent), (38 bytes).
    Removing stm32f4xx_hal_tim.o(i.HAL_TIM_GetActiveChannel), (4 bytes).
    Removing stm32f4xx_hal_tim.o(i.HAL_TIM_GetChannelState), (34 bytes).
    Removing stm32f4xx_hal_tim.o(i.HAL_TIM_IC_CaptureCallback), (2 bytes).
    Removing stm32f4xx_hal_tim.o(i.HAL_TIM_IC_CaptureHalfCpltCallback), (2 bytes).
    Removing stm32f4xx_hal_tim.o(i.HAL_TIM_IC_ConfigChannel), (292 bytes).
    Removing stm32f4xx_hal_tim.o(i.HAL_TIM_IC_DeInit), (92 bytes).
    Removing stm32f4xx_hal_tim.o(i.HAL_TIM_IC_GetState), (6 bytes).
    Removing stm32f4xx_hal_tim.o(i.HAL_TIM_IC_Init), (90 bytes).
    Removing stm32f4xx_hal_tim.o(i.HAL_TIM_IC_MspDeInit), (2 bytes).
    Removing stm32f4xx_hal_tim.o(i.HAL_TIM_IC_MspInit), (2 bytes).
    Removing stm32f4xx_hal_tim.o(i.HAL_TIM_IC_Start), (228 bytes).
    Removing stm32f4xx_hal_tim.o(i.HAL_TIM_IC_Start_DMA), (460 bytes).
    Removing stm32f4xx_hal_tim.o(i.HAL_TIM_IC_Start_IT), (268 bytes).
    Removing stm32f4xx_hal_tim.o(i.HAL_TIM_IC_Stop), (82 bytes).
    Removing stm32f4xx_hal_tim.o(i.HAL_TIM_IC_Stop_DMA), (160 bytes).
    Removing stm32f4xx_hal_tim.o(i.HAL_TIM_IC_Stop_IT), (146 bytes).
    Removing stm32f4xx_hal_tim.o(i.HAL_TIM_IRQHandler), (304 bytes).
    Removing stm32f4xx_hal_tim.o(i.HAL_TIM_OC_ConfigChannel), (82 bytes).
    Removing stm32f4xx_hal_tim.o(i.HAL_TIM_OC_DeInit), (92 bytes).
    Removing stm32f4xx_hal_tim.o(i.HAL_TIM_OC_DelayElapsedCallback), (2 bytes).
    Removing stm32f4xx_hal_tim.o(i.HAL_TIM_OC_GetState), (6 bytes).
    Removing stm32f4xx_hal_tim.o(i.HAL_TIM_OC_Init), (90 bytes).
    Removing stm32f4xx_hal_tim.o(i.HAL_TIM_OC_MspDeInit), (2 bytes).
    Removing stm32f4xx_hal_tim.o(i.HAL_TIM_OC_MspInit), (2 bytes).
    Removing stm32f4xx_hal_tim.o(i.HAL_TIM_OC_Start), (200 bytes).
    Removing stm32f4xx_hal_tim.o(i.HAL_TIM_OC_Start_DMA), (448 bytes).
    Removing stm32f4xx_hal_tim.o(i.HAL_TIM_OC_Start_IT), (244 bytes).
    Removing stm32f4xx_hal_tim.o(i.HAL_TIM_OC_Stop), (124 bytes).
    Removing stm32f4xx_hal_tim.o(i.HAL_TIM_OC_Stop_DMA), (204 bytes).
    Removing stm32f4xx_hal_tim.o(i.HAL_TIM_OC_Stop_IT), (188 bytes).
    Removing stm32f4xx_hal_tim.o(i.HAL_TIM_OnePulse_ConfigChannel), (230 bytes).
    Removing stm32f4xx_hal_tim.o(i.HAL_TIM_OnePulse_DeInit), (76 bytes).
    Removing stm32f4xx_hal_tim.o(i.HAL_TIM_OnePulse_GetState), (6 bytes).
    Removing stm32f4xx_hal_tim.o(i.HAL_TIM_OnePulse_Init), (86 bytes).
    Removing stm32f4xx_hal_tim.o(i.HAL_TIM_OnePulse_MspDeInit), (2 bytes).
    Removing stm32f4xx_hal_tim.o(i.HAL_TIM_OnePulse_MspInit), (2 bytes).
    Removing stm32f4xx_hal_tim.o(i.HAL_TIM_OnePulse_Start), (112 bytes).
    Removing stm32f4xx_hal_tim.o(i.HAL_TIM_OnePulse_Start_IT), (132 bytes).
    Removing stm32f4xx_hal_tim.o(i.HAL_TIM_OnePulse_Stop), (112 bytes).
    Removing stm32f4xx_hal_tim.o(i.HAL_TIM_OnePulse_Stop_IT), (132 bytes).
    Removing stm32f4xx_hal_tim.o(i.HAL_TIM_PWM_ConfigChannel), (204 bytes).
    Removing stm32f4xx_hal_tim.o(i.HAL_TIM_PWM_DeInit), (92 bytes).
    Removing stm32f4xx_hal_tim.o(i.HAL_TIM_PWM_GetState), (6 bytes).
    Removing stm32f4xx_hal_tim.o(i.HAL_TIM_PWM_Init), (90 bytes).
    Removing stm32f4xx_hal_tim.o(i.HAL_TIM_PWM_MspDeInit), (2 bytes).
    Removing stm32f4xx_hal_tim.o(i.HAL_TIM_PWM_MspInit), (2 bytes).
    Removing stm32f4xx_hal_tim.o(i.HAL_TIM_PWM_PulseFinishedCallback), (2 bytes).
    Removing stm32f4xx_hal_tim.o(i.HAL_TIM_PWM_PulseFinishedHalfCpltCallback), (2 bytes).
    Removing stm32f4xx_hal_tim.o(i.HAL_TIM_PWM_Start), (200 bytes).
    Removing stm32f4xx_hal_tim.o(i.HAL_TIM_PWM_Start_DMA), (448 bytes).
    Removing stm32f4xx_hal_tim.o(i.HAL_TIM_PWM_Start_IT), (244 bytes).
    Removing stm32f4xx_hal_tim.o(i.HAL_TIM_PWM_Stop), (124 bytes).
    Removing stm32f4xx_hal_tim.o(i.HAL_TIM_PWM_Stop_DMA), (204 bytes).
    Removing stm32f4xx_hal_tim.o(i.HAL_TIM_PWM_Stop_IT), (188 bytes).
    Removing stm32f4xx_hal_tim.o(i.HAL_TIM_PeriodElapsedCallback), (2 bytes).
    Removing stm32f4xx_hal_tim.o(i.HAL_TIM_PeriodElapsedHalfCpltCallback), (2 bytes).
    Removing stm32f4xx_hal_tim.o(i.HAL_TIM_ReadCapturedValue), (42 bytes).
    Removing stm32f4xx_hal_tim.o(i.HAL_TIM_SlaveConfigSynchro), (86 bytes).
    Removing stm32f4xx_hal_tim.o(i.HAL_TIM_SlaveConfigSynchro_IT), (86 bytes).
    Removing stm32f4xx_hal_tim.o(i.HAL_TIM_TriggerCallback), (2 bytes).
    Removing stm32f4xx_hal_tim.o(i.HAL_TIM_TriggerHalfCpltCallback), (2 bytes).
    Removing stm32f4xx_hal_tim.o(i.TIM_CCxChannelCmd), (26 bytes).
    Removing stm32f4xx_hal_tim.o(i.TIM_DMACaptureCplt), (110 bytes).
    Removing stm32f4xx_hal_tim.o(i.TIM_DMACaptureHalfCplt), (56 bytes).
    Removing stm32f4xx_hal_tim.o(i.TIM_DMADelayPulseCplt), (94 bytes).
    Removing stm32f4xx_hal_tim.o(i.TIM_DMADelayPulseHalfCplt), (56 bytes).
    Removing stm32f4xx_hal_tim.o(i.TIM_DMAError), (84 bytes).
    Removing stm32f4xx_hal_tim.o(i.TIM_DMAPeriodElapsedCplt), (22 bytes).
    Removing stm32f4xx_hal_tim.o(i.TIM_DMAPeriodElapsedHalfCplt), (10 bytes).
    Removing stm32f4xx_hal_tim.o(i.TIM_DMATriggerCplt), (22 bytes).
    Removing stm32f4xx_hal_tim.o(i.TIM_DMATriggerHalfCplt), (10 bytes).
    Removing stm32f4xx_hal_tim.o(i.TIM_OC1_SetConfig), (96 bytes).
    Removing stm32f4xx_hal_tim.o(i.TIM_OC2_SetConfig), (108 bytes).
    Removing stm32f4xx_hal_tim.o(i.TIM_OC3_SetConfig), (104 bytes).
    Removing stm32f4xx_hal_tim.o(i.TIM_OC4_SetConfig), (80 bytes).
    Removing stm32f4xx_hal_tim.o(i.TIM_SlaveTimer_SetConfig), (140 bytes).
    Removing stm32f4xx_hal_tim.o(i.TIM_TI1_SetConfig), (128 bytes).
    Removing stm32f4xx_hal_tim.o(i.TIM_TI2_SetConfig), (54 bytes).
    Removing stm32f4xx_hal_tim_ex.o(.rev16_text), (4 bytes).
    Removing stm32f4xx_hal_tim_ex.o(.revsh_text), (4 bytes).
    Removing stm32f4xx_hal_tim_ex.o(.rrx_text), (6 bytes).
    Removing stm32f4xx_hal_tim_ex.o(i.HAL_TIMEx_BreakCallback), (2 bytes).
    Removing stm32f4xx_hal_tim_ex.o(i.HAL_TIMEx_CommutCallback), (2 bytes).
    Removing stm32f4xx_hal_tim_ex.o(i.HAL_TIMEx_CommutHalfCpltCallback), (2 bytes).
    Removing stm32f4xx_hal_tim_ex.o(i.HAL_TIMEx_ConfigBreakDeadTime), (84 bytes).
    Removing stm32f4xx_hal_tim_ex.o(i.HAL_TIMEx_ConfigCommutEvent), (112 bytes).
    Removing stm32f4xx_hal_tim_ex.o(i.HAL_TIMEx_ConfigCommutEvent_DMA), (144 bytes).
    Removing stm32f4xx_hal_tim_ex.o(i.HAL_TIMEx_ConfigCommutEvent_IT), (112 bytes).
    Removing stm32f4xx_hal_tim_ex.o(i.HAL_TIMEx_GetChannelNState), (34 bytes).
    Removing stm32f4xx_hal_tim_ex.o(i.HAL_TIMEx_HallSensor_DeInit), (76 bytes).
    Removing stm32f4xx_hal_tim_ex.o(i.HAL_TIMEx_HallSensor_GetState), (6 bytes).
    Removing stm32f4xx_hal_tim_ex.o(i.HAL_TIMEx_HallSensor_Init), (208 bytes).
    Removing stm32f4xx_hal_tim_ex.o(i.HAL_TIMEx_HallSensor_MspDeInit), (2 bytes).
    Removing stm32f4xx_hal_tim_ex.o(i.HAL_TIMEx_HallSensor_MspInit), (2 bytes).
    Removing stm32f4xx_hal_tim_ex.o(i.HAL_TIMEx_HallSensor_Start), (168 bytes).
    Removing stm32f4xx_hal_tim_ex.o(i.HAL_TIMEx_HallSensor_Start_DMA), (224 bytes).
    Removing stm32f4xx_hal_tim_ex.o(i.HAL_TIMEx_HallSensor_Start_IT), (180 bytes).
    Removing stm32f4xx_hal_tim_ex.o(i.HAL_TIMEx_HallSensor_Stop), (58 bytes).
    Removing stm32f4xx_hal_tim_ex.o(i.HAL_TIMEx_HallSensor_Stop_DMA), (70 bytes).
    Removing stm32f4xx_hal_tim_ex.o(i.HAL_TIMEx_HallSensor_Stop_IT), (68 bytes).
    Removing stm32f4xx_hal_tim_ex.o(i.HAL_TIMEx_OCN_Start), (192 bytes).
    Removing stm32f4xx_hal_tim_ex.o(i.HAL_TIMEx_OCN_Start_DMA), (392 bytes).
    Removing stm32f4xx_hal_tim_ex.o(i.HAL_TIMEx_OCN_Start_IT), (232 bytes).
    Removing stm32f4xx_hal_tim_ex.o(i.HAL_TIMEx_OCN_Stop), (104 bytes).
    Removing stm32f4xx_hal_tim_ex.o(i.HAL_TIMEx_OCN_Stop_DMA), (168 bytes).
    Removing stm32f4xx_hal_tim_ex.o(i.HAL_TIMEx_OCN_Stop_IT), (170 bytes).
    Removing stm32f4xx_hal_tim_ex.o(i.HAL_TIMEx_OnePulseN_Start), (100 bytes).
    Removing stm32f4xx_hal_tim_ex.o(i.HAL_TIMEx_OnePulseN_Start_IT), (120 bytes).
    Removing stm32f4xx_hal_tim_ex.o(i.HAL_TIMEx_OnePulseN_Stop), (98 bytes).
    Removing stm32f4xx_hal_tim_ex.o(i.HAL_TIMEx_OnePulseN_Stop_IT), (120 bytes).
    Removing stm32f4xx_hal_tim_ex.o(i.HAL_TIMEx_PWMN_Start), (192 bytes).
    Removing stm32f4xx_hal_tim_ex.o(i.HAL_TIMEx_PWMN_Start_DMA), (392 bytes).
    Removing stm32f4xx_hal_tim_ex.o(i.HAL_TIMEx_PWMN_Start_IT), (232 bytes).
    Removing stm32f4xx_hal_tim_ex.o(i.HAL_TIMEx_PWMN_Stop), (104 bytes).
    Removing stm32f4xx_hal_tim_ex.o(i.HAL_TIMEx_PWMN_Stop_DMA), (168 bytes).
    Removing stm32f4xx_hal_tim_ex.o(i.HAL_TIMEx_PWMN_Stop_IT), (170 bytes).
    Removing stm32f4xx_hal_tim_ex.o(i.HAL_TIMEx_RemapConfig), (28 bytes).
    Removing stm32f4xx_hal_tim_ex.o(i.TIMEx_DMACommutationCplt), (16 bytes).
    Removing stm32f4xx_hal_tim_ex.o(i.TIMEx_DMACommutationHalfCplt), (16 bytes).
    Removing stm32f4xx_hal_tim_ex.o(i.TIM_CCxNChannelCmd), (26 bytes).
    Removing stm32f4xx_hal_tim_ex.o(i.TIM_DMADelayPulseNCplt), (74 bytes).
    Removing stm32f4xx_hal_tim_ex.o(i.TIM_DMAErrorCCxN), (62 bytes).
    Removing stm32f4xx_hal_uart.o(.rev16_text), (4 bytes).
    Removing stm32f4xx_hal_uart.o(.revsh_text), (4 bytes).
    Removing stm32f4xx_hal_uart.o(.rrx_text), (6 bytes).
    Removing stm32f4xx_hal_uart.o(i.HAL_HalfDuplex_EnableReceiver), (50 bytes).
    Removing stm32f4xx_hal_uart.o(i.HAL_HalfDuplex_EnableTransmitter), (50 bytes).
    Removing stm32f4xx_hal_uart.o(i.HAL_HalfDuplex_Init), (110 bytes).
    Removing stm32f4xx_hal_uart.o(i.HAL_LIN_Init), (130 bytes).
    Removing stm32f4xx_hal_uart.o(i.HAL_LIN_SendBreak), (60 bytes).
    Removing stm32f4xx_hal_uart.o(i.HAL_MultiProcessor_EnterMuteMode), (62 bytes).
    Removing stm32f4xx_hal_uart.o(i.HAL_MultiProcessor_ExitMuteMode), (62 bytes).
    Removing stm32f4xx_hal_uart.o(i.HAL_MultiProcessor_Init), (144 bytes).
    Removing stm32f4xx_hal_uart.o(i.HAL_UARTEx_GetRxEventType), (4 bytes).
    Removing stm32f4xx_hal_uart.o(i.HAL_UARTEx_ReceiveToIdle), (240 bytes).
    Removing stm32f4xx_hal_uart.o(i.HAL_UARTEx_ReceiveToIdle_DMA), (74 bytes).
    Removing stm32f4xx_hal_uart.o(i.HAL_UARTEx_ReceiveToIdle_IT), (78 bytes).
    Removing stm32f4xx_hal_uart.o(i.HAL_UARTEx_RxEventCallback), (2 bytes).
    Removing stm32f4xx_hal_uart.o(i.HAL_UART_Abort), (210 bytes).
    Removing stm32f4xx_hal_uart.o(i.HAL_UART_AbortCpltCallback), (2 bytes).
    Removing stm32f4xx_hal_uart.o(i.HAL_UART_AbortReceive), (148 bytes).
    Removing stm32f4xx_hal_uart.o(i.HAL_UART_AbortReceiveCpltCallback), (2 bytes).
    Removing stm32f4xx_hal_uart.o(i.HAL_UART_AbortReceive_IT), (152 bytes).
    Removing stm32f4xx_hal_uart.o(i.HAL_UART_AbortTransmit), (98 bytes).
    Removing stm32f4xx_hal_uart.o(i.HAL_UART_AbortTransmitCpltCallback), (2 bytes).
    Removing stm32f4xx_hal_uart.o(i.HAL_UART_AbortTransmit_IT), (104 bytes).
    Removing stm32f4xx_hal_uart.o(i.HAL_UART_Abort_IT), (244 bytes).
    Removing stm32f4xx_hal_uart.o(i.HAL_UART_DMAPause), (120 bytes).
    Removing stm32f4xx_hal_uart.o(i.HAL_UART_DMAResume), (114 bytes).
    Removing stm32f4xx_hal_uart.o(i.HAL_UART_DMAStop), (112 bytes).
    Removing stm32f4xx_hal_uart.o(i.HAL_UART_DeInit), (54 bytes).
    Removing stm32f4xx_hal_uart.o(i.HAL_UART_ErrorCallback), (2 bytes).
    Removing stm32f4xx_hal_uart.o(i.HAL_UART_GetError), (4 bytes).
    Removing stm32f4xx_hal_uart.o(i.HAL_UART_GetState), (10 bytes).
    Removing stm32f4xx_hal_uart.o(i.HAL_UART_IRQHandler), (640 bytes).
    Removing stm32f4xx_hal_uart.o(i.HAL_UART_MspDeInit), (2 bytes).
    Removing stm32f4xx_hal_uart.o(i.HAL_UART_MspInit), (2 bytes).
    Removing stm32f4xx_hal_uart.o(i.HAL_UART_Receive), (176 bytes).
    Removing stm32f4xx_hal_uart.o(i.HAL_UART_Receive_DMA), (28 bytes).
    Removing stm32f4xx_hal_uart.o(i.HAL_UART_Receive_IT), (28 bytes).
    Removing stm32f4xx_hal_uart.o(i.HAL_UART_RxCpltCallback), (2 bytes).
    Removing stm32f4xx_hal_uart.o(i.HAL_UART_RxHalfCpltCallback), (2 bytes).
    Removing stm32f4xx_hal_uart.o(i.HAL_UART_Transmit_DMA), (132 bytes).
    Removing stm32f4xx_hal_uart.o(i.HAL_UART_Transmit_IT), (50 bytes).
    Removing stm32f4xx_hal_uart.o(i.HAL_UART_TxCpltCallback), (2 bytes).
    Removing stm32f4xx_hal_uart.o(i.HAL_UART_TxHalfCpltCallback), (2 bytes).
    Removing stm32f4xx_hal_uart.o(i.UART_DMAAbortOnError), (14 bytes).
    Removing stm32f4xx_hal_uart.o(i.UART_DMAError), (74 bytes).
    Removing stm32f4xx_hal_uart.o(i.UART_DMAReceiveCplt), (134 bytes).
    Removing stm32f4xx_hal_uart.o(i.UART_DMARxAbortCallback), (44 bytes).
    Removing stm32f4xx_hal_uart.o(i.UART_DMARxHalfCplt), (30 bytes).
    Removing stm32f4xx_hal_uart.o(i.UART_DMARxOnlyAbortCallback), (22 bytes).
    Removing stm32f4xx_hal_uart.o(i.UART_DMATransmitCplt), (66 bytes).
    Removing stm32f4xx_hal_uart.o(i.UART_DMATxAbortCallback), (44 bytes).
    Removing stm32f4xx_hal_uart.o(i.UART_DMATxHalfCplt), (10 bytes).
    Removing stm32f4xx_hal_uart.o(i.UART_DMATxOnlyAbortCallback), (20 bytes).
    Removing stm32f4xx_hal_uart.o(i.UART_EndTxTransfer), (28 bytes).
    Removing stm32f4xx_hal_uart.o(i.UART_Receive_IT), (194 bytes).
    Removing stm32f4xx_hal_uart.o(i.UART_Start_Receive_DMA), (160 bytes).
    Removing stm32f4xx_hal_uart.o(i.UART_Start_Receive_IT), (54 bytes).
    Removing system_stm32f4xx.o(.rev16_text), (4 bytes).
    Removing system_stm32f4xx.o(.revsh_text), (4 bytes).
    Removing system_stm32f4xx.o(.rrx_text), (6 bytes).
    Removing system_stm32f4xx.o(i.SystemCoreClockUpdate), (120 bytes).
    Removing ad9959_new.o(.rev16_text), (4 bytes).
    Removing ad9959_new.o(.revsh_text), (4 bytes).
    Removing ad9959_new.o(.rrx_text), (6 bytes).
    Removing ad9959_new.o(i.AD9959FreqSweep), (604 bytes).
    Removing ad9959_new.o(i.CalculateFreq), (48 bytes).
    Removing ad9959_new.o(i.CalculatePhase), (20 bytes).
    Removing ad9959_new.o(i.CalculateStayTime), (128 bytes).
    Removing ad9959_new.o(i.Channel_Select), (132 bytes).
    Removing ad9959_new.o(i.Phase_2_AD9959), (96 bytes).
    Removing ad9959_new.o(i.ReadData_AD9959), (252 bytes).
    Removing ad9959_new.o(i.SweepFrequency), (356 bytes).
    Removing ad9959_new.o(i.WriteFreqOrtho), (416 bytes).
    Removing ad9959_new.o(i.WritePhase), (100 bytes).
    Removing ad9959_new.o(i.Write_Amplitude), (104 bytes).
    Removing ad9959_new.o(i.Write_Phase), (100 bytes).
    Removing ad9959_new.o(i.Write_frequence), (144 bytes).
    Removing ad9959_new.o(.data), (3 bytes).
    Removing ad9959_new.o(.data), (2 bytes).
    Removing ad9959_new.o(.data), (4 bytes).
    Removing ad9959_new.o(.data), (4 bytes).
    Removing fft_processing.o(.rev16_text), (4 bytes).
    Removing fft_processing.o(.revsh_text), (4 bytes).
    Removing fft_processing.o(.rrx_text), (6 bytes).
    Removing fft_processing.o(.constdata), (4 bytes).
    Removing fft_processing.o(.constdata), (4 bytes).
    Removing 9833.o(.rev16_text), (4 bytes).
    Removing 9833.o(.revsh_text), (4 bytes).
    Removing 9833.o(.rrx_text), (6 bytes).
    Removing 9833.o(i.AD9833_AdjustPhase), (172 bytes).
    Removing 9833.o(i.AD9833_Init), (80 bytes).
    Removing 9833.o(i.AD9833_SetAbsolutePhase), (168 bytes).
    Removing 9833.o(i.AD9833_SetWave), (128 bytes).
    Removing 9833.o(i.AD9833_SetWaveData), (320 bytes).
    Removing 9833.o(i.writeSPI), (96 bytes).
    Removing 9833.o(.data), (12 bytes).
    Removing tm1637.o(.rev16_text), (4 bytes).
    Removing tm1637.o(.revsh_text), (4 bytes).
    Removing tm1637.o(.rrx_text), (6 bytes).
    Removing tm1637.o(i.DisplayCO2ppm), (92 bytes).
    Removing tm1637.o(i.TM1637_CHECK_ack), (84 bytes).
    Removing tm1637.o(i.TM1637_ClearAll), (82 bytes).
    Removing tm1637.o(i.TM1637_START), (92 bytes).
    Removing tm1637.o(i.TM1637_STOP), (92 bytes).
    Removing tm1637.o(i.TM1637_WRITE_BYTE_DATA), (124 bytes).
    Removing tm1637.o(i.TM1637_WRITE_DISPLAY_BYTE_FIX_ADDRESS), (88 bytes).
    Removing tm1637.o(i.delay_us), (48 bytes).
    Removing tm1637.o(.constdata), (16 bytes).
    Removing tm1637.o(.data), (4 bytes).
    Removing basicmathfunctions.o(.rev16_text), (4 bytes).
    Removing basicmathfunctions.o(.revsh_text), (4 bytes).
    Removing basicmathfunctions.o(.rrx_text), (6 bytes).
    Removing basicmathfunctions.o(i.arm_abs_f32), (24 bytes).
    Removing basicmathfunctions.o(i.arm_abs_f64), (32 bytes).
    Removing basicmathfunctions.o(i.arm_abs_q15), (34 bytes).
    Removing basicmathfunctions.o(i.arm_abs_q31), (26 bytes).
    Removing basicmathfunctions.o(i.arm_abs_q7), (34 bytes).
    Removing basicmathfunctions.o(i.arm_add_f32), (28 bytes).
    Removing basicmathfunctions.o(i.arm_add_f64), (54 bytes).
    Removing basicmathfunctions.o(i.arm_add_q15), (28 bytes).
    Removing basicmathfunctions.o(i.arm_add_q31), (22 bytes).
    Removing basicmathfunctions.o(i.arm_add_q7), (30 bytes).
    Removing basicmathfunctions.o(i.arm_and_u16), (26 bytes).
    Removing basicmathfunctions.o(i.arm_and_u32), (20 bytes).
    Removing basicmathfunctions.o(i.arm_and_u8), (26 bytes).
    Removing basicmathfunctions.o(i.arm_clip_f32), (68 bytes).
    Removing basicmathfunctions.o(i.arm_clip_q15), (44 bytes).
    Removing basicmathfunctions.o(i.arm_clip_q31), (44 bytes).
    Removing basicmathfunctions.o(i.arm_clip_q7), (36 bytes).
    Removing basicmathfunctions.o(i.arm_dot_prod_f32), (36 bytes).
    Removing basicmathfunctions.o(i.arm_dot_prod_f64), (88 bytes).
    Removing basicmathfunctions.o(i.arm_dot_prod_q15), (32 bytes).
    Removing basicmathfunctions.o(i.arm_dot_prod_q31), (42 bytes).
    Removing basicmathfunctions.o(i.arm_dot_prod_q7), (30 bytes).
    Removing basicmathfunctions.o(i.arm_mult_f32), (28 bytes).
    Removing basicmathfunctions.o(i.arm_mult_f64), (54 bytes).
    Removing basicmathfunctions.o(i.arm_mult_q15), (32 bytes).
    Removing basicmathfunctions.o(i.arm_mult_q31), (28 bytes).
    Removing basicmathfunctions.o(i.arm_mult_q7), (32 bytes).
    Removing basicmathfunctions.o(i.arm_negate_f32), (24 bytes).
    Removing basicmathfunctions.o(i.arm_negate_f64), (38 bytes).
    Removing basicmathfunctions.o(i.arm_negate_q15), (40 bytes).
    Removing basicmathfunctions.o(i.arm_negate_q31), (22 bytes).
    Removing basicmathfunctions.o(i.arm_negate_q7), (28 bytes).
    Removing basicmathfunctions.o(i.arm_not_u16), (22 bytes).
    Removing basicmathfunctions.o(i.arm_not_u32), (16 bytes).
    Removing basicmathfunctions.o(i.arm_not_u8), (22 bytes).
    Removing basicmathfunctions.o(i.arm_offset_f32), (24 bytes).
    Removing basicmathfunctions.o(i.arm_offset_f64), (58 bytes).
    Removing basicmathfunctions.o(i.arm_offset_q15), (24 bytes).
    Removing basicmathfunctions.o(i.arm_offset_q31), (20 bytes).
    Removing basicmathfunctions.o(i.arm_offset_q7), (26 bytes).
    Removing basicmathfunctions.o(i.arm_or_u16), (26 bytes).
    Removing basicmathfunctions.o(i.arm_or_u32), (20 bytes).
    Removing basicmathfunctions.o(i.arm_or_u8), (26 bytes).
    Removing basicmathfunctions.o(i.arm_scale_f32), (24 bytes).
    Removing basicmathfunctions.o(i.arm_scale_f64), (58 bytes).
    Removing basicmathfunctions.o(i.arm_scale_q15), (38 bytes).
    Removing basicmathfunctions.o(i.arm_scale_q31), (74 bytes).
    Removing basicmathfunctions.o(i.arm_scale_q7), (38 bytes).
    Removing basicmathfunctions.o(i.arm_shift_q15), (50 bytes).
    Removing basicmathfunctions.o(i.arm_shift_q31), (74 bytes).
    Removing basicmathfunctions.o(i.arm_shift_q7), (50 bytes).
    Removing basicmathfunctions.o(i.arm_sub_f32), (28 bytes).
    Removing basicmathfunctions.o(i.arm_sub_f64), (54 bytes).
    Removing basicmathfunctions.o(i.arm_sub_q15), (28 bytes).
    Removing basicmathfunctions.o(i.arm_sub_q31), (22 bytes).
    Removing basicmathfunctions.o(i.arm_sub_q7), (30 bytes).
    Removing basicmathfunctions.o(i.arm_xor_u16), (26 bytes).
    Removing basicmathfunctions.o(i.arm_xor_u32), (20 bytes).
    Removing basicmathfunctions.o(i.arm_xor_u8), (26 bytes).
    Removing basicmathfunctionsf16.o(.rev16_text), (4 bytes).
    Removing basicmathfunctionsf16.o(.revsh_text), (4 bytes).
    Removing basicmathfunctionsf16.o(.rrx_text), (6 bytes).
    Removing bayesfunctions.o(.rev16_text), (4 bytes).
    Removing bayesfunctions.o(.revsh_text), (4 bytes).
    Removing bayesfunctions.o(.rrx_text), (6 bytes).
    Removing bayesfunctions.o(i.arm_gaussian_naive_bayes_predict_f32), (180 bytes).
    Removing bayesfunctionsf16.o(.rev16_text), (4 bytes).
    Removing bayesfunctionsf16.o(.revsh_text), (4 bytes).
    Removing bayesfunctionsf16.o(.rrx_text), (6 bytes).
    Removing commontables.o(.rev16_text), (4 bytes).
    Removing commontables.o(.revsh_text), (4 bytes).
    Removing commontables.o(.rrx_text), (6 bytes).
    Removing commontables.o(.constdata), (2048 bytes).
    Removing commontables.o(.constdata), (256 bytes).
    Removing commontables.o(.constdata), (512 bytes).
    Removing commontables.o(.constdata), (1024 bytes).
    Removing commontables.o(.constdata), (2048 bytes).
    Removing commontables.o(.constdata), (4096 bytes).
    Removing commontables.o(.constdata), (8192 bytes).
    Removing commontables.o(.constdata), (16384 bytes).
    Removing commontables.o(.constdata), (32768 bytes).
    Removing commontables.o(.constdata), (65536 bytes).
    Removing commontables.o(.constdata), (128 bytes).
    Removing commontables.o(.constdata), (256 bytes).
    Removing commontables.o(.constdata), (512 bytes).
    Removing commontables.o(.constdata), (1024 bytes).
    Removing commontables.o(.constdata), (2048 bytes).
    Removing commontables.o(.constdata), (4096 bytes).
    Removing commontables.o(.constdata), (16384 bytes).
    Removing commontables.o(.constdata), (32768 bytes).
    Removing commontables.o(.constdata), (96 bytes).
    Removing commontables.o(.constdata), (192 bytes).
    Removing commontables.o(.constdata), (384 bytes).
    Removing commontables.o(.constdata), (768 bytes).
    Removing commontables.o(.constdata), (1536 bytes).
    Removing commontables.o(.constdata), (3072 bytes).
    Removing commontables.o(.constdata), (6144 bytes).
    Removing commontables.o(.constdata), (12288 bytes).
    Removing commontables.o(.constdata), (24576 bytes).
    Removing commontables.o(.constdata), (48 bytes).
    Removing commontables.o(.constdata), (96 bytes).
    Removing commontables.o(.constdata), (192 bytes).
    Removing commontables.o(.constdata), (384 bytes).
    Removing commontables.o(.constdata), (768 bytes).
    Removing commontables.o(.constdata), (1536 bytes).
    Removing commontables.o(.constdata), (3072 bytes).
    Removing commontables.o(.constdata), (6144 bytes).
    Removing commontables.o(.constdata), (12288 bytes).
    Removing commontables.o(.constdata), (24 bytes).
    Removing commontables.o(.constdata), (48 bytes).
    Removing commontables.o(.constdata), (112 bytes).
    Removing commontables.o(.constdata), (224 bytes).
    Removing commontables.o(.constdata), (480 bytes).
    Removing commontables.o(.constdata), (960 bytes).
    Removing commontables.o(.constdata), (1984 bytes).
    Removing commontables.o(.constdata), (3968 bytes).
    Removing commontables.o(.constdata), (8064 bytes).
    Removing commontables.o(.constdata), (40 bytes).
    Removing commontables.o(.constdata), (96 bytes).
    Removing commontables.o(.constdata), (112 bytes).
    Removing commontables.o(.constdata), (416 bytes).
    Removing commontables.o(.constdata), (880 bytes).
    Removing commontables.o(.constdata), (896 bytes).
    Removing commontables.o(.constdata), (7616 bytes).
    Removing commontables.o(.constdata), (8064 bytes).
    Removing commontables.o(.constdata), (24 bytes).
    Removing commontables.o(.constdata), (48 bytes).
    Removing commontables.o(.constdata), (112 bytes).
    Removing commontables.o(.constdata), (224 bytes).
    Removing commontables.o(.constdata), (480 bytes).
    Removing commontables.o(.constdata), (960 bytes).
    Removing commontables.o(.constdata), (1984 bytes).
    Removing commontables.o(.constdata), (3968 bytes).
    Removing commontables.o(.constdata), (8064 bytes).
    Removing commontables.o(.constdata), (256 bytes).
    Removing commontables.o(.constdata), (512 bytes).
    Removing commontables.o(.constdata), (1024 bytes).
    Removing commontables.o(.constdata), (2048 bytes).
    Removing commontables.o(.constdata), (4096 bytes).
    Removing commontables.o(.constdata), (8192 bytes).
    Removing commontables.o(.constdata), (16384 bytes).
    Removing commontables.o(.constdata), (32768 bytes).
    Removing commontables.o(.constdata), (128 bytes).
    Removing commontables.o(.constdata), (256 bytes).
    Removing commontables.o(.constdata), (512 bytes).
    Removing commontables.o(.constdata), (1024 bytes).
    Removing commontables.o(.constdata), (2048 bytes).
    Removing commontables.o(.constdata), (4096 bytes).
    Removing commontables.o(.constdata), (8192 bytes).
    Removing commontables.o(.constdata), (16384 bytes).
    Removing commontables.o(.constdata), (32768 bytes).
    Removing commontables.o(.constdata), (32768 bytes).
    Removing commontables.o(.constdata), (32768 bytes).
    Removing commontables.o(.constdata), (32768 bytes).
    Removing commontables.o(.constdata), (16384 bytes).
    Removing commontables.o(.constdata), (16384 bytes).
    Removing commontables.o(.constdata), (1024 bytes).
    Removing commontables.o(.constdata), (512 bytes).
    Removing commontables.o(.constdata), (4096 bytes).
    Removing commontables.o(.constdata), (2048 bytes).
    Removing commontables.o(.constdata), (16384 bytes).
    Removing commontables.o(.constdata), (8192 bytes).
    Removing commontables.o(.constdata), (65536 bytes).
    Removing commontables.o(.constdata), (32768 bytes).
    Removing commontables.o(.constdata), (512 bytes).
    Removing commontables.o(.constdata), (256 bytes).
    Removing commontables.o(.constdata), (2048 bytes).
    Removing commontables.o(.constdata), (1024 bytes).
    Removing commontables.o(.constdata), (8192 bytes).
    Removing commontables.o(.constdata), (4096 bytes).
    Removing commontables.o(.constdata), (32768 bytes).
    Removing commontables.o(.constdata), (16384 bytes).
    Removing commontables.o(.constdata), (1024 bytes).
    Removing commontables.o(.constdata), (512 bytes).
    Removing commontables.o(.constdata), (4096 bytes).
    Removing commontables.o(.constdata), (2048 bytes).
    Removing commontables.o(.constdata), (16384 bytes).
    Removing commontables.o(.constdata), (8192 bytes).
    Removing commontables.o(.constdata), (65536 bytes).
    Removing commontables.o(.constdata), (32768 bytes).
    Removing commontables.o(.constdata), (128 bytes).
    Removing commontables.o(.constdata), (256 bytes).
    Removing commontables.o(.constdata), (2052 bytes).
    Removing commontables.o(.constdata), (2052 bytes).
    Removing commontables.o(.constdata), (1026 bytes).
    Removing commontables.o(.constdata), (128 bytes).
    Removing commontables.o(.constdata), (32 bytes).
    Removing commontables.o(.constdata), (16 bytes).
    Removing commontables.o(.constdata), (16 bytes).
    Removing commontables.o(.constdata), (16 bytes).
    Removing commontables.o(.constdata), (16 bytes).
    Removing commontables.o(.constdata), (16 bytes).
    Removing commontables.o(.constdata), (16 bytes).
    Removing commontables.o(.constdata), (16 bytes).
    Removing commontables.o(.constdata), (16 bytes).
    Removing commontables.o(.constdata), (16 bytes).
    Removing commontables.o(.constdata), (16 bytes).
    Removing commontables.o(.constdata), (16 bytes).
    Removing commontables.o(.constdata), (16 bytes).
    Removing commontables.o(.constdata), (16 bytes).
    Removing commontables.o(.constdata), (16 bytes).
    Removing commontables.o(.constdata), (16 bytes).
    Removing commontables.o(.constdata), (16 bytes).
    Removing commontables.o(.constdata), (16 bytes).
    Removing commontables.o(.constdata), (16 bytes).
    Removing commontables.o(.constdata), (16 bytes).
    Removing commontables.o(.constdata), (16 bytes).
    Removing commontables.o(.constdata), (16 bytes).
    Removing commontables.o(.constdata), (16 bytes).
    Removing commontables.o(.constdata), (16 bytes).
    Removing commontables.o(.constdata), (16 bytes).
    Removing commontables.o(.constdata), (16 bytes).
    Removing commontables.o(.constdata), (16 bytes).
    Removing commontables.o(.constdata), (16 bytes).
    Removing commontables.o(.constdata), (16 bytes).
    Removing commontables.o(.constdata), (16 bytes).
    Removing commontables.o(.constdata), (16 bytes).
    Removing commontables.o(.constdata), (16 bytes).
    Removing commontables.o(.constdata), (16 bytes).
    Removing commontables.o(.constdata), (16 bytes).
    Removing commontables.o(.constdata), (16 bytes).
    Removing commontables.o(.constdata), (16 bytes).
    Removing commontables.o(.constdata), (24 bytes).
    Removing commontables.o(.constdata), (24 bytes).
    Removing commontables.o(.constdata), (24 bytes).
    Removing commontables.o(.constdata), (24 bytes).
    Removing commontables.o(.constdata), (24 bytes).
    Removing commontables.o(.constdata), (24 bytes).
    Removing commontables.o(.constdata), (24 bytes).
    Removing commontables.o(.constdata), (24 bytes).
    Removing commontables.o(.constdata), (24 bytes).
    Removing commontables.o(.constdata), (24 bytes).
    Removing commontables.o(.constdata), (24 bytes).
    Removing commontables.o(.constdata), (24 bytes).
    Removing commontables.o(.constdata), (24 bytes).
    Removing commontables.o(.constdata), (24 bytes).
    Removing commontables.o(.constdata), (24 bytes).
    Removing commontables.o(.constdata), (24 bytes).
    Removing commontables.o(.constdata), (24 bytes).
    Removing commontables.o(.constdata), (24 bytes).
    Removing commontables.o(.constdata), (24 bytes).
    Removing commontables.o(.constdata), (24 bytes).
    Removing commontables.o(.constdata), (24 bytes).
    Removing commontables.o(.constdata), (24 bytes).
    Removing commontables.o(.constdata), (24 bytes).
    Removing commontables.o(.constdata), (24 bytes).
    Removing commontables.o(.constdata), (24 bytes).
    Removing commontables.o(.constdata), (24 bytes).
    Removing commontables.o(.constdata), (24 bytes).
    Removing commontables.o(.constdata), (24 bytes).
    Removing commontables.o(.constdata), (24 bytes).
    Removing commontables.o(.constdata), (24 bytes).
    Removing commontables.o(.constdata), (24 bytes).
    Removing commontables.o(.constdata), (24 bytes).
    Removing commontables.o(.constdata), (24 bytes).
    Removing commontables.o(.constdata), (24 bytes).
    Removing commontablesf16.o(.rev16_text), (4 bytes).
    Removing commontablesf16.o(.revsh_text), (4 bytes).
    Removing commontablesf16.o(.rrx_text), (6 bytes).
    Removing complexmathfunctions.o(.rev16_text), (4 bytes).
    Removing complexmathfunctions.o(.revsh_text), (4 bytes).
    Removing complexmathfunctions.o(.rrx_text), (6 bytes).
    Removing complexmathfunctions.o(i.arm_cmplx_conj_f32), (36 bytes).
    Removing complexmathfunctions.o(i.arm_cmplx_conj_q15), (34 bytes).
    Removing complexmathfunctions.o(i.arm_cmplx_conj_q31), (26 bytes).
    Removing complexmathfunctions.o(i.arm_cmplx_dot_prod_f32), (72 bytes).
    Removing complexmathfunctions.o(i.arm_cmplx_dot_prod_q15), (96 bytes).
    Removing complexmathfunctions.o(i.arm_cmplx_dot_prod_q31), (130 bytes).
    Removing complexmathfunctions.o(i.arm_cmplx_mag_f64), (86 bytes).
    Removing complexmathfunctions.o(i.arm_cmplx_mag_fast_q15), (46 bytes).
    Removing complexmathfunctions.o(i.arm_cmplx_mag_q15), (52 bytes).
    Removing complexmathfunctions.o(i.arm_cmplx_mag_q31), (46 bytes).
    Removing complexmathfunctions.o(i.arm_cmplx_mag_squared_f32), (34 bytes).
    Removing complexmathfunctions.o(i.arm_cmplx_mag_squared_f64), (78 bytes).
    Removing complexmathfunctions.o(i.arm_cmplx_mag_squared_q15), (32 bytes).
    Removing complexmathfunctions.o(i.arm_cmplx_mag_squared_q31), (32 bytes).
    Removing complexmathfunctions.o(i.arm_cmplx_mult_cmplx_f32), (58 bytes).
    Removing complexmathfunctions.o(i.arm_cmplx_mult_cmplx_f64), (134 bytes).
    Removing complexmathfunctions.o(i.arm_cmplx_mult_cmplx_q15), (66 bytes).
    Removing complexmathfunctions.o(i.arm_cmplx_mult_cmplx_q31), (70 bytes).
    Removing complexmathfunctions.o(i.arm_cmplx_mult_real_f32), (44 bytes).
    Removing complexmathfunctions.o(i.arm_cmplx_mult_real_q15), (48 bytes).
    Removing complexmathfunctions.o(i.arm_cmplx_mult_real_q31), (42 bytes).
    Removing complexmathfunctionsf16.o(.rev16_text), (4 bytes).
    Removing complexmathfunctionsf16.o(.revsh_text), (4 bytes).
    Removing complexmathfunctionsf16.o(.rrx_text), (6 bytes).
    Removing controllerfunctions.o(.rev16_text), (4 bytes).
    Removing controllerfunctions.o(.revsh_text), (4 bytes).
    Removing controllerfunctions.o(.rrx_text), (6 bytes).
    Removing controllerfunctions.o(i.arm_pid_init_f32), (54 bytes).
    Removing controllerfunctions.o(i.arm_pid_init_q15), (52 bytes).
    Removing controllerfunctions.o(i.arm_pid_init_q31), (46 bytes).
    Removing controllerfunctions.o(i.arm_pid_reset_f32), (10 bytes).
    Removing controllerfunctions.o(i.arm_pid_reset_q15), (8 bytes).
    Removing controllerfunctions.o(i.arm_pid_reset_q31), (10 bytes).
    Removing controllerfunctions.o(i.arm_sin_cos_f32), (272 bytes).
    Removing controllerfunctions.o(i.arm_sin_cos_q31), (524 bytes).
    Removing distancefunctions.o(.rev16_text), (4 bytes).
    Removing distancefunctions.o(.revsh_text), (4 bytes).
    Removing distancefunctions.o(.rrx_text), (6 bytes).
    Removing distancefunctions.o(i.arm_boolean_distance_TF_FT), (172 bytes).
    Removing distancefunctions.o(i.arm_boolean_distance_TT), (114 bytes).
    Removing distancefunctions.o(i.arm_boolean_distance_TT_FF_TF_FT), (276 bytes).
    Removing distancefunctions.o(i.arm_boolean_distance_TT_TF_FT), (224 bytes).
    Removing distancefunctions.o(i.arm_braycurtis_distance_f32), (60 bytes).
    Removing distancefunctions.o(i.arm_canberra_distance_f32), (76 bytes).
    Removing distancefunctions.o(i.arm_chebyshev_distance_f32), (56 bytes).
    Removing distancefunctions.o(i.arm_chebyshev_distance_f64), (108 bytes).
    Removing distancefunctions.o(i.arm_cityblock_distance_f32), (40 bytes).
    Removing distancefunctions.o(i.arm_cityblock_distance_f64), (92 bytes).
    Removing distancefunctions.o(i.arm_correlation_distance_f32), (200 bytes).
    Removing distancefunctions.o(i.arm_cosine_distance_f32), (88 bytes).
    Removing distancefunctions.o(i.arm_cosine_distance_f64), (112 bytes).
    Removing distancefunctions.o(i.arm_dice_distance), (132 bytes).
    Removing distancefunctions.o(i.arm_euclidean_distance_f32), (56 bytes).
    Removing distancefunctions.o(i.arm_euclidean_distance_f64), (104 bytes).
    Removing distancefunctions.o(i.arm_hamming_distance), (74 bytes).
    Removing distancefunctions.o(i.arm_jaccard_distance), (78 bytes).
    Removing distancefunctions.o(i.arm_jensenshannon_distance_f32), (136 bytes).
    Removing distancefunctions.o(i.arm_kulsinski_distance), (86 bytes).
    Removing distancefunctions.o(i.arm_minkowski_distance_f32), (108 bytes).
    Removing distancefunctions.o(i.arm_rogerstanimoto_distance), (88 bytes).
    Removing distancefunctions.o(i.arm_russellrao_distance), (42 bytes).
    Removing distancefunctions.o(i.arm_sokalmichener_distance), (96 bytes).
    Removing distancefunctions.o(i.arm_sokalsneath_distance), (84 bytes).
    Removing distancefunctions.o(i.arm_yule_distance), (140 bytes).
    Removing distancefunctions.o(i.rel_entr), (28 bytes).
    Removing distancefunctionsf16.o(.rev16_text), (4 bytes).
    Removing distancefunctionsf16.o(.revsh_text), (4 bytes).
    Removing distancefunctionsf16.o(.rrx_text), (6 bytes).
    Removing fastmathfunctions.o(.rev16_text), (4 bytes).
    Removing fastmathfunctions.o(.revsh_text), (4 bytes).
    Removing fastmathfunctions.o(.rrx_text), (6 bytes).
    Removing fastmathfunctions.o(i.arm_atan2_f32), (540 bytes).
    Removing fastmathfunctions.o(i.arm_atan2_q15), (644 bytes).
    Removing fastmathfunctions.o(i.arm_atan2_q31), (948 bytes).
    Removing fastmathfunctions.o(i.arm_cos_f32), (132 bytes).
    Removing fastmathfunctions.o(i.arm_cos_q15), (68 bytes).
    Removing fastmathfunctions.o(i.arm_cos_q31), (64 bytes).
    Removing fastmathfunctions.o(i.arm_divide_q15), (106 bytes).
    Removing fastmathfunctions.o(i.arm_divide_q31), (118 bytes).
    Removing fastmathfunctions.o(i.arm_sin_f32), (128 bytes).
    Removing fastmathfunctions.o(i.arm_sin_q15), (60 bytes).
    Removing fastmathfunctions.o(i.arm_sin_q31), (64 bytes).
    Removing fastmathfunctions.o(i.arm_sqrt_q15), (156 bytes).
    Removing fastmathfunctions.o(i.arm_sqrt_q31), (188 bytes).
    Removing fastmathfunctions.o(i.arm_vexp_f32), (32 bytes).
    Removing fastmathfunctions.o(i.arm_vexp_f64), (32 bytes).
    Removing fastmathfunctions.o(i.arm_vlog_f32), (32 bytes).
    Removing fastmathfunctions.o(i.arm_vlog_f64), (32 bytes).
    Removing fastmathfunctions.o(i.arm_vlog_q15), (96 bytes).
    Removing fastmathfunctions.o(i.arm_vlog_q31), (92 bytes).
    Removing fastmathfunctions.o(.constdata), (112 bytes).
    Removing fastmathfunctionsf16.o(.rev16_text), (4 bytes).
    Removing fastmathfunctionsf16.o(.revsh_text), (4 bytes).
    Removing fastmathfunctionsf16.o(.rrx_text), (6 bytes).
    Removing filteringfunctions.o(.rev16_text), (4 bytes).
    Removing filteringfunctions.o(.revsh_text), (4 bytes).
    Removing filteringfunctions.o(.rrx_text), (6 bytes).
    Removing filteringfunctions.o(i.arm_biquad_cas_df1_32x64_init_q31), (26 bytes).
    Removing filteringfunctions.o(i.arm_biquad_cas_df1_32x64_q31), (328 bytes).
    Removing filteringfunctions.o(i.arm_biquad_cascade_df1_f32), (132 bytes).
    Removing filteringfunctions.o(i.arm_biquad_cascade_df1_fast_q15), (182 bytes).
    Removing filteringfunctions.o(i.arm_biquad_cascade_df1_fast_q31), (148 bytes).
    Removing filteringfunctions.o(i.arm_biquad_cascade_df1_init_f32), (22 bytes).
    Removing filteringfunctions.o(i.arm_biquad_cascade_df1_init_q15), (26 bytes).
    Removing filteringfunctions.o(i.arm_biquad_cascade_df1_init_q31), (26 bytes).
    Removing filteringfunctions.o(i.arm_biquad_cascade_df1_q15), (328 bytes).
    Removing filteringfunctions.o(i.arm_biquad_cascade_df1_q31), (176 bytes).
    Removing filteringfunctions.o(i.arm_biquad_cascade_df2T_f32), (100 bytes).
    Removing filteringfunctions.o(i.arm_biquad_cascade_df2T_f64), (224 bytes).
    Removing filteringfunctions.o(i.arm_biquad_cascade_df2T_init_f32), (22 bytes).
    Removing filteringfunctions.o(i.arm_biquad_cascade_df2T_init_f64), (22 bytes).
    Removing filteringfunctions.o(i.arm_biquad_cascade_stereo_df2T_f32), (152 bytes).
    Removing filteringfunctions.o(i.arm_biquad_cascade_stereo_df2T_init_f32), (22 bytes).
    Removing filteringfunctions.o(i.arm_conv_f32), (308 bytes).
    Removing filteringfunctions.o(i.arm_conv_fast_opt_q15), (192 bytes).
    Removing filteringfunctions.o(i.arm_conv_fast_q15), (1070 bytes).
    Removing filteringfunctions.o(i.arm_conv_fast_q31), (818 bytes).
    Removing filteringfunctions.o(i.arm_conv_opt_q15), (194 bytes).
    Removing filteringfunctions.o(i.arm_conv_opt_q7), (626 bytes).
    Removing filteringfunctions.o(i.arm_conv_partial_f32), (440 bytes).
    Removing filteringfunctions.o(i.arm_conv_partial_fast_opt_q15), (232 bytes).
    Removing filteringfunctions.o(i.arm_conv_partial_fast_q15), (1282 bytes).
    Removing filteringfunctions.o(i.arm_conv_partial_fast_q31), (424 bytes).
    Removing filteringfunctions.o(i.arm_conv_partial_opt_q15), (244 bytes).
    Removing filteringfunctions.o(i.arm_conv_partial_opt_q7), (660 bytes).
    Removing filteringfunctions.o(i.arm_conv_partial_q15), (1546 bytes).
    Removing filteringfunctions.o(i.arm_conv_partial_q31), (494 bytes).
    Removing filteringfunctions.o(i.arm_conv_partial_q7), (432 bytes).
    Removing filteringfunctions.o(i.arm_conv_q15), (1366 bytes).
    Removing filteringfunctions.o(i.arm_conv_q31), (350 bytes).
    Removing filteringfunctions.o(i.arm_conv_q7), (298 bytes).
    Removing filteringfunctions.o(i.arm_correlate_f32), (340 bytes).
    Removing filteringfunctions.o(i.arm_correlate_f64), (512 bytes).
    Removing filteringfunctions.o(i.arm_correlate_fast_opt_q15), (200 bytes).
    Removing filteringfunctions.o(i.arm_correlate_fast_q15), (994 bytes).
    Removing filteringfunctions.o(i.arm_correlate_fast_q31), (856 bytes).
    Removing filteringfunctions.o(i.arm_correlate_opt_q15), (210 bytes).
    Removing filteringfunctions.o(i.arm_correlate_opt_q7), (640 bytes).
    Removing filteringfunctions.o(i.arm_correlate_q15), (1270 bytes).
    Removing filteringfunctions.o(i.arm_correlate_q31), (398 bytes).
    Removing filteringfunctions.o(i.arm_correlate_q7), (324 bytes).
    Removing filteringfunctions.o(i.arm_fir_decimate_f32), (120 bytes).
    Removing filteringfunctions.o(i.arm_fir_decimate_fast_q15), (328 bytes).
    Removing filteringfunctions.o(i.arm_fir_decimate_fast_q31), (114 bytes).
    Removing filteringfunctions.o(i.arm_fir_decimate_init_f32), (50 bytes).
    Removing filteringfunctions.o(i.arm_fir_decimate_init_q15), (50 bytes).
    Removing filteringfunctions.o(i.arm_fir_decimate_init_q31), (50 bytes).
    Removing filteringfunctions.o(i.arm_fir_decimate_q15), (372 bytes).
    Removing filteringfunctions.o(i.arm_fir_decimate_q31), (130 bytes).
    Removing filteringfunctions.o(i.arm_fir_f32), (104 bytes).
    Removing filteringfunctions.o(i.arm_fir_f64), (176 bytes).
    Removing filteringfunctions.o(i.arm_fir_fast_q15), (112 bytes).
    Removing filteringfunctions.o(i.arm_fir_fast_q31), (94 bytes).
    Removing filteringfunctions.o(i.arm_fir_init_f32), (28 bytes).
    Removing filteringfunctions.o(i.arm_fir_init_f64), (28 bytes).
    Removing filteringfunctions.o(i.arm_fir_init_q15), (38 bytes).
    Removing filteringfunctions.o(i.arm_fir_init_q31), (28 bytes).
    Removing filteringfunctions.o(i.arm_fir_init_q7), (26 bytes).
    Removing filteringfunctions.o(i.arm_fir_interpolate_f32), (140 bytes).
    Removing filteringfunctions.o(i.arm_fir_interpolate_init_f32), (52 bytes).
    Removing filteringfunctions.o(i.arm_fir_interpolate_init_q15), (52 bytes).
    Removing filteringfunctions.o(i.arm_fir_interpolate_init_q31), (52 bytes).
    Removing filteringfunctions.o(i.arm_fir_interpolate_q15), (184 bytes).
    Removing filteringfunctions.o(i.arm_fir_interpolate_q31), (176 bytes).
    Removing filteringfunctions.o(i.arm_fir_lattice_f32), (102 bytes).
    Removing filteringfunctions.o(i.arm_fir_lattice_init_f32), (22 bytes).
    Removing filteringfunctions.o(i.arm_fir_lattice_init_q15), (22 bytes).
    Removing filteringfunctions.o(i.arm_fir_lattice_init_q31), (22 bytes).
    Removing filteringfunctions.o(i.arm_fir_lattice_q15), (132 bytes).
    Removing filteringfunctions.o(i.arm_fir_lattice_q31), (106 bytes).
    Removing filteringfunctions.o(i.arm_fir_q15), (132 bytes).
    Removing filteringfunctions.o(i.arm_fir_q31), (116 bytes).
    Removing filteringfunctions.o(i.arm_fir_q7), (100 bytes).
    Removing filteringfunctions.o(i.arm_fir_sparse_f32), (444 bytes).
    Removing filteringfunctions.o(i.arm_fir_sparse_init_f32), (38 bytes).
    Removing filteringfunctions.o(i.arm_fir_sparse_init_q15), (38 bytes).
    Removing filteringfunctions.o(i.arm_fir_sparse_init_q31), (38 bytes).
    Removing filteringfunctions.o(i.arm_fir_sparse_init_q7), (38 bytes).
    Removing filteringfunctions.o(i.arm_fir_sparse_q15), (512 bytes).
    Removing filteringfunctions.o(i.arm_fir_sparse_q31), (496 bytes).
    Removing filteringfunctions.o(i.arm_fir_sparse_q7), (498 bytes).
    Removing filteringfunctions.o(i.arm_iir_lattice_f32), (132 bytes).
    Removing filteringfunctions.o(i.arm_iir_lattice_init_f32), (28 bytes).
    Removing filteringfunctions.o(i.arm_iir_lattice_init_q15), (28 bytes).
    Removing filteringfunctions.o(i.arm_iir_lattice_init_q31), (28 bytes).
    Removing filteringfunctions.o(i.arm_iir_lattice_q15), (238 bytes).
    Removing filteringfunctions.o(i.arm_iir_lattice_q31), (236 bytes).
    Removing filteringfunctions.o(i.arm_levinson_durbin_f32), (240 bytes).
    Removing filteringfunctions.o(i.arm_levinson_durbin_q31), (396 bytes).
    Removing filteringfunctions.o(i.arm_lms_f32), (164 bytes).
    Removing filteringfunctions.o(i.arm_lms_init_f32), (44 bytes).
    Removing filteringfunctions.o(i.arm_lms_init_q15), (40 bytes).
    Removing filteringfunctions.o(i.arm_lms_init_q31), (40 bytes).
    Removing filteringfunctions.o(i.arm_lms_norm_f32), (208 bytes).
    Removing filteringfunctions.o(i.arm_lms_norm_init_f32), (60 bytes).
    Removing filteringfunctions.o(i.arm_lms_norm_init_q15), (56 bytes).
    Removing filteringfunctions.o(i.arm_lms_norm_init_q31), (56 bytes).
    Removing filteringfunctions.o(i.arm_lms_norm_q15), (362 bytes).
    Removing filteringfunctions.o(i.arm_lms_norm_q31), (438 bytes).
    Removing filteringfunctions.o(i.arm_lms_q15), (222 bytes).
    Removing filteringfunctions.o(i.arm_lms_q31), (244 bytes).
    Removing filteringfunctionsf16.o(.rev16_text), (4 bytes).
    Removing filteringfunctionsf16.o(.revsh_text), (4 bytes).
    Removing filteringfunctionsf16.o(.rrx_text), (6 bytes).
    Removing interpolationfunctions.o(.rev16_text), (4 bytes).
    Removing interpolationfunctions.o(.revsh_text), (4 bytes).
    Removing interpolationfunctions.o(.rrx_text), (6 bytes).
    Removing interpolationfunctions.o(i.arm_bilinear_interp_f32), (148 bytes).
    Removing interpolationfunctions.o(i.arm_bilinear_interp_q15), (156 bytes).
    Removing interpolationfunctions.o(i.arm_bilinear_interp_q31), (124 bytes).
    Removing interpolationfunctions.o(i.arm_bilinear_interp_q7), (120 bytes).
    Removing interpolationfunctions.o(i.arm_linear_interp_f32), (128 bytes).
    Removing interpolationfunctions.o(i.arm_linear_interp_q15), (72 bytes).
    Removing interpolationfunctions.o(i.arm_linear_interp_q31), (62 bytes).
    Removing interpolationfunctions.o(i.arm_linear_interp_q7), (60 bytes).
    Removing interpolationfunctions.o(i.arm_spline_f32), (244 bytes).
    Removing interpolationfunctions.o(i.arm_spline_init_f32), (416 bytes).
    Removing interpolationfunctionsf16.o(.rev16_text), (4 bytes).
    Removing interpolationfunctionsf16.o(.revsh_text), (4 bytes).
    Removing interpolationfunctionsf16.o(.rrx_text), (6 bytes).
    Removing matrixfunctions.o(.rev16_text), (4 bytes).
    Removing matrixfunctions.o(.revsh_text), (4 bytes).
    Removing matrixfunctions.o(.rrx_text), (6 bytes).
    Removing matrixfunctions.o(i.arm_householder_f32), (184 bytes).
    Removing matrixfunctions.o(i.arm_householder_f64), (284 bytes).
    Removing matrixfunctions.o(i.arm_mat_add_f32), (42 bytes).
    Removing matrixfunctions.o(i.arm_mat_add_q15), (42 bytes).
    Removing matrixfunctions.o(i.arm_mat_add_q31), (36 bytes).
    Removing matrixfunctions.o(i.arm_mat_cholesky_f32), (192 bytes).
    Removing matrixfunctions.o(i.arm_mat_cholesky_f64), (272 bytes).
    Removing matrixfunctions.o(i.arm_mat_cmplx_mult_f32), (152 bytes).
    Removing matrixfunctions.o(i.arm_mat_cmplx_mult_q15), (346 bytes).
    Removing matrixfunctions.o(i.arm_mat_cmplx_mult_q31), (246 bytes).
    Removing matrixfunctions.o(i.arm_mat_cmplx_trans_f32), (68 bytes).
    Removing matrixfunctions.o(i.arm_mat_cmplx_trans_q15), (66 bytes).
    Removing matrixfunctions.o(i.arm_mat_cmplx_trans_q31), (66 bytes).
    Removing matrixfunctions.o(i.arm_mat_init_f32), (8 bytes).
    Removing matrixfunctions.o(i.arm_mat_init_f64), (8 bytes).
    Removing matrixfunctions.o(i.arm_mat_init_q15), (8 bytes).
    Removing matrixfunctions.o(i.arm_mat_init_q31), (8 bytes).
    Removing matrixfunctions.o(i.arm_mat_inverse_f32), (696 bytes).
    Removing matrixfunctions.o(i.arm_mat_inverse_f64), (948 bytes).
    Removing matrixfunctions.o(i.arm_mat_ldlt_f32), (500 bytes).
    Removing matrixfunctions.o(i.arm_mat_ldlt_f64), (624 bytes).
    Removing matrixfunctions.o(i.arm_mat_mult_f32), (116 bytes).
    Removing matrixfunctions.o(i.arm_mat_mult_f64), (216 bytes).
    Removing matrixfunctions.o(i.arm_mat_mult_fast_q15), (782 bytes).
    Removing matrixfunctions.o(i.arm_mat_mult_fast_q31), (420 bytes).
    Removing matrixfunctions.o(i.arm_mat_mult_opt_q31), (134 bytes).
    Removing matrixfunctions.o(i.arm_mat_mult_q15), (214 bytes).
    Removing matrixfunctions.o(i.arm_mat_mult_q31), (134 bytes).
    Removing matrixfunctions.o(i.arm_mat_mult_q7), (128 bytes).
    Removing matrixfunctions.o(i.arm_mat_qr_f32), (1132 bytes).
    Removing matrixfunctions.o(i.arm_mat_qr_f64), (1568 bytes).
    Removing matrixfunctions.o(i.arm_mat_scale_f32), (34 bytes).
    Removing matrixfunctions.o(i.arm_mat_scale_q15), (48 bytes).
    Removing matrixfunctions.o(i.arm_mat_scale_q31), (54 bytes).
    Removing matrixfunctions.o(i.arm_mat_solve_lower_triangular_f32), (144 bytes).
    Removing matrixfunctions.o(i.arm_mat_solve_lower_triangular_f64), (216 bytes).
    Removing matrixfunctions.o(i.arm_mat_solve_upper_triangular_f32), (142 bytes).
    Removing matrixfunctions.o(i.arm_mat_solve_upper_triangular_f64), (212 bytes).
    Removing matrixfunctions.o(i.arm_mat_sub_f32), (42 bytes).
    Removing matrixfunctions.o(i.arm_mat_sub_f64), (70 bytes).
    Removing matrixfunctions.o(i.arm_mat_sub_q15), (42 bytes).
    Removing matrixfunctions.o(i.arm_mat_sub_q31), (36 bytes).
    Removing matrixfunctions.o(i.arm_mat_trans_f32), (52 bytes).
    Removing matrixfunctions.o(i.arm_mat_trans_f64), (78 bytes).
    Removing matrixfunctions.o(i.arm_mat_trans_q15), (52 bytes).
    Removing matrixfunctions.o(i.arm_mat_trans_q31), (52 bytes).
    Removing matrixfunctions.o(i.arm_mat_trans_q7), (52 bytes).
    Removing matrixfunctions.o(i.arm_mat_vec_mult_f32), (256 bytes).
    Removing matrixfunctions.o(i.arm_mat_vec_mult_q15), (560 bytes).
    Removing matrixfunctions.o(i.arm_mat_vec_mult_q31), (336 bytes).
    Removing matrixfunctions.o(i.arm_mat_vec_mult_q7), (574 bytes).
    Removing matrixfunctionsf16.o(.rev16_text), (4 bytes).
    Removing matrixfunctionsf16.o(.revsh_text), (4 bytes).
    Removing matrixfunctionsf16.o(.rrx_text), (6 bytes).
    Removing quaternionmathfunctions.o(.rev16_text), (4 bytes).
    Removing quaternionmathfunctions.o(.revsh_text), (4 bytes).
    Removing quaternionmathfunctions.o(.rrx_text), (6 bytes).
    Removing quaternionmathfunctions.o(i.arm_quaternion2rotation_f32), (204 bytes).
    Removing quaternionmathfunctions.o(i.arm_quaternion_conjugate_f32), (68 bytes).
    Removing quaternionmathfunctions.o(i.arm_quaternion_inverse_f32), (112 bytes).
    Removing quaternionmathfunctions.o(i.arm_quaternion_norm_f32), (74 bytes).
    Removing quaternionmathfunctions.o(i.arm_quaternion_normalize_f32), (118 bytes).
    Removing quaternionmathfunctions.o(i.arm_quaternion_product_f32), (36 bytes).
    Removing quaternionmathfunctions.o(i.arm_quaternion_product_single_f32), (210 bytes).
    Removing quaternionmathfunctions.o(i.arm_rotation2quaternion_f32), (464 bytes).
    Removing svmfunctions.o(.rev16_text), (4 bytes).
    Removing svmfunctions.o(.revsh_text), (4 bytes).
    Removing svmfunctions.o(.rrx_text), (6 bytes).
    Removing svmfunctions.o(i.arm_svm_linear_init_f32), (22 bytes).
    Removing svmfunctions.o(i.arm_svm_linear_predict_f32), (104 bytes).
    Removing svmfunctions.o(i.arm_svm_polynomial_init_f32), (28 bytes).
    Removing svmfunctions.o(i.arm_svm_polynomial_predict_f32), (136 bytes).
    Removing svmfunctions.o(i.arm_svm_rbf_init_f32), (28 bytes).
    Removing svmfunctions.o(i.arm_svm_rbf_predict_f32), (140 bytes).
    Removing svmfunctions.o(i.arm_svm_sigmoid_init_f32), (24 bytes).
    Removing svmfunctions.o(i.arm_svm_sigmoid_predict_f32), (140 bytes).
    Removing svmfunctionsf16.o(.rev16_text), (4 bytes).
    Removing svmfunctionsf16.o(.revsh_text), (4 bytes).
    Removing svmfunctionsf16.o(.rrx_text), (6 bytes).
    Removing statisticsfunctions.o(.rev16_text), (4 bytes).
    Removing statisticsfunctions.o(.revsh_text), (4 bytes).
    Removing statisticsfunctions.o(.rrx_text), (6 bytes).
    Removing statisticsfunctions.o(i.arm_absmax_f32), (54 bytes).
    Removing statisticsfunctions.o(i.arm_absmax_f64), (102 bytes).
    Removing statisticsfunctions.o(i.arm_absmax_no_idx_f32), (44 bytes).
    Removing statisticsfunctions.o(i.arm_absmax_no_idx_f64), (84 bytes).
    Removing statisticsfunctions.o(i.arm_absmax_no_idx_q15), (146 bytes).
    Removing statisticsfunctions.o(i.arm_absmax_no_idx_q31), (122 bytes).
    Removing statisticsfunctions.o(i.arm_absmax_no_idx_q7), (146 bytes).
    Removing statisticsfunctions.o(i.arm_absmax_q15), (182 bytes).
    Removing statisticsfunctions.o(i.arm_absmax_q31), (156 bytes).
    Removing statisticsfunctions.o(i.arm_absmax_q7), (182 bytes).
    Removing statisticsfunctions.o(i.arm_absmin_f32), (54 bytes).
    Removing statisticsfunctions.o(i.arm_absmin_f64), (102 bytes).
    Removing statisticsfunctions.o(i.arm_absmin_no_idx_f32), (44 bytes).
    Removing statisticsfunctions.o(i.arm_absmin_no_idx_f64), (84 bytes).
    Removing statisticsfunctions.o(i.arm_absmin_no_idx_q15), (146 bytes).
    Removing statisticsfunctions.o(i.arm_absmin_no_idx_q31), (122 bytes).
    Removing statisticsfunctions.o(i.arm_absmin_no_idx_q7), (146 bytes).
    Removing statisticsfunctions.o(i.arm_absmin_q15), (182 bytes).
    Removing statisticsfunctions.o(i.arm_absmin_q31), (156 bytes).
    Removing statisticsfunctions.o(i.arm_absmin_q7), (182 bytes).
    Removing statisticsfunctions.o(i.arm_accumulate_f32), (32 bytes).
    Removing statisticsfunctions.o(i.arm_accumulate_f64), (60 bytes).
    Removing statisticsfunctions.o(i.arm_entropy_f32), (52 bytes).
    Removing statisticsfunctions.o(i.arm_entropy_f64), (88 bytes).
    Removing statisticsfunctions.o(i.arm_kullback_leibler_f32), (60 bytes).
    Removing statisticsfunctions.o(i.arm_kullback_leibler_f64), (104 bytes).
    Removing statisticsfunctions.o(i.arm_logsumexp_dot_prod_f32), (26 bytes).
    Removing statisticsfunctions.o(i.arm_logsumexp_f32), (96 bytes).
    Removing statisticsfunctions.o(i.arm_max_f32), (46 bytes).
    Removing statisticsfunctions.o(i.arm_max_f64), (78 bytes).
    Removing statisticsfunctions.o(i.arm_max_no_idx_f32), (44 bytes).
    Removing statisticsfunctions.o(i.arm_max_no_idx_f64), (72 bytes).
    Removing statisticsfunctions.o(i.arm_max_no_idx_q15), (26 bytes).
    Removing statisticsfunctions.o(i.arm_max_no_idx_q31), (22 bytes).
    Removing statisticsfunctions.o(i.arm_max_no_idx_q7), (26 bytes).
    Removing statisticsfunctions.o(i.arm_max_q15), (36 bytes).
    Removing statisticsfunctions.o(i.arm_max_q31), (32 bytes).
    Removing statisticsfunctions.o(i.arm_max_q7), (36 bytes).
    Removing statisticsfunctions.o(i.arm_mean_f32), (44 bytes).
    Removing statisticsfunctions.o(i.arm_mean_f64), (96 bytes).
    Removing statisticsfunctions.o(i.arm_mean_q15), (28 bytes).
    Removing statisticsfunctions.o(i.arm_mean_q31), (44 bytes).
    Removing statisticsfunctions.o(i.arm_mean_q7), (28 bytes).
    Removing statisticsfunctions.o(i.arm_min_f32), (46 bytes).
    Removing statisticsfunctions.o(i.arm_min_f64), (78 bytes).
    Removing statisticsfunctions.o(i.arm_min_no_idx_f32), (44 bytes).
    Removing statisticsfunctions.o(i.arm_min_no_idx_f64), (72 bytes).
    Removing statisticsfunctions.o(i.arm_min_no_idx_q15), (26 bytes).
    Removing statisticsfunctions.o(i.arm_min_no_idx_q31), (22 bytes).
    Removing statisticsfunctions.o(i.arm_min_no_idx_q7), (26 bytes).
    Removing statisticsfunctions.o(i.arm_min_q15), (36 bytes).
    Removing statisticsfunctions.o(i.arm_min_q31), (32 bytes).
    Removing statisticsfunctions.o(i.arm_min_q7), (36 bytes).
    Removing statisticsfunctions.o(i.arm_mse_f32), (56 bytes).
    Removing statisticsfunctions.o(i.arm_mse_f64), (116 bytes).
    Removing statisticsfunctions.o(i.arm_mse_q15), (66 bytes).
    Removing statisticsfunctions.o(i.arm_mse_q31), (78 bytes).
    Removing statisticsfunctions.o(i.arm_mse_q7), (52 bytes).
    Removing statisticsfunctions.o(i.arm_power_f32), (32 bytes).
    Removing statisticsfunctions.o(i.arm_power_f64), (72 bytes).
    Removing statisticsfunctions.o(i.arm_power_q15), (28 bytes).
    Removing statisticsfunctions.o(i.arm_power_q31), (40 bytes).
    Removing statisticsfunctions.o(i.arm_power_q7), (26 bytes).
    Removing statisticsfunctions.o(i.arm_rms_f32), (68 bytes).
    Removing statisticsfunctions.o(i.arm_rms_q15), (58 bytes).
    Removing statisticsfunctions.o(i.arm_rms_q31), (68 bytes).
    Removing statisticsfunctions.o(i.arm_std_f32), (48 bytes).
    Removing statisticsfunctions.o(i.arm_std_f64), (24 bytes).
    Removing statisticsfunctions.o(i.arm_std_q15), (92 bytes).
    Removing statisticsfunctions.o(i.arm_std_q31), (124 bytes).
    Removing statisticsfunctions.o(i.arm_var_f32), (108 bytes).
    Removing statisticsfunctions.o(i.arm_var_f64), (152 bytes).
    Removing statisticsfunctions.o(i.arm_var_q15), (82 bytes).
    Removing statisticsfunctions.o(i.arm_var_q31), (120 bytes).
    Removing statisticsfunctionsf16.o(.rev16_text), (4 bytes).
    Removing statisticsfunctionsf16.o(.revsh_text), (4 bytes).
    Removing statisticsfunctionsf16.o(.rrx_text), (6 bytes).
    Removing supportfunctions.o(.rev16_text), (4 bytes).
    Removing supportfunctions.o(.revsh_text), (4 bytes).
    Removing supportfunctions.o(.rrx_text), (6 bytes).
    Removing supportfunctions.o(i.arm_barycenter_f32), (104 bytes).
    Removing supportfunctions.o(i.arm_bitonic_sort_core_f32), (136 bytes).
    Removing supportfunctions.o(i.arm_bitonic_sort_f32), (80 bytes).
    Removing supportfunctions.o(i.arm_bubble_sort_f32), (90 bytes).
    Removing supportfunctions.o(i.arm_copy_f32), (20 bytes).
    Removing supportfunctions.o(i.arm_copy_f64), (20 bytes).
    Removing supportfunctions.o(i.arm_copy_q15), (20 bytes).
    Removing supportfunctions.o(i.arm_copy_q31), (14 bytes).
    Removing supportfunctions.o(i.arm_copy_q7), (20 bytes).
    Removing supportfunctions.o(i.arm_f64_to_float), (34 bytes).
    Removing supportfunctions.o(i.arm_f64_to_q15), (72 bytes).
    Removing supportfunctions.o(i.arm_f64_to_q31), (72 bytes).
    Removing supportfunctions.o(i.arm_f64_to_q7), (72 bytes).
    Removing supportfunctions.o(i.arm_fill_f32), (16 bytes).
    Removing supportfunctions.o(i.arm_fill_f64), (16 bytes).
    Removing supportfunctions.o(i.arm_fill_q15), (16 bytes).
    Removing supportfunctions.o(i.arm_fill_q31), (12 bytes).
    Removing supportfunctions.o(i.arm_fill_q7), (16 bytes).
    Removing supportfunctions.o(i.arm_float_to_f64), (28 bytes).
    Removing supportfunctions.o(i.arm_float_to_q15), (44 bytes).
    Removing supportfunctions.o(i.arm_float_to_q31), (60 bytes).
    Removing supportfunctions.o(i.arm_float_to_q7), (44 bytes).
    Removing supportfunctions.o(i.arm_heap_sort_f32), (94 bytes).
    Removing supportfunctions.o(i.arm_heapify), (136 bytes).
    Removing supportfunctions.o(i.arm_insertion_sort_f32), (88 bytes).
    Removing supportfunctions.o(i.arm_merge_sort_core_f32), (156 bytes).
    Removing supportfunctions.o(i.arm_merge_sort_f32), (60 bytes).
    Removing supportfunctions.o(i.arm_merge_sort_init_f32), (6 bytes).
    Removing supportfunctions.o(i.arm_q15_to_f64), (60 bytes).
    Removing supportfunctions.o(i.arm_q15_to_float), (40 bytes).
    Removing supportfunctions.o(i.arm_q15_to_q31), (20 bytes).
    Removing supportfunctions.o(i.arm_q15_to_q7), (22 bytes).
    Removing supportfunctions.o(i.arm_q31_to_f64), (56 bytes).
    Removing supportfunctions.o(i.arm_q31_to_float), (36 bytes).
    Removing supportfunctions.o(i.arm_q31_to_q15), (18 bytes).
    Removing supportfunctions.o(i.arm_q31_to_q7), (18 bytes).
    Removing supportfunctions.o(i.arm_q7_to_f64), (60 bytes).
    Removing supportfunctions.o(i.arm_q7_to_float), (40 bytes).
    Removing supportfunctions.o(i.arm_q7_to_q15), (22 bytes).
    Removing supportfunctions.o(i.arm_q7_to_q31), (20 bytes).
    Removing supportfunctions.o(i.arm_quick_sort_core_f32), (178 bytes).
    Removing supportfunctions.o(i.arm_quick_sort_f32), (40 bytes).
    Removing supportfunctions.o(i.arm_selection_sort_f32), (122 bytes).
    Removing supportfunctions.o(i.arm_sort_f32), (58 bytes).
    Removing supportfunctions.o(i.arm_sort_init_f32), (6 bytes).
    Removing supportfunctions.o(i.arm_weighted_sum_f32), (44 bytes).
    Removing supportfunctionsf16.o(.rev16_text), (4 bytes).
    Removing supportfunctionsf16.o(.revsh_text), (4 bytes).
    Removing supportfunctionsf16.o(.rrx_text), (6 bytes).
    Removing transformfunctions.o(.rev16_text), (4 bytes).
    Removing transformfunctions.o(.revsh_text), (4 bytes).
    Removing transformfunctions.o(.rrx_text), (6 bytes).
    Removing transformfunctions.o(i.arm_bitreversal_16), (64 bytes).
    Removing transformfunctions.o(i.arm_bitreversal_64), (108 bytes).
    Removing transformfunctions.o(i.arm_bitreversal_f32), (168 bytes).
    Removing transformfunctions.o(i.arm_bitreversal_q15), (102 bytes).
    Removing transformfunctions.o(i.arm_bitreversal_q31), (170 bytes).
    Removing transformfunctions.o(i.arm_cfft_f64), (256 bytes).
    Removing transformfunctions.o(i.arm_cfft_init_f32), (148 bytes).
    Removing transformfunctions.o(i.arm_cfft_init_f64), (148 bytes).
    Removing transformfunctions.o(i.arm_cfft_init_q15), (148 bytes).
    Removing transformfunctions.o(i.arm_cfft_init_q31), (148 bytes).
    Removing transformfunctions.o(i.arm_cfft_q15), (172 bytes).
    Removing transformfunctions.o(i.arm_cfft_q31), (172 bytes).
    Removing transformfunctions.o(i.arm_cfft_radix2_f32), (60 bytes).
    Removing transformfunctions.o(i.arm_cfft_radix2_init_f32), (284 bytes).
    Removing transformfunctions.o(i.arm_cfft_radix2_init_q15), (192 bytes).
    Removing transformfunctions.o(i.arm_cfft_radix2_init_q31), (192 bytes).
    Removing transformfunctions.o(i.arm_cfft_radix2_q15), (46 bytes).
    Removing transformfunctions.o(i.arm_cfft_radix2_q31), (46 bytes).
    Removing transformfunctions.o(i.arm_cfft_radix4_f32), (60 bytes).
    Removing transformfunctions.o(i.arm_cfft_radix4_init_f32), (176 bytes).
    Removing transformfunctions.o(i.arm_cfft_radix4_init_q15), (124 bytes).
    Removing transformfunctions.o(i.arm_cfft_radix4_init_q31), (124 bytes).
    Removing transformfunctions.o(i.arm_cfft_radix4_q15), (54 bytes).
    Removing transformfunctions.o(i.arm_cfft_radix4_q31), (54 bytes).
    Removing transformfunctions.o(i.arm_cfft_radix4by2_f64), (286 bytes).
    Removing transformfunctions.o(i.arm_cfft_radix4by2_inverse_q15), (190 bytes).
    Removing transformfunctions.o(i.arm_cfft_radix4by2_inverse_q31), (190 bytes).
    Removing transformfunctions.o(i.arm_cfft_radix4by2_q15), (190 bytes).
    Removing transformfunctions.o(i.arm_cfft_radix4by2_q31), (190 bytes).
    Removing transformfunctions.o(i.arm_dct4_f32), (196 bytes).
    Removing transformfunctions.o(i.arm_dct4_init_f32), (132 bytes).
    Removing transformfunctions.o(i.arm_dct4_init_q15), (128 bytes).
    Removing transformfunctions.o(i.arm_dct4_init_q31), (128 bytes).
    Removing transformfunctions.o(i.arm_dct4_q15), (180 bytes).
    Removing transformfunctions.o(i.arm_dct4_q31), (170 bytes).
    Removing transformfunctions.o(i.arm_mfcc_f32), (240 bytes).
    Removing transformfunctions.o(i.arm_mfcc_init_f32), (34 bytes).
    Removing transformfunctions.o(i.arm_mfcc_init_q15), (36 bytes).
    Removing transformfunctions.o(i.arm_mfcc_init_q31), (36 bytes).
    Removing transformfunctions.o(i.arm_mfcc_q15), (324 bytes).
    Removing transformfunctions.o(i.arm_mfcc_q31), (296 bytes).
    Removing transformfunctions.o(i.arm_radix2_butterfly_f32), (334 bytes).
    Removing transformfunctions.o(i.arm_radix2_butterfly_inverse_f32), (342 bytes).
    Removing transformfunctions.o(i.arm_radix2_butterfly_inverse_q15), (614 bytes).
    Removing transformfunctions.o(i.arm_radix2_butterfly_inverse_q31), (440 bytes).
    Removing transformfunctions.o(i.arm_radix2_butterfly_q15), (658 bytes).
    Removing transformfunctions.o(i.arm_radix2_butterfly_q31), (432 bytes).
    Removing transformfunctions.o(i.arm_radix4_butterfly_f32), (324 bytes).
    Removing transformfunctions.o(i.arm_radix4_butterfly_f64), (768 bytes).
    Removing transformfunctions.o(i.arm_radix4_butterfly_inverse_f32), (534 bytes).
    Removing transformfunctions.o(i.arm_radix4_butterfly_inverse_q15), (908 bytes).
    Removing transformfunctions.o(i.arm_radix4_butterfly_inverse_q31), (944 bytes).
    Removing transformfunctions.o(i.arm_radix4_butterfly_q15), (908 bytes).
    Removing transformfunctions.o(i.arm_radix4_butterfly_q31), (944 bytes).
    Removing transformfunctions.o(i.arm_rfft_1024_fast_init_f32), (44 bytes).
    Removing transformfunctions.o(i.arm_rfft_1024_fast_init_f64), (52 bytes).
    Removing transformfunctions.o(i.arm_rfft_128_fast_init_f32), (40 bytes).
    Removing transformfunctions.o(i.arm_rfft_128_fast_init_f64), (48 bytes).
    Removing transformfunctions.o(i.arm_rfft_2048_fast_init_f32), (44 bytes).
    Removing transformfunctions.o(i.arm_rfft_2048_fast_init_f64), (52 bytes).
    Removing transformfunctions.o(i.arm_rfft_256_fast_init_f32), (40 bytes).
    Removing transformfunctions.o(i.arm_rfft_256_fast_init_f64), (48 bytes).
    Removing transformfunctions.o(i.arm_rfft_32_fast_init_f32), (40 bytes).
    Removing transformfunctions.o(i.arm_rfft_32_fast_init_f64), (48 bytes).
    Removing transformfunctions.o(i.arm_rfft_4096_fast_init_f32), (44 bytes).
    Removing transformfunctions.o(i.arm_rfft_4096_fast_init_f64), (52 bytes).
    Removing transformfunctions.o(i.arm_rfft_512_fast_init_f32), (44 bytes).
    Removing transformfunctions.o(i.arm_rfft_512_fast_init_f64), (52 bytes).
    Removing transformfunctions.o(i.arm_rfft_64_fast_init_f32), (40 bytes).
    Removing transformfunctions.o(i.arm_rfft_64_fast_init_f64), (48 bytes).
    Removing transformfunctions.o(i.arm_rfft_f32), (126 bytes).
    Removing transformfunctions.o(i.arm_rfft_fast_f32), (54 bytes).
    Removing transformfunctions.o(i.arm_rfft_fast_f64), (68 bytes).
    Removing transformfunctions.o(i.arm_rfft_fast_init_f32), (120 bytes).
    Removing transformfunctions.o(i.arm_rfft_fast_init_f64), (120 bytes).
    Removing transformfunctions.o(i.arm_rfft_init_f32), (116 bytes).
    Removing transformfunctions.o(i.arm_rfft_init_q15), (200 bytes).
    Removing transformfunctions.o(i.arm_rfft_init_q31), (200 bytes).
    Removing transformfunctions.o(i.arm_rfft_q15), (100 bytes).
    Removing transformfunctions.o(i.arm_rfft_q31), (100 bytes).
    Removing transformfunctions.o(i.arm_split_rfft_f32), (200 bytes).
    Removing transformfunctions.o(i.arm_split_rfft_q15), (276 bytes).
    Removing transformfunctions.o(i.arm_split_rfft_q31), (226 bytes).
    Removing transformfunctions.o(i.arm_split_rifft_f32), (110 bytes).
    Removing transformfunctions.o(i.arm_split_rifft_q15), (166 bytes).
    Removing transformfunctions.o(i.arm_split_rifft_q31), (100 bytes).
    Removing transformfunctions.o(i.merge_rfft_f32), (160 bytes).
    Removing transformfunctions.o(i.merge_rfft_f64), (332 bytes).
    Removing transformfunctions.o(i.stage_rfft_f32), (164 bytes).
    Removing transformfunctions.o(i.stage_rfft_f64), (360 bytes).
    Removing transformfunctionsf16.o(.rev16_text), (4 bytes).
    Removing transformfunctionsf16.o(.revsh_text), (4 bytes).
    Removing transformfunctionsf16.o(.rrx_text), (6 bytes).
    Removing dneg.o(.text), (6 bytes).
    Removing dflti.o(.text), (34 bytes).
    Removing ffixl.o(.text), (66 bytes).
    Removing dfixi.o(.text), (62 bytes).
    Removing dfixl.o(.text), (74 bytes).
    Removing cdcmple.o(.text), (48 bytes).
    Removing d2f.o(.text), (56 bytes).
    Removing fepilogue.o(.text), (110 bytes).
    Removing frem.o(.text), (82 bytes).
    Removing dscalb.o(.text), (46 bytes).
    Removing dsqrt.o(.text), (162 bytes).
    Removing fpstat.o(.text), (4 bytes).

1311 unused section(s) (total 1032873 bytes) removed from the image.

==============================================================================

Image Symbol Table

    Local Symbols

    Symbol Name                              Value     Ov Type        Size  Object(Section)

    ../Core/Src/adc.c                        0x00000000   Number         0  adc.o ABSOLUTE
    ../Core/Src/dma.c                        0x00000000   Number         0  dma.o ABSOLUTE
    ../Core/Src/gpio.c                       0x00000000   Number         0  gpio.o ABSOLUTE
    ../Core/Src/main.c                       0x00000000   Number         0  main.o ABSOLUTE
    ../Core/Src/stm32f4xx_hal_msp.c          0x00000000   Number         0  stm32f4xx_hal_msp.o ABSOLUTE
    ../Core/Src/stm32f4xx_it.c               0x00000000   Number         0  stm32f4xx_it.o ABSOLUTE
    ../Core/Src/system_stm32f4xx.c           0x00000000   Number         0  system_stm32f4xx.o ABSOLUTE
    ../Core/Src/tim.c                        0x00000000   Number         0  tim.o ABSOLUTE
    ../Core/Src/usart.c                      0x00000000   Number         0  usart.o ABSOLUTE
    ../Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal.c 0x00000000   Number         0  stm32f4xx_hal.o ABSOLUTE
    ../Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_adc.c 0x00000000   Number         0  stm32f4xx_hal_adc.o ABSOLUTE
    ../Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_adc_ex.c 0x00000000   Number         0  stm32f4xx_hal_adc_ex.o ABSOLUTE
    ../Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_cortex.c 0x00000000   Number         0  stm32f4xx_hal_cortex.o ABSOLUTE
    ../Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_dma.c 0x00000000   Number         0  stm32f4xx_hal_dma.o ABSOLUTE
    ../Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_dma_ex.c 0x00000000   Number         0  stm32f4xx_hal_dma_ex.o ABSOLUTE
    ../Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_exti.c 0x00000000   Number         0  stm32f4xx_hal_exti.o ABSOLUTE
    ../Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_flash.c 0x00000000   Number         0  stm32f4xx_hal_flash.o ABSOLUTE
    ../Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_flash_ex.c 0x00000000   Number         0  stm32f4xx_hal_flash_ex.o ABSOLUTE
    ../Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_flash_ramfunc.c 0x00000000   Number         0  stm32f4xx_hal_flash_ramfunc.o ABSOLUTE
    ../Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_gpio.c 0x00000000   Number         0  stm32f4xx_hal_gpio.o ABSOLUTE
    ../Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_pwr.c 0x00000000   Number         0  stm32f4xx_hal_pwr.o ABSOLUTE
    ../Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_pwr_ex.c 0x00000000   Number         0  stm32f4xx_hal_pwr_ex.o ABSOLUTE
    ../Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_rcc.c 0x00000000   Number         0  stm32f4xx_hal_rcc.o ABSOLUTE
    ../Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_rcc_ex.c 0x00000000   Number         0  stm32f4xx_hal_rcc_ex.o ABSOLUTE
    ../Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_tim.c 0x00000000   Number         0  stm32f4xx_hal_tim.o ABSOLUTE
    ../Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_tim_ex.c 0x00000000   Number         0  stm32f4xx_hal_tim_ex.o ABSOLUTE
    ../Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_uart.c 0x00000000   Number         0  stm32f4xx_hal_uart.o ABSOLUTE
    ../Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_ll_adc.c 0x00000000   Number         0  stm32f4xx_ll_adc.o ABSOLUTE
    ../clib/microlib/division.c              0x00000000   Number         0  uidiv.o ABSOLUTE
    ../clib/microlib/division.c              0x00000000   Number         0  uldiv.o ABSOLUTE
    ../clib/microlib/division.c              0x00000000   Number         0  ldiv.o ABSOLUTE
    ../clib/microlib/errno.c                 0x00000000   Number         0  errno.o ABSOLUTE
    ../clib/microlib/init/entry.s            0x00000000   Number         0  entry11b.o ABSOLUTE
    ../clib/microlib/init/entry.s            0x00000000   Number         0  entry11a.o ABSOLUTE
    ../clib/microlib/init/entry.s            0x00000000   Number         0  entry10b.o ABSOLUTE
    ../clib/microlib/init/entry.s            0x00000000   Number         0  entry10a.o ABSOLUTE
    ../clib/microlib/init/entry.s            0x00000000   Number         0  entry9a.o ABSOLUTE
    ../clib/microlib/init/entry.s            0x00000000   Number         0  entry8b.o ABSOLUTE
    ../clib/microlib/init/entry.s            0x00000000   Number         0  entry8a.o ABSOLUTE
    ../clib/microlib/init/entry.s            0x00000000   Number         0  entry7b.o ABSOLUTE
    ../clib/microlib/init/entry.s            0x00000000   Number         0  entry7a.o ABSOLUTE
    ../clib/microlib/init/entry.s            0x00000000   Number         0  entry5.o ABSOLUTE
    ../clib/microlib/init/entry.s            0x00000000   Number         0  entry2.o ABSOLUTE
    ../clib/microlib/init/entry.s            0x00000000   Number         0  entry9b.o ABSOLUTE
    ../clib/microlib/init/entry.s            0x00000000   Number         0  entry.o ABSOLUTE
    ../clib/microlib/longlong.c              0x00000000   Number         0  llshl.o ABSOLUTE
    ../clib/microlib/longlong.c              0x00000000   Number         0  llsshr.o ABSOLUTE
    ../clib/microlib/longlong.c              0x00000000   Number         0  llushr.o ABSOLUTE
    ../clib/microlib/printf/printf.c         0x00000000   Number         0  printfb.o ABSOLUTE
    ../clib/microlib/printf/printf.c         0x00000000   Number         0  printfa.o ABSOLUTE
    ../clib/microlib/printf/printf.c         0x00000000   Number         0  printf8.o ABSOLUTE
    ../clib/microlib/printf/printf.c         0x00000000   Number         0  printf7.o ABSOLUTE
    ../clib/microlib/printf/printf.c         0x00000000   Number         0  printf6.o ABSOLUTE
    ../clib/microlib/printf/printf.c         0x00000000   Number         0  printf5.o ABSOLUTE
    ../clib/microlib/printf/printf.c         0x00000000   Number         0  printf4.o ABSOLUTE
    ../clib/microlib/printf/printf.c         0x00000000   Number         0  printf3.o ABSOLUTE
    ../clib/microlib/printf/printf.c         0x00000000   Number         0  printf2.o ABSOLUTE
    ../clib/microlib/printf/printf.c         0x00000000   Number         0  printf1.o ABSOLUTE
    ../clib/microlib/printf/printf.c         0x00000000   Number         0  printf0.o ABSOLUTE
    ../clib/microlib/printf/stubs.s          0x00000000   Number         0  stubs.o ABSOLUTE
    ../clib/microlib/stdio/streams.c         0x00000000   Number         0  stdout.o ABSOLUTE
    ../clib/microlib/string/memcpy.c         0x00000000   Number         0  memcpyb.o ABSOLUTE
    ../clib/microlib/string/memcpy.c         0x00000000   Number         0  memcpya.o ABSOLUTE
    ../clib/microlib/string/memset.c         0x00000000   Number         0  memseta.o ABSOLUTE
    ../clib/microlib/stubs.s                 0x00000000   Number         0  iusefp.o ABSOLUTE
    ../fplib/microlib/d2f.c                  0x00000000   Number         0  d2f.o ABSOLUTE
    ../fplib/microlib/f2d.c                  0x00000000   Number         0  f2d.o ABSOLUTE
    ../fplib/microlib/fpadd.c                0x00000000   Number         0  dadd.o ABSOLUTE
    ../fplib/microlib/fpdiv.c                0x00000000   Number         0  ddiv.o ABSOLUTE
    ../fplib/microlib/fpepilogue.c           0x00000000   Number         0  fepilogue.o ABSOLUTE
    ../fplib/microlib/fpepilogue.c           0x00000000   Number         0  depilogue.o ABSOLUTE
    ../fplib/microlib/fpfix.c                0x00000000   Number         0  dfixl.o ABSOLUTE
    ../fplib/microlib/fpfix.c                0x00000000   Number         0  dfixui.o ABSOLUTE
    ../fplib/microlib/fpfix.c                0x00000000   Number         0  ffixl.o ABSOLUTE
    ../fplib/microlib/fpfix.c                0x00000000   Number         0  dfixi.o ABSOLUTE
    ../fplib/microlib/fpfix.c                0x00000000   Number         0  dfixul.o ABSOLUTE
    ../fplib/microlib/fpflt.c                0x00000000   Number         0  dflti.o ABSOLUTE
    ../fplib/microlib/fpflt.c                0x00000000   Number         0  dfltui.o ABSOLUTE
    ../fplib/microlib/fpmul.c                0x00000000   Number         0  dmul.o ABSOLUTE
    ../fplib/microlib/fpneg.c                0x00000000   Number         0  dneg.o ABSOLUTE
    ../fplib/microlib/fprem.c                0x00000000   Number         0  frem.o ABSOLUTE
    ../fplib/microlib/fpscalb.c              0x00000000   Number         0  dscalb.o ABSOLUTE
    ../fplib/microlib/fpsqrt.c               0x00000000   Number         0  dsqrt.o ABSOLUTE
    ../fplib/microlib/fpstat.c               0x00000000   Number         0  fpstat.o ABSOLUTE
    ../mathlib/cosf.c                        0x00000000   Number         0  cosf_x.o ABSOLUTE
    ../mathlib/cosf.c                        0x00000000   Number         0  cosf.o ABSOLUTE
    ../mathlib/dunder.c                      0x00000000   Number         0  dunder.o ABSOLUTE
    ../mathlib/exp.c                         0x00000000   Number         0  exp.o ABSOLUTE
    ../mathlib/exp.c                         0x00000000   Number         0  exp_x.o ABSOLUTE
    ../mathlib/expf.c                        0x00000000   Number         0  expf_x.o ABSOLUTE
    ../mathlib/expf.c                        0x00000000   Number         0  expf.o ABSOLUTE
    ../mathlib/fabs.c                        0x00000000   Number         0  fabs.o ABSOLUTE
    ../mathlib/fmodf.c                       0x00000000   Number         0  fmodf_x.o ABSOLUTE
    ../mathlib/fmodf.c                       0x00000000   Number         0  fmodf.o ABSOLUTE
    ../mathlib/fpclassify.c                  0x00000000   Number         0  fpclassify.o ABSOLUTE
    ../mathlib/fpclassifyf.c                 0x00000000   Number         0  fpclassifyf.o ABSOLUTE
    ../mathlib/funder.c                      0x00000000   Number         0  funder.o ABSOLUTE
    ../mathlib/log.c                         0x00000000   Number         0  log.o ABSOLUTE
    ../mathlib/log.c                         0x00000000   Number         0  log_x.o ABSOLUTE
    ../mathlib/logf.c                        0x00000000   Number         0  logf_x.o ABSOLUTE
    ../mathlib/logf.c                        0x00000000   Number         0  logf.o ABSOLUTE
    ../mathlib/poly.c                        0x00000000   Number         0  poly.o ABSOLUTE
    ../mathlib/pow.c                         0x00000000   Number         0  pow.o ABSOLUTE
    ../mathlib/pow.c                         0x00000000   Number         0  pow_x.o ABSOLUTE
    ../mathlib/powf.c                        0x00000000   Number         0  powf.o ABSOLUTE
    ../mathlib/powf.c                        0x00000000   Number         0  powf_x.o ABSOLUTE
    ../mathlib/qnan.c                        0x00000000   Number         0  qnan.o ABSOLUTE
    ../mathlib/rredf.c                       0x00000000   Number         0  rredf.o ABSOLUTE
    ../mathlib/sqrt.c                        0x00000000   Number         0  sqrt_x.o ABSOLUTE
    ../mathlib/sqrt.c                        0x00000000   Number         0  sqrt.o ABSOLUTE
    ../mathlib/sqrtf.c                       0x00000000   Number         0  sqrtf.o ABSOLUTE
    ../mathlib/sqrtf.c                       0x00000000   Number         0  sqrtf_x.o ABSOLUTE
    ../mathlib/tanhf.c                       0x00000000   Number         0  tanhf.o ABSOLUTE
    ../mathlib/tanhf.c                       0x00000000   Number         0  tanhf_x.o ABSOLUTE
    ..\Core\Src\adc.c                        0x00000000   Number         0  adc.o ABSOLUTE
    ..\Core\Src\dma.c                        0x00000000   Number         0  dma.o ABSOLUTE
    ..\Core\Src\gpio.c                       0x00000000   Number         0  gpio.o ABSOLUTE
    ..\Core\Src\main.c                       0x00000000   Number         0  main.o ABSOLUTE
    ..\Core\Src\stm32f4xx_hal_msp.c          0x00000000   Number         0  stm32f4xx_hal_msp.o ABSOLUTE
    ..\Core\Src\stm32f4xx_it.c               0x00000000   Number         0  stm32f4xx_it.o ABSOLUTE
    ..\Core\Src\system_stm32f4xx.c           0x00000000   Number         0  system_stm32f4xx.o ABSOLUTE
    ..\Core\Src\tim.c                        0x00000000   Number         0  tim.o ABSOLUTE
    ..\Core\Src\usart.c                      0x00000000   Number         0  usart.o ABSOLUTE
    ..\Drivers\STM32F4xx_HAL_Driver\Src\stm32f4xx_hal.c 0x00000000   Number         0  stm32f4xx_hal.o ABSOLUTE
    ..\Drivers\STM32F4xx_HAL_Driver\Src\stm32f4xx_hal_adc.c 0x00000000   Number         0  stm32f4xx_hal_adc.o ABSOLUTE
    ..\Drivers\STM32F4xx_HAL_Driver\Src\stm32f4xx_hal_adc_ex.c 0x00000000   Number         0  stm32f4xx_hal_adc_ex.o ABSOLUTE
    ..\Drivers\STM32F4xx_HAL_Driver\Src\stm32f4xx_hal_cortex.c 0x00000000   Number         0  stm32f4xx_hal_cortex.o ABSOLUTE
    ..\Drivers\STM32F4xx_HAL_Driver\Src\stm32f4xx_hal_dma.c 0x00000000   Number         0  stm32f4xx_hal_dma.o ABSOLUTE
    ..\Drivers\STM32F4xx_HAL_Driver\Src\stm32f4xx_hal_dma_ex.c 0x00000000   Number         0  stm32f4xx_hal_dma_ex.o ABSOLUTE
    ..\Drivers\STM32F4xx_HAL_Driver\Src\stm32f4xx_hal_exti.c 0x00000000   Number         0  stm32f4xx_hal_exti.o ABSOLUTE
    ..\Drivers\STM32F4xx_HAL_Driver\Src\stm32f4xx_hal_flash.c 0x00000000   Number         0  stm32f4xx_hal_flash.o ABSOLUTE
    ..\Drivers\STM32F4xx_HAL_Driver\Src\stm32f4xx_hal_flash_ex.c 0x00000000   Number         0  stm32f4xx_hal_flash_ex.o ABSOLUTE
    ..\Drivers\STM32F4xx_HAL_Driver\Src\stm32f4xx_hal_flash_ramfunc.c 0x00000000   Number         0  stm32f4xx_hal_flash_ramfunc.o ABSOLUTE
    ..\Drivers\STM32F4xx_HAL_Driver\Src\stm32f4xx_hal_gpio.c 0x00000000   Number         0  stm32f4xx_hal_gpio.o ABSOLUTE
    ..\Drivers\STM32F4xx_HAL_Driver\Src\stm32f4xx_hal_pwr.c 0x00000000   Number         0  stm32f4xx_hal_pwr.o ABSOLUTE
    ..\Drivers\STM32F4xx_HAL_Driver\Src\stm32f4xx_hal_pwr_ex.c 0x00000000   Number         0  stm32f4xx_hal_pwr_ex.o ABSOLUTE
    ..\Drivers\STM32F4xx_HAL_Driver\Src\stm32f4xx_hal_rcc.c 0x00000000   Number         0  stm32f4xx_hal_rcc.o ABSOLUTE
    ..\Drivers\STM32F4xx_HAL_Driver\Src\stm32f4xx_hal_rcc_ex.c 0x00000000   Number         0  stm32f4xx_hal_rcc_ex.o ABSOLUTE
    ..\Drivers\STM32F4xx_HAL_Driver\Src\stm32f4xx_hal_tim.c 0x00000000   Number         0  stm32f4xx_hal_tim.o ABSOLUTE
    ..\Drivers\STM32F4xx_HAL_Driver\Src\stm32f4xx_hal_tim_ex.c 0x00000000   Number         0  stm32f4xx_hal_tim_ex.o ABSOLUTE
    ..\Drivers\STM32F4xx_HAL_Driver\Src\stm32f4xx_hal_uart.c 0x00000000   Number         0  stm32f4xx_hal_uart.o ABSOLUTE
    D:/keil5/ARM/PACK/ARM/CMSIS-DSP/1.14.2/Source/BasicMathFunctions/BasicMathFunctions.c 0x00000000   Number         0  basicmathfunctions.o ABSOLUTE
    D:/keil5/ARM/PACK/ARM/CMSIS-DSP/1.14.2/Source/BasicMathFunctions/BasicMathFunctionsF16.c 0x00000000   Number         0  basicmathfunctionsf16.o ABSOLUTE
    D:/keil5/ARM/PACK/ARM/CMSIS-DSP/1.14.2/Source/BayesFunctions/BayesFunctions.c 0x00000000   Number         0  bayesfunctions.o ABSOLUTE
    D:/keil5/ARM/PACK/ARM/CMSIS-DSP/1.14.2/Source/BayesFunctions/BayesFunctionsF16.c 0x00000000   Number         0  bayesfunctionsf16.o ABSOLUTE
    D:/keil5/ARM/PACK/ARM/CMSIS-DSP/1.14.2/Source/CommonTables/CommonTables.c 0x00000000   Number         0  commontables.o ABSOLUTE
    D:/keil5/ARM/PACK/ARM/CMSIS-DSP/1.14.2/Source/CommonTables/CommonTablesF16.c 0x00000000   Number         0  commontablesf16.o ABSOLUTE
    D:/keil5/ARM/PACK/ARM/CMSIS-DSP/1.14.2/Source/ComplexMathFunctions/ComplexMathFunctions.c 0x00000000   Number         0  complexmathfunctions.o ABSOLUTE
    D:/keil5/ARM/PACK/ARM/CMSIS-DSP/1.14.2/Source/ComplexMathFunctions/ComplexMathFunctionsF16.c 0x00000000   Number         0  complexmathfunctionsf16.o ABSOLUTE
    D:/keil5/ARM/PACK/ARM/CMSIS-DSP/1.14.2/Source/ControllerFunctions/ControllerFunctions.c 0x00000000   Number         0  controllerfunctions.o ABSOLUTE
    D:/keil5/ARM/PACK/ARM/CMSIS-DSP/1.14.2/Source/DistanceFunctions/DistanceFunctions.c 0x00000000   Number         0  distancefunctions.o ABSOLUTE
    D:/keil5/ARM/PACK/ARM/CMSIS-DSP/1.14.2/Source/DistanceFunctions/DistanceFunctionsF16.c 0x00000000   Number         0  distancefunctionsf16.o ABSOLUTE
    D:/keil5/ARM/PACK/ARM/CMSIS-DSP/1.14.2/Source/FastMathFunctions/FastMathFunctions.c 0x00000000   Number         0  fastmathfunctions.o ABSOLUTE
    D:/keil5/ARM/PACK/ARM/CMSIS-DSP/1.14.2/Source/FastMathFunctions/FastMathFunctionsF16.c 0x00000000   Number         0  fastmathfunctionsf16.o ABSOLUTE
    D:/keil5/ARM/PACK/ARM/CMSIS-DSP/1.14.2/Source/FilteringFunctions/FilteringFunctions.c 0x00000000   Number         0  filteringfunctions.o ABSOLUTE
    D:/keil5/ARM/PACK/ARM/CMSIS-DSP/1.14.2/Source/FilteringFunctions/FilteringFunctionsF16.c 0x00000000   Number         0  filteringfunctionsf16.o ABSOLUTE
    D:/keil5/ARM/PACK/ARM/CMSIS-DSP/1.14.2/Source/InterpolationFunctions/InterpolationFunctions.c 0x00000000   Number         0  interpolationfunctions.o ABSOLUTE
    D:/keil5/ARM/PACK/ARM/CMSIS-DSP/1.14.2/Source/InterpolationFunctions/InterpolationFunctionsF16.c 0x00000000   Number         0  interpolationfunctionsf16.o ABSOLUTE
    D:/keil5/ARM/PACK/ARM/CMSIS-DSP/1.14.2/Source/MatrixFunctions/MatrixFunctions.c 0x00000000   Number         0  matrixfunctions.o ABSOLUTE
    D:/keil5/ARM/PACK/ARM/CMSIS-DSP/1.14.2/Source/MatrixFunctions/MatrixFunctionsF16.c 0x00000000   Number         0  matrixfunctionsf16.o ABSOLUTE
    D:/keil5/ARM/PACK/ARM/CMSIS-DSP/1.14.2/Source/QuaternionMathFunctions/QuaternionMathFunctions.c 0x00000000   Number         0  quaternionmathfunctions.o ABSOLUTE
    D:/keil5/ARM/PACK/ARM/CMSIS-DSP/1.14.2/Source/SVMFunctions/SVMFunctions.c 0x00000000   Number         0  svmfunctions.o ABSOLUTE
    D:/keil5/ARM/PACK/ARM/CMSIS-DSP/1.14.2/Source/SVMFunctions/SVMFunctionsF16.c 0x00000000   Number         0  svmfunctionsf16.o ABSOLUTE
    D:/keil5/ARM/PACK/ARM/CMSIS-DSP/1.14.2/Source/StatisticsFunctions/StatisticsFunctions.c 0x00000000   Number         0  statisticsfunctions.o ABSOLUTE
    D:/keil5/ARM/PACK/ARM/CMSIS-DSP/1.14.2/Source/StatisticsFunctions/StatisticsFunctionsF16.c 0x00000000   Number         0  statisticsfunctionsf16.o ABSOLUTE
    D:/keil5/ARM/PACK/ARM/CMSIS-DSP/1.14.2/Source/SupportFunctions/SupportFunctions.c 0x00000000   Number         0  supportfunctions.o ABSOLUTE
    D:/keil5/ARM/PACK/ARM/CMSIS-DSP/1.14.2/Source/SupportFunctions/SupportFunctionsF16.c 0x00000000   Number         0  supportfunctionsf16.o ABSOLUTE
    D:/keil5/ARM/PACK/ARM/CMSIS-DSP/1.14.2/Source/TransformFunctions/TransformFunctions.c 0x00000000   Number         0  transformfunctions.o ABSOLUTE
    D:/keil5/ARM/PACK/ARM/CMSIS-DSP/1.14.2/Source/TransformFunctions/TransformFunctionsF16.c 0x00000000   Number         0  transformfunctionsf16.o ABSOLUTE
    D:\keil5\ARM\PACK\ARM\CMSIS-DSP\1.14.2\Source\BasicMathFunctions\BasicMathFunctions.c 0x00000000   Number         0  basicmathfunctions.o ABSOLUTE
    D:\keil5\ARM\PACK\ARM\CMSIS-DSP\1.14.2\Source\BasicMathFunctions\BasicMathFunctionsF16.c 0x00000000   Number         0  basicmathfunctionsf16.o ABSOLUTE
    D:\keil5\ARM\PACK\ARM\CMSIS-DSP\1.14.2\Source\BayesFunctions\BayesFunctions.c 0x00000000   Number         0  bayesfunctions.o ABSOLUTE
    D:\keil5\ARM\PACK\ARM\CMSIS-DSP\1.14.2\Source\BayesFunctions\BayesFunctionsF16.c 0x00000000   Number         0  bayesfunctionsf16.o ABSOLUTE
    D:\keil5\ARM\PACK\ARM\CMSIS-DSP\1.14.2\Source\CommonTables\CommonTables.c 0x00000000   Number         0  commontables.o ABSOLUTE
    D:\keil5\ARM\PACK\ARM\CMSIS-DSP\1.14.2\Source\CommonTables\CommonTablesF16.c 0x00000000   Number         0  commontablesf16.o ABSOLUTE
    D:\keil5\ARM\PACK\ARM\CMSIS-DSP\1.14.2\Source\ComplexMathFunctions\ComplexMathFunctions.c 0x00000000   Number         0  complexmathfunctions.o ABSOLUTE
    D:\keil5\ARM\PACK\ARM\CMSIS-DSP\1.14.2\Source\ComplexMathFunctions\ComplexMathFunctionsF16.c 0x00000000   Number         0  complexmathfunctionsf16.o ABSOLUTE
    D:\keil5\ARM\PACK\ARM\CMSIS-DSP\1.14.2\Source\ControllerFunctions\ControllerFunctions.c 0x00000000   Number         0  controllerfunctions.o ABSOLUTE
    D:\keil5\ARM\PACK\ARM\CMSIS-DSP\1.14.2\Source\DistanceFunctions\DistanceFunctions.c 0x00000000   Number         0  distancefunctions.o ABSOLUTE
    D:\keil5\ARM\PACK\ARM\CMSIS-DSP\1.14.2\Source\DistanceFunctions\DistanceFunctionsF16.c 0x00000000   Number         0  distancefunctionsf16.o ABSOLUTE
    D:\keil5\ARM\PACK\ARM\CMSIS-DSP\1.14.2\Source\FastMathFunctions\FastMathFunctions.c 0x00000000   Number         0  fastmathfunctions.o ABSOLUTE
    D:\keil5\ARM\PACK\ARM\CMSIS-DSP\1.14.2\Source\FastMathFunctions\FastMathFunctionsF16.c 0x00000000   Number         0  fastmathfunctionsf16.o ABSOLUTE
    D:\keil5\ARM\PACK\ARM\CMSIS-DSP\1.14.2\Source\FilteringFunctions\FilteringFunctions.c 0x00000000   Number         0  filteringfunctions.o ABSOLUTE
    D:\keil5\ARM\PACK\ARM\CMSIS-DSP\1.14.2\Source\FilteringFunctions\FilteringFunctionsF16.c 0x00000000   Number         0  filteringfunctionsf16.o ABSOLUTE
    D:\keil5\ARM\PACK\ARM\CMSIS-DSP\1.14.2\Source\InterpolationFunctions\InterpolationFunctions.c 0x00000000   Number         0  interpolationfunctions.o ABSOLUTE
    D:\keil5\ARM\PACK\ARM\CMSIS-DSP\1.14.2\Source\InterpolationFunctions\InterpolationFunctionsF16.c 0x00000000   Number         0  interpolationfunctionsf16.o ABSOLUTE
    D:\keil5\ARM\PACK\ARM\CMSIS-DSP\1.14.2\Source\MatrixFunctions\MatrixFunctions.c 0x00000000   Number         0  matrixfunctions.o ABSOLUTE
    D:\keil5\ARM\PACK\ARM\CMSIS-DSP\1.14.2\Source\MatrixFunctions\MatrixFunctionsF16.c 0x00000000   Number         0  matrixfunctionsf16.o ABSOLUTE
    D:\keil5\ARM\PACK\ARM\CMSIS-DSP\1.14.2\Source\QuaternionMathFunctions\QuaternionMathFunctions.c 0x00000000   Number         0  quaternionmathfunctions.o ABSOLUTE
    D:\keil5\ARM\PACK\ARM\CMSIS-DSP\1.14.2\Source\SVMFunctions\SVMFunctions.c 0x00000000   Number         0  svmfunctions.o ABSOLUTE
    D:\keil5\ARM\PACK\ARM\CMSIS-DSP\1.14.2\Source\SVMFunctions\SVMFunctionsF16.c 0x00000000   Number         0  svmfunctionsf16.o ABSOLUTE
    D:\keil5\ARM\PACK\ARM\CMSIS-DSP\1.14.2\Source\StatisticsFunctions\StatisticsFunctions.c 0x00000000   Number         0  statisticsfunctions.o ABSOLUTE
    D:\keil5\ARM\PACK\ARM\CMSIS-DSP\1.14.2\Source\StatisticsFunctions\StatisticsFunctionsF16.c 0x00000000   Number         0  statisticsfunctionsf16.o ABSOLUTE
    D:\keil5\ARM\PACK\ARM\CMSIS-DSP\1.14.2\Source\SupportFunctions\SupportFunctions.c 0x00000000   Number         0  supportfunctions.o ABSOLUTE
    D:\keil5\ARM\PACK\ARM\CMSIS-DSP\1.14.2\Source\SupportFunctions\SupportFunctionsF16.c 0x00000000   Number         0  supportfunctionsf16.o ABSOLUTE
    D:\keil5\ARM\PACK\ARM\CMSIS-DSP\1.14.2\Source\TransformFunctions\TransformFunctions.c 0x00000000   Number         0  transformfunctions.o ABSOLUTE
    D:\keil5\ARM\PACK\ARM\CMSIS-DSP\1.14.2\Source\TransformFunctions\TransformFunctionsF16.c 0x00000000   Number         0  transformfunctionsf16.o ABSOLUTE
    USER\9833.c                              0x00000000   Number         0  9833.o ABSOLUTE
    USER\AD9959_NEW.C                        0x00000000   Number         0  ad9959_new.o ABSOLUTE
    USER\\9833.c                             0x00000000   Number         0  9833.o ABSOLUTE
    USER\\AD9959_NEW.C                       0x00000000   Number         0  ad9959_new.o ABSOLUTE
    USER\\fft_processing.c                   0x00000000   Number         0  fft_processing.o ABSOLUTE
    USER\\tm1637.c                           0x00000000   Number         0  tm1637.o ABSOLUTE
    USER\fft_processing.c                    0x00000000   Number         0  fft_processing.o ABSOLUTE
    USER\tm1637.c                            0x00000000   Number         0  tm1637.o ABSOLUTE
    cdcmple.s                                0x00000000   Number         0  cdcmple.o ABSOLUTE
    cdrcmple.s                               0x00000000   Number         0  cdrcmple.o ABSOLUTE
    dc.s                                     0x00000000   Number         0  dc.o ABSOLUTE
    handlers.s                               0x00000000   Number         0  handlers.o ABSOLUTE
    init.s                                   0x00000000   Number         0  init.o ABSOLUTE
    startup_stm32f407xx.s                    0x00000000   Number         0  startup_stm32f407xx.o ABSOLUTE
    RESET                                    0x08000000   Section      392  startup_stm32f407xx.o(RESET)
    .ARM.Collect$$$$00000000                 0x08000188   Section        0  entry.o(.ARM.Collect$$$$00000000)
    .ARM.Collect$$$$00000001                 0x08000188   Section        4  entry2.o(.ARM.Collect$$$$00000001)
    .ARM.Collect$$$$00000004                 0x0800018c   Section        4  entry5.o(.ARM.Collect$$$$00000004)
    .ARM.Collect$$$$00000008                 0x08000190   Section        0  entry7b.o(.ARM.Collect$$$$00000008)
    .ARM.Collect$$$$0000000A                 0x08000190   Section        0  entry8b.o(.ARM.Collect$$$$0000000A)
    .ARM.Collect$$$$0000000B                 0x08000190   Section        8  entry9a.o(.ARM.Collect$$$$0000000B)
    .ARM.Collect$$$$0000000D                 0x08000198   Section        0  entry10a.o(.ARM.Collect$$$$0000000D)
    .ARM.Collect$$$$0000000F                 0x08000198   Section        0  entry11a.o(.ARM.Collect$$$$0000000F)
    .ARM.Collect$$$$00002712                 0x08000198   Section        4  entry2.o(.ARM.Collect$$$$00002712)
    __lit__00000000                          0x08000198   Data           4  entry2.o(.ARM.Collect$$$$00002712)
    .text                                    0x0800019c   Section       36  startup_stm32f407xx.o(.text)
    $v0                                      0x0800019c   Number         0  startup_stm32f407xx.o(.text)
    .text                                    0x080001c0   Section        0  uldiv.o(.text)
    .text                                    0x08000222   Section        0  llshl.o(.text)
    .text                                    0x08000240   Section        0  llsshr.o(.text)
    .text                                    0x08000264   Section        0  memseta.o(.text)
    .text                                    0x08000288   Section        0  dadd.o(.text)
    .text                                    0x080003d6   Section        0  dmul.o(.text)
    .text                                    0x080004ba   Section        0  ddiv.o(.text)
    .text                                    0x08000598   Section        0  dfltui.o(.text)
    .text                                    0x080005b2   Section        0  dfixui.o(.text)
    .text                                    0x080005e4   Section        0  f2d.o(.text)
    .text                                    0x0800060c   Section       48  cdrcmple.o(.text)
    .text                                    0x0800063c   Section        0  uidiv.o(.text)
    .text                                    0x08000668   Section        0  llushr.o(.text)
    .text                                    0x08000688   Section        0  iusefp.o(.text)
    .text                                    0x08000688   Section        0  depilogue.o(.text)
    .text                                    0x08000742   Section        0  dfixul.o(.text)
    .text                                    0x08000774   Section       36  init.o(.text)
    i.AD9959MSGInit                          0x08000798   Section        0  ad9959_new.o(i.AD9959MSGInit)
    i.ADC_DMAConvCplt                        0x080007c4   Section        0  stm32f4xx_hal_adc.o(i.ADC_DMAConvCplt)
    ADC_DMAConvCplt                          0x080007c5   Thumb Code   110  stm32f4xx_hal_adc.o(i.ADC_DMAConvCplt)
    i.ADC_DMAError                           0x08000832   Section        0  stm32f4xx_hal_adc.o(i.ADC_DMAError)
    ADC_DMAError                             0x08000833   Thumb Code    22  stm32f4xx_hal_adc.o(i.ADC_DMAError)
    i.ADC_DMAHalfConvCplt                    0x08000848   Section        0  stm32f4xx_hal_adc.o(i.ADC_DMAHalfConvCplt)
    ADC_DMAHalfConvCplt                      0x08000849   Thumb Code    10  stm32f4xx_hal_adc.o(i.ADC_DMAHalfConvCplt)
    i.ADC_Init                               0x08000854   Section        0  stm32f4xx_hal_adc.o(i.ADC_Init)
    ADC_Init                                 0x08000855   Thumb Code   284  stm32f4xx_hal_adc.o(i.ADC_Init)
    i.BusFault_Handler                       0x0800097c   Section        0  stm32f4xx_it.o(i.BusFault_Handler)
    i.DMA2_Stream0_IRQHandler                0x08000980   Section        0  stm32f4xx_it.o(i.DMA2_Stream0_IRQHandler)
    i.DMA_CalcBaseAndBitshift                0x0800098c   Section        0  stm32f4xx_hal_dma.o(i.DMA_CalcBaseAndBitshift)
    DMA_CalcBaseAndBitshift                  0x0800098d   Thumb Code    34  stm32f4xx_hal_dma.o(i.DMA_CalcBaseAndBitshift)
    i.DMA_CheckFifoParam                     0x080009b4   Section        0  stm32f4xx_hal_dma.o(i.DMA_CheckFifoParam)
    DMA_CheckFifoParam                       0x080009b5   Thumb Code    84  stm32f4xx_hal_dma.o(i.DMA_CheckFifoParam)
    i.DMA_SetConfig                          0x08000a08   Section        0  stm32f4xx_hal_dma.o(i.DMA_SetConfig)
    DMA_SetConfig                            0x08000a09   Thumb Code    40  stm32f4xx_hal_dma.o(i.DMA_SetConfig)
    i.DebugMon_Handler                       0x08000a30   Section        0  stm32f4xx_it.o(i.DebugMon_Handler)
    i.Error_Handler                          0x08000a32   Section        0  main.o(i.Error_Handler)
    i.FFT_DetectFundamentalFrequency         0x08000a38   Section        0  fft_processing.o(i.FFT_DetectFundamentalFrequency)
    i.FFT_DetectHarmonic                     0x08000ae0   Section        0  fft_processing.o(i.FFT_DetectHarmonic)
    i.FFT_InitWindow                         0x08000c2c   Section        0  fft_processing.o(i.FFT_InitWindow)
    i.FFT_Process                            0x08000c84   Section        0  fft_processing.o(i.FFT_Process)
    i.FFT_SmoothFrequency                    0x08000d68   Section        0  fft_processing.o(i.FFT_SmoothFrequency)
    i.HAL_ADC_ConfigChannel                  0x08000dac   Section        0  stm32f4xx_hal_adc.o(i.HAL_ADC_ConfigChannel)
    i.HAL_ADC_ConvCpltCallback               0x08000ef8   Section        0  main.o(i.HAL_ADC_ConvCpltCallback)
    i.HAL_ADC_ConvHalfCpltCallback           0x08000f10   Section        0  stm32f4xx_hal_adc.o(i.HAL_ADC_ConvHalfCpltCallback)
    i.HAL_ADC_ErrorCallback                  0x08000f12   Section        0  stm32f4xx_hal_adc.o(i.HAL_ADC_ErrorCallback)
    i.HAL_ADC_Init                           0x08000f14   Section        0  stm32f4xx_hal_adc.o(i.HAL_ADC_Init)
    i.HAL_ADC_MspInit                        0x08000f68   Section        0  adc.o(i.HAL_ADC_MspInit)
    i.HAL_ADC_Start_DMA                      0x08001000   Section        0  stm32f4xx_hal_adc.o(i.HAL_ADC_Start_DMA)
    i.HAL_DMA_IRQHandler                     0x08001158   Section        0  stm32f4xx_hal_dma.o(i.HAL_DMA_IRQHandler)
    i.HAL_DMA_Init                           0x080012f8   Section        0  stm32f4xx_hal_dma.o(i.HAL_DMA_Init)
    i.HAL_DMA_Start_IT                       0x080013cc   Section        0  stm32f4xx_hal_dma.o(i.HAL_DMA_Start_IT)
    i.HAL_GPIO_Init                          0x0800143c   Section        0  stm32f4xx_hal_gpio.o(i.HAL_GPIO_Init)
    i.HAL_GPIO_WritePin                      0x0800162c   Section        0  stm32f4xx_hal_gpio.o(i.HAL_GPIO_WritePin)
    i.HAL_GetTick                            0x08001638   Section        0  stm32f4xx_hal.o(i.HAL_GetTick)
    i.HAL_IncTick                            0x08001644   Section        0  stm32f4xx_hal.o(i.HAL_IncTick)
    i.HAL_Init                               0x08001654   Section        0  stm32f4xx_hal.o(i.HAL_Init)
    i.HAL_InitTick                           0x08001688   Section        0  stm32f4xx_hal.o(i.HAL_InitTick)
    i.HAL_MspInit                            0x080016c8   Section        0  stm32f4xx_hal_msp.o(i.HAL_MspInit)
    i.HAL_NVIC_EnableIRQ                     0x080016f8   Section        0  stm32f4xx_hal_cortex.o(i.HAL_NVIC_EnableIRQ)
    i.HAL_NVIC_SetPriority                   0x08001714   Section        0  stm32f4xx_hal_cortex.o(i.HAL_NVIC_SetPriority)
    i.HAL_NVIC_SetPriorityGrouping           0x08001754   Section        0  stm32f4xx_hal_cortex.o(i.HAL_NVIC_SetPriorityGrouping)
    i.HAL_RCC_ClockConfig                    0x08001778   Section        0  stm32f4xx_hal_rcc.o(i.HAL_RCC_ClockConfig)
    i.HAL_RCC_GetPCLK1Freq                   0x080018ac   Section        0  stm32f4xx_hal_rcc.o(i.HAL_RCC_GetPCLK1Freq)
    i.HAL_RCC_GetPCLK2Freq                   0x080018cc   Section        0  stm32f4xx_hal_rcc.o(i.HAL_RCC_GetPCLK2Freq)
    i.HAL_RCC_GetSysClockFreq                0x080018ec   Section        0  stm32f4xx_hal_rcc.o(i.HAL_RCC_GetSysClockFreq)
    i.HAL_RCC_OscConfig                      0x0800194c   Section        0  stm32f4xx_hal_rcc.o(i.HAL_RCC_OscConfig)
    i.HAL_SYSTICK_Config                     0x08001cb8   Section        0  stm32f4xx_hal_cortex.o(i.HAL_SYSTICK_Config)
    i.HAL_TIMEx_MasterConfigSynchronization  0x08001ce0   Section        0  stm32f4xx_hal_tim_ex.o(i.HAL_TIMEx_MasterConfigSynchronization)
    i.HAL_TIM_Base_Init                      0x08001d70   Section        0  stm32f4xx_hal_tim.o(i.HAL_TIM_Base_Init)
    i.HAL_TIM_Base_MspInit                   0x08001dcc   Section        0  tim.o(i.HAL_TIM_Base_MspInit)
    i.HAL_TIM_Base_Start                     0x08001e0c   Section        0  stm32f4xx_hal_tim.o(i.HAL_TIM_Base_Start)
    i.HAL_TIM_ConfigClockSource              0x08001e84   Section        0  stm32f4xx_hal_tim.o(i.HAL_TIM_ConfigClockSource)
    i.HAL_UART_Init                          0x08001f60   Section        0  stm32f4xx_hal_uart.o(i.HAL_UART_Init)
    i.HAL_UART_MspInit                       0x08001fc4   Section        0  usart.o(i.HAL_UART_MspInit)
    i.HAL_UART_Transmit                      0x08002030   Section        0  stm32f4xx_hal_uart.o(i.HAL_UART_Transmit)
    i.HardFault_Handler                      0x080020d0   Section        0  stm32f4xx_it.o(i.HardFault_Handler)
    i.IO_Update                              0x080020d4   Section        0  ad9959_new.o(i.IO_Update)
    i.Init_AD9959                            0x08002110   Section        0  ad9959_new.o(i.Init_AD9959)
    i.IntReset                               0x080022e4   Section        0  ad9959_new.o(i.IntReset)
    i.Intserve                               0x08002320   Section        0  ad9959_new.o(i.Intserve)
    i.MX_ADC1_Init                           0x080023c0   Section        0  adc.o(i.MX_ADC1_Init)
    i.MX_DMA_Init                            0x08002420   Section        0  dma.o(i.MX_DMA_Init)
    i.MX_GPIO_Init                           0x0800244c   Section        0  gpio.o(i.MX_GPIO_Init)
    i.MX_TIM3_Init                           0x08002478   Section        0  tim.o(i.MX_TIM3_Init)
    i.MX_TIM4_Init                           0x080024e0   Section        0  tim.o(i.MX_TIM4_Init)
    i.MX_USART1_UART_Init                    0x08002544   Section        0  usart.o(i.MX_USART1_UART_Init)
    i.MemManage_Handler                      0x0800257c   Section        0  stm32f4xx_it.o(i.MemManage_Handler)
    i.NMI_Handler                            0x0800257e   Section        0  stm32f4xx_it.o(i.NMI_Handler)
    i.PendSV_Handler                         0x08002580   Section        0  stm32f4xx_it.o(i.PendSV_Handler)
    i.SVC_Handler                            0x08002582   Section        0  stm32f4xx_it.o(i.SVC_Handler)
    i.SysTick_Handler                        0x08002584   Section        0  stm32f4xx_it.o(i.SysTick_Handler)
    i.SystemClock_Config                     0x08002588   Section        0  main.o(i.SystemClock_Config)
    i.SystemInit                             0x0800261c   Section        0  system_stm32f4xx.o(i.SystemInit)
    i.TIM_Base_SetConfig                     0x0800262c   Section        0  stm32f4xx_hal_tim.o(i.TIM_Base_SetConfig)
    i.TIM_ETR_SetConfig                      0x080026fc   Section        0  stm32f4xx_hal_tim.o(i.TIM_ETR_SetConfig)
    i.TIM_ITRx_SetConfig                     0x08002710   Section        0  stm32f4xx_hal_tim.o(i.TIM_ITRx_SetConfig)
    TIM_ITRx_SetConfig                       0x08002711   Thumb Code    16  stm32f4xx_hal_tim.o(i.TIM_ITRx_SetConfig)
    i.TIM_TI1_ConfigInputStage               0x08002720   Section        0  stm32f4xx_hal_tim.o(i.TIM_TI1_ConfigInputStage)
    TIM_TI1_ConfigInputStage                 0x08002721   Thumb Code    34  stm32f4xx_hal_tim.o(i.TIM_TI1_ConfigInputStage)
    i.TIM_TI2_ConfigInputStage               0x08002742   Section        0  stm32f4xx_hal_tim.o(i.TIM_TI2_ConfigInputStage)
    TIM_TI2_ConfigInputStage                 0x08002743   Thumb Code    36  stm32f4xx_hal_tim.o(i.TIM_TI2_ConfigInputStage)
    i.UART_EndRxTransfer                     0x08002766   Section        0  stm32f4xx_hal_uart.o(i.UART_EndRxTransfer)
    UART_EndRxTransfer                       0x08002767   Thumb Code    78  stm32f4xx_hal_uart.o(i.UART_EndRxTransfer)
    i.UART_SetConfig                         0x080027b4   Section        0  stm32f4xx_hal_uart.o(i.UART_SetConfig)
    UART_SetConfig                           0x080027b5   Thumb Code   258  stm32f4xx_hal_uart.o(i.UART_SetConfig)
    i.UART_WaitOnFlagUntilTimeout            0x080028c0   Section        0  stm32f4xx_hal_uart.o(i.UART_WaitOnFlagUntilTimeout)
    UART_WaitOnFlagUntilTimeout              0x080028c1   Thumb Code   114  stm32f4xx_hal_uart.o(i.UART_WaitOnFlagUntilTimeout)
    i.UsageFault_Handler                     0x08002932   Section        0  stm32f4xx_it.o(i.UsageFault_Handler)
    i.WriteAmplitude                         0x08002934   Section        0  ad9959_new.o(i.WriteAmplitude)
    i.WriteData_AD9959                       0x0800299c   Section        0  ad9959_new.o(i.WriteData_AD9959)
    i.WriteFreq                              0x08002a78   Section        0  ad9959_new.o(i.WriteFreq)
    i.__0printf                              0x08002b0c   Section        0  printfa.o(i.__0printf)
    i.__ARM_fpclassifyf                      0x08002b2c   Section        0  fpclassifyf.o(i.__ARM_fpclassifyf)
    i.__NVIC_SetPriority                     0x08002b52   Section        0  stm32f4xx_hal_cortex.o(i.__NVIC_SetPriority)
    __NVIC_SetPriority                       0x08002b53   Thumb Code    32  stm32f4xx_hal_cortex.o(i.__NVIC_SetPriority)
    i.__hardfp_cosf                          0x08002b74   Section        0  cosf.o(i.__hardfp_cosf)
    i.__hardfp_expf                          0x08002cc4   Section        0  expf.o(i.__hardfp_expf)
    i.__hardfp_sqrtf                         0x08002eac   Section        0  sqrtf.o(i.__hardfp_sqrtf)
    i.__mathlib_flt_infnan                   0x08002ee6   Section        0  funder.o(i.__mathlib_flt_infnan)
    i.__mathlib_flt_invalid                  0x08002eec   Section        0  funder.o(i.__mathlib_flt_invalid)
    i.__mathlib_flt_overflow                 0x08002efc   Section        0  funder.o(i.__mathlib_flt_overflow)
    i.__mathlib_flt_underflow                0x08002f0c   Section        0  funder.o(i.__mathlib_flt_underflow)
    i.__mathlib_rredf2                       0x08002f1c   Section        0  rredf.o(i.__mathlib_rredf2)
    i.__scatterload_copy                     0x08003070   Section       14  handlers.o(i.__scatterload_copy)
    i.__scatterload_null                     0x0800307e   Section        2  handlers.o(i.__scatterload_null)
    i.__scatterload_zeroinit                 0x08003080   Section       14  handlers.o(i.__scatterload_zeroinit)
    i.__set_errno                            0x08003090   Section        0  errno.o(i.__set_errno)
    i._fp_digits                             0x0800309c   Section        0  printfa.o(i._fp_digits)
    _fp_digits                               0x0800309d   Thumb Code   366  printfa.o(i._fp_digits)
    i._printf_core                           0x08003220   Section        0  printfa.o(i._printf_core)
    _printf_core                             0x08003221   Thumb Code  1744  printfa.o(i._printf_core)
    i._printf_post_padding                   0x080038fc   Section        0  printfa.o(i._printf_post_padding)
    _printf_post_padding                     0x080038fd   Thumb Code    36  printfa.o(i._printf_post_padding)
    i._printf_pre_padding                    0x08003920   Section        0  printfa.o(i._printf_pre_padding)
    _printf_pre_padding                      0x08003921   Thumb Code    46  printfa.o(i._printf_pre_padding)
    i.arm_bitreversal_32                     0x08003950   Section        0  transformfunctions.o(i.arm_bitreversal_32)
    i.arm_cfft_f32                           0x0800398e   Section        0  transformfunctions.o(i.arm_cfft_f32)
    i.arm_cfft_radix8by2_f32                 0x08003a5a   Section        0  transformfunctions.o(i.arm_cfft_radix8by2_f32)
    i.arm_cfft_radix8by4_f32                 0x08003be2   Section        0  transformfunctions.o(i.arm_cfft_radix8by4_f32)
    i.arm_cmplx_mag_f32                      0x08004010   Section        0  complexmathfunctions.o(i.arm_cmplx_mag_f32)
    i.arm_radix8_butterfly_f32               0x08004054   Section        0  transformfunctions.o(i.arm_radix8_butterfly_f32)
    i.calculateThreshold                     0x08004518   Section        0  fft_processing.o(i.calculateThreshold)
    calculateThreshold                       0x08004519   Thumb Code   118  fft_processing.o(i.calculateThreshold)
    i.delay1                                 0x08004594   Section        0  ad9959_new.o(i.delay1)
    i.fputc                                  0x080045a0   Section        0  usart.o(i.fputc)
    i.main                                   0x080045b8   Section        0  main.o(i.main)
    i.parabolicInterpolation                 0x08004720   Section        0  fft_processing.o(i.parabolicInterpolation)
    parabolicInterpolation                   0x08004721   Thumb Code   100  fft_processing.o(i.parabolicInterpolation)
    .constdata                               0x08004788   Section        8  stm32f4xx_hal_dma.o(.constdata)
    flagBitshiftOffset                       0x08004788   Data           8  stm32f4xx_hal_dma.o(.constdata)
    .constdata                               0x08004790   Section       16  system_stm32f4xx.o(.constdata)
    .constdata                               0x080047a0   Section        8  system_stm32f4xx.o(.constdata)
    .constdata                               0x080047a8   Section     8192  commontables.o(.constdata)
    .constdata                               0x080067a8   Section     3600  commontables.o(.constdata)
    .constdata                               0x080075b8   Section       16  commontables.o(.constdata)
    .constdata                               0x080075c8   Section       48  expf.o(.constdata)
    twotokover4top                           0x080075c8   Data          16  expf.o(.constdata)
    twotokover4bot                           0x080075d8   Data          16  expf.o(.constdata)
    twotokover4all                           0x080075e8   Data          16  expf.o(.constdata)
    .constdata                               0x080075f8   Section       32  rredf.o(.constdata)
    twooverpi                                0x080075f8   Data          32  rredf.o(.constdata)
    .data                                    0x20000000   Section        4  main.o(.data)
    .data                                    0x20000004   Section       12  stm32f4xx_hal.o(.data)
    .data                                    0x20000010   Section        4  system_stm32f4xx.o(.data)
    .data                                    0x20000014   Section       11  ad9959_new.o(.data)
    .data                                    0x20000020   Section        4  stdout.o(.data)
    .data                                    0x20000024   Section        4  errno.o(.data)
    _errno                                   0x20000024   Data           4  errno.o(.data)
    .bss                                     0x20000028   Section    18464  main.o(.bss)
    .bss                                     0x20004848   Section      168  adc.o(.bss)
    .bss                                     0x200048f0   Section      144  tim.o(.bss)
    .bss                                     0x20004980   Section       72  usart.o(.bss)
    .bss                                     0x200049c8   Section       32  ad9959_new.o(.bss)
    STACK                                    0x200049e8   Section     1024  startup_stm32f407xx.o(STACK)

    Global Symbols

    Symbol Name                              Value     Ov Type        Size  Object(Section)

    BuildAttributes$$THM_ISAv4$E$P$D$K$B$S$7EM$VFPi3$EXTD16$VFPS$VFMA$PE$A:L22UL41UL21$X:L11$S22US41US21$IEEE1$IW$USESV6$~STKCKD$USESV7$~SHL$OTIME$ROPI$IEEEX$EBA8$MICROLIB$REQ8$PRES8$EABIv2 0x00000000   Number         0  anon$$obj.o ABSOLUTE
    __ARM_use_no_argv                        0x00000000   Number         0  main.o ABSOLUTE
    _printf_a                                0x00000000   Number         0  stubs.o ABSOLUTE
    _printf_c                                0x00000000   Number         0  stubs.o ABSOLUTE
    _printf_charcount                        0x00000000   Number         0  stubs.o ABSOLUTE
    _printf_d                                0x00000000   Number         0  stubs.o ABSOLUTE
    _printf_e                                0x00000000   Number         0  stubs.o ABSOLUTE
    _printf_f                                0x00000000   Number         0  stubs.o ABSOLUTE
    _printf_flags                            0x00000000   Number         0  stubs.o ABSOLUTE
    _printf_fp_dec                           0x00000000   Number         0  stubs.o ABSOLUTE
    _printf_fp_hex                           0x00000000   Number         0  stubs.o ABSOLUTE
    _printf_g                                0x00000000   Number         0  stubs.o ABSOLUTE
    _printf_i                                0x00000000   Number         0  stubs.o ABSOLUTE
    _printf_int_dec                          0x00000000   Number         0  stubs.o ABSOLUTE
    _printf_l                                0x00000000   Number         0  stubs.o ABSOLUTE
    _printf_lc                               0x00000000   Number         0  stubs.o ABSOLUTE
    _printf_ll                               0x00000000   Number         0  stubs.o ABSOLUTE
    _printf_lld                              0x00000000   Number         0  stubs.o ABSOLUTE
    _printf_lli                              0x00000000   Number         0  stubs.o ABSOLUTE
    _printf_llo                              0x00000000   Number         0  stubs.o ABSOLUTE
    _printf_llu                              0x00000000   Number         0  stubs.o ABSOLUTE
    _printf_llx                              0x00000000   Number         0  stubs.o ABSOLUTE
    _printf_longlong_dec                     0x00000000   Number         0  stubs.o ABSOLUTE
    _printf_longlong_hex                     0x00000000   Number         0  stubs.o ABSOLUTE
    _printf_longlong_oct                     0x00000000   Number         0  stubs.o ABSOLUTE
    _printf_ls                               0x00000000   Number         0  stubs.o ABSOLUTE
    _printf_mbtowc                           0x00000000   Number         0  stubs.o ABSOLUTE
    _printf_n                                0x00000000   Number         0  stubs.o ABSOLUTE
    _printf_o                                0x00000000   Number         0  stubs.o ABSOLUTE
    _printf_p                                0x00000000   Number         0  stubs.o ABSOLUTE
    _printf_percent                          0x00000000   Number         0  stubs.o ABSOLUTE
    _printf_pre_padding                      0x00000000   Number         0  stubs.o ABSOLUTE
    _printf_return_value                     0x00000000   Number         0  stubs.o ABSOLUTE
    _printf_s                                0x00000000   Number         0  stubs.o ABSOLUTE
    _printf_sizespec                         0x00000000   Number         0  stubs.o ABSOLUTE
    _printf_str                              0x00000000   Number         0  stubs.o ABSOLUTE
    _printf_truncate_signed                  0x00000000   Number         0  stubs.o ABSOLUTE
    _printf_truncate_unsigned                0x00000000   Number         0  stubs.o ABSOLUTE
    _printf_u                                0x00000000   Number         0  stubs.o ABSOLUTE
    _printf_wc                               0x00000000   Number         0  stubs.o ABSOLUTE
    _printf_wctomb                           0x00000000   Number         0  stubs.o ABSOLUTE
    _printf_widthprec                        0x00000000   Number         0  stubs.o ABSOLUTE
    _printf_x                                0x00000000   Number         0  stubs.o ABSOLUTE
    __cpp_initialize__aeabi_                  - Undefined Weak Reference
    __cxa_finalize                            - Undefined Weak Reference
    __decompress                              - Undefined Weak Reference
    _clock_init                               - Undefined Weak Reference
    _microlib_exit                            - Undefined Weak Reference
    __Vectors_Size                           0x00000188   Number         0  startup_stm32f407xx.o ABSOLUTE
    __Vectors                                0x08000000   Data           4  startup_stm32f407xx.o(RESET)
    __Vectors_End                            0x08000188   Data           0  startup_stm32f407xx.o(RESET)
    __main                                   0x08000189   Thumb Code     0  entry.o(.ARM.Collect$$$$00000000)
    _main_stk                                0x08000189   Thumb Code     0  entry2.o(.ARM.Collect$$$$00000001)
    _main_scatterload                        0x0800018d   Thumb Code     0  entry5.o(.ARM.Collect$$$$00000004)
    __main_after_scatterload                 0x08000191   Thumb Code     0  entry5.o(.ARM.Collect$$$$00000004)
    _main_clock                              0x08000191   Thumb Code     0  entry7b.o(.ARM.Collect$$$$00000008)
    _main_cpp_init                           0x08000191   Thumb Code     0  entry8b.o(.ARM.Collect$$$$0000000A)
    _main_init                               0x08000191   Thumb Code     0  entry9a.o(.ARM.Collect$$$$0000000B)
    __rt_final_cpp                           0x08000199   Thumb Code     0  entry10a.o(.ARM.Collect$$$$0000000D)
    __rt_final_exit                          0x08000199   Thumb Code     0  entry11a.o(.ARM.Collect$$$$0000000F)
    Reset_Handler                            0x0800019d   Thumb Code     8  startup_stm32f407xx.o(.text)
    ADC_IRQHandler                           0x080001b7   Thumb Code     0  startup_stm32f407xx.o(.text)
    CAN1_RX0_IRQHandler                      0x080001b7   Thumb Code     0  startup_stm32f407xx.o(.text)
    CAN1_RX1_IRQHandler                      0x080001b7   Thumb Code     0  startup_stm32f407xx.o(.text)
    CAN1_SCE_IRQHandler                      0x080001b7   Thumb Code     0  startup_stm32f407xx.o(.text)
    CAN1_TX_IRQHandler                       0x080001b7   Thumb Code     0  startup_stm32f407xx.o(.text)
    CAN2_RX0_IRQHandler                      0x080001b7   Thumb Code     0  startup_stm32f407xx.o(.text)
    CAN2_RX1_IRQHandler                      0x080001b7   Thumb Code     0  startup_stm32f407xx.o(.text)
    CAN2_SCE_IRQHandler                      0x080001b7   Thumb Code     0  startup_stm32f407xx.o(.text)
    CAN2_TX_IRQHandler                       0x080001b7   Thumb Code     0  startup_stm32f407xx.o(.text)
    DCMI_IRQHandler                          0x080001b7   Thumb Code     0  startup_stm32f407xx.o(.text)
    DMA1_Stream0_IRQHandler                  0x080001b7   Thumb Code     0  startup_stm32f407xx.o(.text)
    DMA1_Stream1_IRQHandler                  0x080001b7   Thumb Code     0  startup_stm32f407xx.o(.text)
    DMA1_Stream2_IRQHandler                  0x080001b7   Thumb Code     0  startup_stm32f407xx.o(.text)
    DMA1_Stream3_IRQHandler                  0x080001b7   Thumb Code     0  startup_stm32f407xx.o(.text)
    DMA1_Stream4_IRQHandler                  0x080001b7   Thumb Code     0  startup_stm32f407xx.o(.text)
    DMA1_Stream5_IRQHandler                  0x080001b7   Thumb Code     0  startup_stm32f407xx.o(.text)
    DMA1_Stream6_IRQHandler                  0x080001b7   Thumb Code     0  startup_stm32f407xx.o(.text)
    DMA1_Stream7_IRQHandler                  0x080001b7   Thumb Code     0  startup_stm32f407xx.o(.text)
    DMA2_Stream1_IRQHandler                  0x080001b7   Thumb Code     0  startup_stm32f407xx.o(.text)
    DMA2_Stream2_IRQHandler                  0x080001b7   Thumb Code     0  startup_stm32f407xx.o(.text)
    DMA2_Stream3_IRQHandler                  0x080001b7   Thumb Code     0  startup_stm32f407xx.o(.text)
    DMA2_Stream4_IRQHandler                  0x080001b7   Thumb Code     0  startup_stm32f407xx.o(.text)
    DMA2_Stream5_IRQHandler                  0x080001b7   Thumb Code     0  startup_stm32f407xx.o(.text)
    DMA2_Stream6_IRQHandler                  0x080001b7   Thumb Code     0  startup_stm32f407xx.o(.text)
    DMA2_Stream7_IRQHandler                  0x080001b7   Thumb Code     0  startup_stm32f407xx.o(.text)
    ETH_IRQHandler                           0x080001b7   Thumb Code     0  startup_stm32f407xx.o(.text)
    ETH_WKUP_IRQHandler                      0x080001b7   Thumb Code     0  startup_stm32f407xx.o(.text)
    EXTI0_IRQHandler                         0x080001b7   Thumb Code     0  startup_stm32f407xx.o(.text)
    EXTI15_10_IRQHandler                     0x080001b7   Thumb Code     0  startup_stm32f407xx.o(.text)
    EXTI1_IRQHandler                         0x080001b7   Thumb Code     0  startup_stm32f407xx.o(.text)
    EXTI2_IRQHandler                         0x080001b7   Thumb Code     0  startup_stm32f407xx.o(.text)
    EXTI3_IRQHandler                         0x080001b7   Thumb Code     0  startup_stm32f407xx.o(.text)
    EXTI4_IRQHandler                         0x080001b7   Thumb Code     0  startup_stm32f407xx.o(.text)
    EXTI9_5_IRQHandler                       0x080001b7   Thumb Code     0  startup_stm32f407xx.o(.text)
    FLASH_IRQHandler                         0x080001b7   Thumb Code     0  startup_stm32f407xx.o(.text)
    FMC_IRQHandler                           0x080001b7   Thumb Code     0  startup_stm32f407xx.o(.text)
    FPU_IRQHandler                           0x080001b7   Thumb Code     0  startup_stm32f407xx.o(.text)
    HASH_RNG_IRQHandler                      0x080001b7   Thumb Code     0  startup_stm32f407xx.o(.text)
    I2C1_ER_IRQHandler                       0x080001b7   Thumb Code     0  startup_stm32f407xx.o(.text)
    I2C1_EV_IRQHandler                       0x080001b7   Thumb Code     0  startup_stm32f407xx.o(.text)
    I2C2_ER_IRQHandler                       0x080001b7   Thumb Code     0  startup_stm32f407xx.o(.text)
    I2C2_EV_IRQHandler                       0x080001b7   Thumb Code     0  startup_stm32f407xx.o(.text)
    I2C3_ER_IRQHandler                       0x080001b7   Thumb Code     0  startup_stm32f407xx.o(.text)
    I2C3_EV_IRQHandler                       0x080001b7   Thumb Code     0  startup_stm32f407xx.o(.text)
    OTG_FS_IRQHandler                        0x080001b7   Thumb Code     0  startup_stm32f407xx.o(.text)
    OTG_FS_WKUP_IRQHandler                   0x080001b7   Thumb Code     0  startup_stm32f407xx.o(.text)
    OTG_HS_EP1_IN_IRQHandler                 0x080001b7   Thumb Code     0  startup_stm32f407xx.o(.text)
    OTG_HS_EP1_OUT_IRQHandler                0x080001b7   Thumb Code     0  startup_stm32f407xx.o(.text)
    OTG_HS_IRQHandler                        0x080001b7   Thumb Code     0  startup_stm32f407xx.o(.text)
    OTG_HS_WKUP_IRQHandler                   0x080001b7   Thumb Code     0  startup_stm32f407xx.o(.text)
    PVD_IRQHandler                           0x080001b7   Thumb Code     0  startup_stm32f407xx.o(.text)
    RCC_IRQHandler                           0x080001b7   Thumb Code     0  startup_stm32f407xx.o(.text)
    RTC_Alarm_IRQHandler                     0x080001b7   Thumb Code     0  startup_stm32f407xx.o(.text)
    RTC_WKUP_IRQHandler                      0x080001b7   Thumb Code     0  startup_stm32f407xx.o(.text)
    SDIO_IRQHandler                          0x080001b7   Thumb Code     0  startup_stm32f407xx.o(.text)
    SPI1_IRQHandler                          0x080001b7   Thumb Code     0  startup_stm32f407xx.o(.text)
    SPI2_IRQHandler                          0x080001b7   Thumb Code     0  startup_stm32f407xx.o(.text)
    SPI3_IRQHandler                          0x080001b7   Thumb Code     0  startup_stm32f407xx.o(.text)
    TAMP_STAMP_IRQHandler                    0x080001b7   Thumb Code     0  startup_stm32f407xx.o(.text)
    TIM1_BRK_TIM9_IRQHandler                 0x080001b7   Thumb Code     0  startup_stm32f407xx.o(.text)
    TIM1_CC_IRQHandler                       0x080001b7   Thumb Code     0  startup_stm32f407xx.o(.text)
    TIM1_TRG_COM_TIM11_IRQHandler            0x080001b7   Thumb Code     0  startup_stm32f407xx.o(.text)
    TIM1_UP_TIM10_IRQHandler                 0x080001b7   Thumb Code     0  startup_stm32f407xx.o(.text)
    TIM2_IRQHandler                          0x080001b7   Thumb Code     0  startup_stm32f407xx.o(.text)
    TIM3_IRQHandler                          0x080001b7   Thumb Code     0  startup_stm32f407xx.o(.text)
    TIM4_IRQHandler                          0x080001b7   Thumb Code     0  startup_stm32f407xx.o(.text)
    TIM5_IRQHandler                          0x080001b7   Thumb Code     0  startup_stm32f407xx.o(.text)
    TIM6_DAC_IRQHandler                      0x080001b7   Thumb Code     0  startup_stm32f407xx.o(.text)
    TIM7_IRQHandler                          0x080001b7   Thumb Code     0  startup_stm32f407xx.o(.text)
    TIM8_BRK_TIM12_IRQHandler                0x080001b7   Thumb Code     0  startup_stm32f407xx.o(.text)
    TIM8_CC_IRQHandler                       0x080001b7   Thumb Code     0  startup_stm32f407xx.o(.text)
    TIM8_TRG_COM_TIM14_IRQHandler            0x080001b7   Thumb Code     0  startup_stm32f407xx.o(.text)
    TIM8_UP_TIM13_IRQHandler                 0x080001b7   Thumb Code     0  startup_stm32f407xx.o(.text)
    UART4_IRQHandler                         0x080001b7   Thumb Code     0  startup_stm32f407xx.o(.text)
    UART5_IRQHandler                         0x080001b7   Thumb Code     0  startup_stm32f407xx.o(.text)
    USART1_IRQHandler                        0x080001b7   Thumb Code     0  startup_stm32f407xx.o(.text)
    USART2_IRQHandler                        0x080001b7   Thumb Code     0  startup_stm32f407xx.o(.text)
    USART3_IRQHandler                        0x080001b7   Thumb Code     0  startup_stm32f407xx.o(.text)
    USART6_IRQHandler                        0x080001b7   Thumb Code     0  startup_stm32f407xx.o(.text)
    WWDG_IRQHandler                          0x080001b7   Thumb Code     0  startup_stm32f407xx.o(.text)
    __aeabi_uldivmod                         0x080001c1   Thumb Code    98  uldiv.o(.text)
    __aeabi_llsl                             0x08000223   Thumb Code    30  llshl.o(.text)
    _ll_shift_l                              0x08000223   Thumb Code     0  llshl.o(.text)
    __aeabi_lasr                             0x08000241   Thumb Code    36  llsshr.o(.text)
    _ll_sshift_r                             0x08000241   Thumb Code     0  llsshr.o(.text)
    __aeabi_memset                           0x08000265   Thumb Code    14  memseta.o(.text)
    __aeabi_memset4                          0x08000265   Thumb Code     0  memseta.o(.text)
    __aeabi_memset8                          0x08000265   Thumb Code     0  memseta.o(.text)
    __aeabi_memclr                           0x08000273   Thumb Code     4  memseta.o(.text)
    __aeabi_memclr4                          0x08000273   Thumb Code     0  memseta.o(.text)
    __aeabi_memclr8                          0x08000273   Thumb Code     0  memseta.o(.text)
    _memset$wrapper                          0x08000277   Thumb Code    18  memseta.o(.text)
    __aeabi_dadd                             0x08000289   Thumb Code   322  dadd.o(.text)
    __aeabi_dsub                             0x080003cb   Thumb Code     6  dadd.o(.text)
    __aeabi_drsub                            0x080003d1   Thumb Code     6  dadd.o(.text)
    __aeabi_dmul                             0x080003d7   Thumb Code   228  dmul.o(.text)
    __aeabi_ddiv                             0x080004bb   Thumb Code   222  ddiv.o(.text)
    __aeabi_ui2d                             0x08000599   Thumb Code    26  dfltui.o(.text)
    __aeabi_d2uiz                            0x080005b3   Thumb Code    50  dfixui.o(.text)
    __aeabi_f2d                              0x080005e5   Thumb Code    38  f2d.o(.text)
    __aeabi_cdrcmple                         0x0800060d   Thumb Code    48  cdrcmple.o(.text)
    __aeabi_uidiv                            0x0800063d   Thumb Code     0  uidiv.o(.text)
    __aeabi_uidivmod                         0x0800063d   Thumb Code    44  uidiv.o(.text)
    __aeabi_llsr                             0x08000669   Thumb Code    32  llushr.o(.text)
    _ll_ushift_r                             0x08000669   Thumb Code     0  llushr.o(.text)
    __I$use$fp                               0x08000689   Thumb Code     0  iusefp.o(.text)
    _double_round                            0x08000689   Thumb Code    30  depilogue.o(.text)
    _double_epilogue                         0x080006a7   Thumb Code   156  depilogue.o(.text)
    __aeabi_d2ulz                            0x08000743   Thumb Code    48  dfixul.o(.text)
    __scatterload                            0x08000775   Thumb Code    28  init.o(.text)
    __scatterload_rt2                        0x08000775   Thumb Code     0  init.o(.text)
    AD9959MSGInit                            0x08000799   Thumb Code    36  ad9959_new.o(i.AD9959MSGInit)
    BusFault_Handler                         0x0800097d   Thumb Code     2  stm32f4xx_it.o(i.BusFault_Handler)
    DMA2_Stream0_IRQHandler                  0x08000981   Thumb Code     6  stm32f4xx_it.o(i.DMA2_Stream0_IRQHandler)
    DebugMon_Handler                         0x08000a31   Thumb Code     2  stm32f4xx_it.o(i.DebugMon_Handler)
    Error_Handler                            0x08000a33   Thumb Code     4  main.o(i.Error_Handler)
    FFT_DetectFundamentalFrequency           0x08000a39   Thumb Code   156  fft_processing.o(i.FFT_DetectFundamentalFrequency)
    FFT_DetectHarmonic                       0x08000ae1   Thumb Code   314  fft_processing.o(i.FFT_DetectHarmonic)
    FFT_InitWindow                           0x08000c2d   Thumb Code    78  fft_processing.o(i.FFT_InitWindow)
    FFT_Process                              0x08000c85   Thumb Code   202  fft_processing.o(i.FFT_Process)
    FFT_SmoothFrequency                      0x08000d69   Thumb Code    62  fft_processing.o(i.FFT_SmoothFrequency)
    HAL_ADC_ConfigChannel                    0x08000dad   Thumb Code   316  stm32f4xx_hal_adc.o(i.HAL_ADC_ConfigChannel)
    HAL_ADC_ConvCpltCallback                 0x08000ef9   Thumb Code    16  main.o(i.HAL_ADC_ConvCpltCallback)
    HAL_ADC_ConvHalfCpltCallback             0x08000f11   Thumb Code     2  stm32f4xx_hal_adc.o(i.HAL_ADC_ConvHalfCpltCallback)
    HAL_ADC_ErrorCallback                    0x08000f13   Thumb Code     2  stm32f4xx_hal_adc.o(i.HAL_ADC_ErrorCallback)
    HAL_ADC_Init                             0x08000f15   Thumb Code    84  stm32f4xx_hal_adc.o(i.HAL_ADC_Init)
    HAL_ADC_MspInit                          0x08000f69   Thumb Code   132  adc.o(i.HAL_ADC_MspInit)
    HAL_ADC_Start_DMA                        0x08001001   Thumb Code   306  stm32f4xx_hal_adc.o(i.HAL_ADC_Start_DMA)
    HAL_DMA_IRQHandler                       0x08001159   Thumb Code   412  stm32f4xx_hal_dma.o(i.HAL_DMA_IRQHandler)
    HAL_DMA_Init                             0x080012f9   Thumb Code   206  stm32f4xx_hal_dma.o(i.HAL_DMA_Init)
    HAL_DMA_Start_IT                         0x080013cd   Thumb Code   110  stm32f4xx_hal_dma.o(i.HAL_DMA_Start_IT)
    HAL_GPIO_Init                            0x0800143d   Thumb Code   450  stm32f4xx_hal_gpio.o(i.HAL_GPIO_Init)
    HAL_GPIO_WritePin                        0x0800162d   Thumb Code    10  stm32f4xx_hal_gpio.o(i.HAL_GPIO_WritePin)
    HAL_GetTick                              0x08001639   Thumb Code     6  stm32f4xx_hal.o(i.HAL_GetTick)
    HAL_IncTick                              0x08001645   Thumb Code    12  stm32f4xx_hal.o(i.HAL_IncTick)
    HAL_Init                                 0x08001655   Thumb Code    48  stm32f4xx_hal.o(i.HAL_Init)
    HAL_InitTick                             0x08001689   Thumb Code    54  stm32f4xx_hal.o(i.HAL_InitTick)
    HAL_MspInit                              0x080016c9   Thumb Code    42  stm32f4xx_hal_msp.o(i.HAL_MspInit)
    HAL_NVIC_EnableIRQ                       0x080016f9   Thumb Code    26  stm32f4xx_hal_cortex.o(i.HAL_NVIC_EnableIRQ)
    HAL_NVIC_SetPriority                     0x08001715   Thumb Code    60  stm32f4xx_hal_cortex.o(i.HAL_NVIC_SetPriority)
    HAL_NVIC_SetPriorityGrouping             0x08001755   Thumb Code    26  stm32f4xx_hal_cortex.o(i.HAL_NVIC_SetPriorityGrouping)
    HAL_RCC_ClockConfig                      0x08001779   Thumb Code   288  stm32f4xx_hal_rcc.o(i.HAL_RCC_ClockConfig)
    HAL_RCC_GetPCLK1Freq                     0x080018ad   Thumb Code    20  stm32f4xx_hal_rcc.o(i.HAL_RCC_GetPCLK1Freq)
    HAL_RCC_GetPCLK2Freq                     0x080018cd   Thumb Code    20  stm32f4xx_hal_rcc.o(i.HAL_RCC_GetPCLK2Freq)
    HAL_RCC_GetSysClockFreq                  0x080018ed   Thumb Code    88  stm32f4xx_hal_rcc.o(i.HAL_RCC_GetSysClockFreq)
    HAL_RCC_OscConfig                        0x0800194d   Thumb Code   856  stm32f4xx_hal_rcc.o(i.HAL_RCC_OscConfig)
    HAL_SYSTICK_Config                       0x08001cb9   Thumb Code    40  stm32f4xx_hal_cortex.o(i.HAL_SYSTICK_Config)
    HAL_TIMEx_MasterConfigSynchronization    0x08001ce1   Thumb Code   116  stm32f4xx_hal_tim_ex.o(i.HAL_TIMEx_MasterConfigSynchronization)
    HAL_TIM_Base_Init                        0x08001d71   Thumb Code    90  stm32f4xx_hal_tim.o(i.HAL_TIM_Base_Init)
    HAL_TIM_Base_MspInit                     0x08001dcd   Thumb Code    52  tim.o(i.HAL_TIM_Base_MspInit)
    HAL_TIM_Base_Start                       0x08001e0d   Thumb Code    90  stm32f4xx_hal_tim.o(i.HAL_TIM_Base_Start)
    HAL_TIM_ConfigClockSource                0x08001e85   Thumb Code   220  stm32f4xx_hal_tim.o(i.HAL_TIM_ConfigClockSource)
    HAL_UART_Init                            0x08001f61   Thumb Code   100  stm32f4xx_hal_uart.o(i.HAL_UART_Init)
    HAL_UART_MspInit                         0x08001fc5   Thumb Code    94  usart.o(i.HAL_UART_MspInit)
    HAL_UART_Transmit                        0x08002031   Thumb Code   160  stm32f4xx_hal_uart.o(i.HAL_UART_Transmit)
    HardFault_Handler                        0x080020d1   Thumb Code     2  stm32f4xx_it.o(i.HardFault_Handler)
    IO_Update                                0x080020d5   Thumb Code    54  ad9959_new.o(i.IO_Update)
    Init_AD9959                              0x08002111   Thumb Code   438  ad9959_new.o(i.Init_AD9959)
    IntReset                                 0x080022e5   Thumb Code    54  ad9959_new.o(i.IntReset)
    Intserve                                 0x08002321   Thumb Code   142  ad9959_new.o(i.Intserve)
    MX_ADC1_Init                             0x080023c1   Thumb Code    88  adc.o(i.MX_ADC1_Init)
    MX_DMA_Init                              0x08002421   Thumb Code    40  dma.o(i.MX_DMA_Init)
    MX_GPIO_Init                             0x0800244d   Thumb Code    38  gpio.o(i.MX_GPIO_Init)
    MX_TIM3_Init                             0x08002479   Thumb Code    94  tim.o(i.MX_TIM3_Init)
    MX_TIM4_Init                             0x080024e1   Thumb Code    92  tim.o(i.MX_TIM4_Init)
    MX_USART1_UART_Init                      0x08002545   Thumb Code    48  usart.o(i.MX_USART1_UART_Init)
    MemManage_Handler                        0x0800257d   Thumb Code     2  stm32f4xx_it.o(i.MemManage_Handler)
    NMI_Handler                              0x0800257f   Thumb Code     2  stm32f4xx_it.o(i.NMI_Handler)
    PendSV_Handler                           0x08002581   Thumb Code     2  stm32f4xx_it.o(i.PendSV_Handler)
    SVC_Handler                              0x08002583   Thumb Code     2  stm32f4xx_it.o(i.SVC_Handler)
    SysTick_Handler                          0x08002585   Thumb Code     4  stm32f4xx_it.o(i.SysTick_Handler)
    SystemClock_Config                       0x08002589   Thumb Code   138  main.o(i.SystemClock_Config)
    SystemInit                               0x0800261d   Thumb Code    12  system_stm32f4xx.o(i.SystemInit)
    TIM_Base_SetConfig                       0x0800262d   Thumb Code   164  stm32f4xx_hal_tim.o(i.TIM_Base_SetConfig)
    TIM_ETR_SetConfig                        0x080026fd   Thumb Code    20  stm32f4xx_hal_tim.o(i.TIM_ETR_SetConfig)
    UsageFault_Handler                       0x08002933   Thumb Code     2  stm32f4xx_it.o(i.UsageFault_Handler)
    WriteAmplitude                           0x08002935   Thumb Code    96  ad9959_new.o(i.WriteAmplitude)
    WriteData_AD9959                         0x0800299d   Thumb Code   210  ad9959_new.o(i.WriteData_AD9959)
    WriteFreq                                0x08002a79   Thumb Code   130  ad9959_new.o(i.WriteFreq)
    __0printf                                0x08002b0d   Thumb Code    22  printfa.o(i.__0printf)
    __1printf                                0x08002b0d   Thumb Code     0  printfa.o(i.__0printf)
    __2printf                                0x08002b0d   Thumb Code     0  printfa.o(i.__0printf)
    __c89printf                              0x08002b0d   Thumb Code     0  printfa.o(i.__0printf)
    printf                                   0x08002b0d   Thumb Code     0  printfa.o(i.__0printf)
    __ARM_fpclassifyf                        0x08002b2d   Thumb Code    38  fpclassifyf.o(i.__ARM_fpclassifyf)
    __hardfp_cosf                            0x08002b75   Thumb Code   280  cosf.o(i.__hardfp_cosf)
    __hardfp_expf                            0x08002cc5   Thumb Code   426  expf.o(i.__hardfp_expf)
    __mathlib_expf                           0x08002cc5   Thumb Code     0  expf.o(i.__hardfp_expf)
    __hardfp_sqrtf                           0x08002ead   Thumb Code    58  sqrtf.o(i.__hardfp_sqrtf)
    __mathlib_flt_infnan                     0x08002ee7   Thumb Code     6  funder.o(i.__mathlib_flt_infnan)
    __mathlib_flt_invalid                    0x08002eed   Thumb Code    10  funder.o(i.__mathlib_flt_invalid)
    __mathlib_flt_overflow                   0x08002efd   Thumb Code    10  funder.o(i.__mathlib_flt_overflow)
    __mathlib_flt_underflow                  0x08002f0d   Thumb Code    10  funder.o(i.__mathlib_flt_underflow)
    __mathlib_rredf2                         0x08002f1d   Thumb Code   316  rredf.o(i.__mathlib_rredf2)
    __scatterload_copy                       0x08003071   Thumb Code    14  handlers.o(i.__scatterload_copy)
    __scatterload_null                       0x0800307f   Thumb Code     2  handlers.o(i.__scatterload_null)
    __scatterload_zeroinit                   0x08003081   Thumb Code    14  handlers.o(i.__scatterload_zeroinit)
    __set_errno                              0x08003091   Thumb Code     6  errno.o(i.__set_errno)
    arm_bitreversal_32                       0x08003951   Thumb Code    62  transformfunctions.o(i.arm_bitreversal_32)
    arm_cfft_f32                             0x0800398f   Thumb Code   204  transformfunctions.o(i.arm_cfft_f32)
    arm_cfft_radix8by2_f32                   0x08003a5b   Thumb Code   392  transformfunctions.o(i.arm_cfft_radix8by2_f32)
    arm_cfft_radix8by4_f32                   0x08003be3   Thumb Code  1068  transformfunctions.o(i.arm_cfft_radix8by4_f32)
    arm_cmplx_mag_f32                        0x08004011   Thumb Code    62  complexmathfunctions.o(i.arm_cmplx_mag_f32)
    arm_radix8_butterfly_f32                 0x08004055   Thumb Code  1220  transformfunctions.o(i.arm_radix8_butterfly_f32)
    delay1                                   0x08004595   Thumb Code    12  ad9959_new.o(i.delay1)
    fputc                                    0x080045a1   Thumb Code    20  usart.o(i.fputc)
    main                                     0x080045b9   Thumb Code   298  main.o(i.main)
    AHBPrescTable                            0x08004790   Data          16  system_stm32f4xx.o(.constdata)
    APBPrescTable                            0x080047a0   Data           8  system_stm32f4xx.o(.constdata)
    twiddleCoef_1024                         0x080047a8   Data        8192  commontables.o(.constdata)
    armBitRevIndexTable1024                  0x080067a8   Data        3600  commontables.o(.constdata)
    arm_cfft_sR_f32_len1024                  0x080075b8   Data          16  commontables.o(.constdata)
    Region$$Table$$Base                      0x08007618   Number         0  anon$$obj.o(Region$$Table)
    Region$$Table$$Limit                     0x08007638   Number         0  anon$$obj.o(Region$$Table)
    freqHistoryIndex                         0x20000000   Data           1  main.o(.data)
    flag                                     0x20000002   Data           2  main.o(.data)
    uwTickFreq                               0x20000004   Data           1  stm32f4xx_hal.o(.data)
    uwTickPrio                               0x20000008   Data           4  stm32f4xx_hal.o(.data)
    uwTick                                   0x2000000c   Data           4  stm32f4xx_hal.o(.data)
    SystemCoreClock                          0x20000010   Data           4  system_stm32f4xx.o(.data)
    CSR_DATA0                                0x20000014   Data           1  ad9959_new.o(.data)
    CSR_DATA1                                0x20000015   Data           1  ad9959_new.o(.data)
    CSR_DATA2                                0x20000016   Data           1  ad9959_new.o(.data)
    CSR_DATA3                                0x20000017   Data           1  ad9959_new.o(.data)
    FR2_DATA                                 0x20000018   Data           2  ad9959_new.o(.data)
    CPOW0_DATA                               0x2000001a   Data           2  ad9959_new.o(.data)
    FR1_DATA                                 0x2000001c   Data           3  ad9959_new.o(.data)
    __stdout                                 0x20000020   Data           4  stdout.o(.data)
    testInput                                0x20000028   Data        8192  main.o(.bss)
    testOutput                               0x20002028   Data        4096  main.o(.bss)
    adc_buff                                 0x20003028   Data        2048  main.o(.bss)
    window                                   0x20003828   Data        4096  main.o(.bss)
    freqHistory                              0x20004828   Data          32  main.o(.bss)
    hadc1                                    0x20004848   Data          72  adc.o(.bss)
    hdma_adc1                                0x20004890   Data          96  adc.o(.bss)
    htim3                                    0x200048f0   Data          72  tim.o(.bss)
    htim4                                    0x20004938   Data          72  tim.o(.bss)
    huart1                                   0x20004980   Data          72  usart.o(.bss)
    AD9959msg                                0x200049c8   Data          32  ad9959_new.o(.bss)
    __initial_sp                             0x20004de8   Data           0  startup_stm32f407xx.o(STACK)



==============================================================================

Memory Map of the image

  Image Entry point : 0x08000189

  Load Region LR_IROM1 (Base: 0x08000000, Size: 0x00007660, Max: 0x00100000, ABSOLUTE)

    Execution Region ER_IROM1 (Exec base: 0x08000000, Load base: 0x08000000, Size: 0x00007638, Max: 0x00100000, ABSOLUTE)

    Exec Addr    Load Addr    Size         Type   Attr      Idx    E Section Name        Object

    0x08000000   0x08000000   0x00000188   Data   RO            3    RESET               startup_stm32f407xx.o
    0x08000188   0x08000188   0x00000000   Code   RO         9613  * .ARM.Collect$$$$00000000  mc_w.l(entry.o)
    0x08000188   0x08000188   0x00000004   Code   RO         9957    .ARM.Collect$$$$00000001  mc_w.l(entry2.o)
    0x0800018c   0x0800018c   0x00000004   Code   RO         9960    .ARM.Collect$$$$00000004  mc_w.l(entry5.o)
    0x08000190   0x08000190   0x00000000   Code   RO         9962    .ARM.Collect$$$$00000008  mc_w.l(entry7b.o)
    0x08000190   0x08000190   0x00000000   Code   RO         9964    .ARM.Collect$$$$0000000A  mc_w.l(entry8b.o)
    0x08000190   0x08000190   0x00000008   Code   RO         9965    .ARM.Collect$$$$0000000B  mc_w.l(entry9a.o)
    0x08000198   0x08000198   0x00000000   Code   RO         9967    .ARM.Collect$$$$0000000D  mc_w.l(entry10a.o)
    0x08000198   0x08000198   0x00000000   Code   RO         9969    .ARM.Collect$$$$0000000F  mc_w.l(entry11a.o)
    0x08000198   0x08000198   0x00000004   Code   RO         9958    .ARM.Collect$$$$00002712  mc_w.l(entry2.o)
    0x0800019c   0x0800019c   0x00000024   Code   RO            4    .text               startup_stm32f407xx.o
    0x080001c0   0x080001c0   0x00000062   Code   RO         9616    .text               mc_w.l(uldiv.o)
    0x08000222   0x08000222   0x0000001e   Code   RO         9620    .text               mc_w.l(llshl.o)
    0x08000240   0x08000240   0x00000024   Code   RO         9622    .text               mc_w.l(llsshr.o)
    0x08000264   0x08000264   0x00000024   Code   RO         9626    .text               mc_w.l(memseta.o)
    0x08000288   0x08000288   0x0000014e   Code   RO         9891    .text               mf_w.l(dadd.o)
    0x080003d6   0x080003d6   0x000000e4   Code   RO         9893    .text               mf_w.l(dmul.o)
    0x080004ba   0x080004ba   0x000000de   Code   RO         9895    .text               mf_w.l(ddiv.o)
    0x08000598   0x08000598   0x0000001a   Code   RO         9901    .text               mf_w.l(dfltui.o)
    0x080005b2   0x080005b2   0x00000032   Code   RO         9907    .text               mf_w.l(dfixui.o)
    0x080005e4   0x080005e4   0x00000026   Code   RO         9911    .text               mf_w.l(f2d.o)
    0x0800060a   0x0800060a   0x00000002   PAD
    0x0800060c   0x0800060c   0x00000030   Code   RO         9915    .text               mf_w.l(cdrcmple.o)
    0x0800063c   0x0800063c   0x0000002c   Code   RO         9972    .text               mc_w.l(uidiv.o)
    0x08000668   0x08000668   0x00000020   Code   RO         9974    .text               mc_w.l(llushr.o)
    0x08000688   0x08000688   0x00000000   Code   RO         9983    .text               mc_w.l(iusefp.o)
    0x08000688   0x08000688   0x000000ba   Code   RO         9988    .text               mf_w.l(depilogue.o)
    0x08000742   0x08000742   0x00000030   Code   RO         9994    .text               mf_w.l(dfixul.o)
    0x08000772   0x08000772   0x00000002   PAD
    0x08000774   0x08000774   0x00000024   Code   RO         9998    .text               mc_w.l(init.o)
    0x08000798   0x08000798   0x0000002c   Code   RO         3380    i.AD9959MSGInit     ad9959_new.o
    0x080007c4   0x080007c4   0x0000006e   Code   RO          577    i.ADC_DMAConvCplt   stm32f4xx_hal_adc.o
    0x08000832   0x08000832   0x00000016   Code   RO          578    i.ADC_DMAError      stm32f4xx_hal_adc.o
    0x08000848   0x08000848   0x0000000a   Code   RO          579    i.ADC_DMAHalfConvCplt  stm32f4xx_hal_adc.o
    0x08000852   0x08000852   0x00000002   PAD
    0x08000854   0x08000854   0x00000128   Code   RO          580    i.ADC_Init          stm32f4xx_hal_adc.o
    0x0800097c   0x0800097c   0x00000002   Code   RO          471    i.BusFault_Handler  stm32f4xx_it.o
    0x0800097e   0x0800097e   0x00000002   PAD
    0x08000980   0x08000980   0x0000000c   Code   RO          472    i.DMA2_Stream0_IRQHandler  stm32f4xx_it.o
    0x0800098c   0x0800098c   0x00000028   Code   RO         1314    i.DMA_CalcBaseAndBitshift  stm32f4xx_hal_dma.o
    0x080009b4   0x080009b4   0x00000054   Code   RO         1315    i.DMA_CheckFifoParam  stm32f4xx_hal_dma.o
    0x08000a08   0x08000a08   0x00000028   Code   RO         1316    i.DMA_SetConfig     stm32f4xx_hal_dma.o
    0x08000a30   0x08000a30   0x00000002   Code   RO          473    i.DebugMon_Handler  stm32f4xx_it.o
    0x08000a32   0x08000a32   0x00000004   Code   RO           13    i.Error_Handler     main.o
    0x08000a36   0x08000a36   0x00000002   PAD
    0x08000a38   0x08000a38   0x000000a8   Code   RO         3540    i.FFT_DetectFundamentalFrequency  fft_processing.o
    0x08000ae0   0x08000ae0   0x0000014c   Code   RO         3541    i.FFT_DetectHarmonic  fft_processing.o
    0x08000c2c   0x08000c2c   0x00000058   Code   RO         3542    i.FFT_InitWindow    fft_processing.o
    0x08000c84   0x08000c84   0x000000e4   Code   RO         3543    i.FFT_Process       fft_processing.o
    0x08000d68   0x08000d68   0x00000044   Code   RO         3544    i.FFT_SmoothFrequency  fft_processing.o
    0x08000dac   0x08000dac   0x0000014c   Code   RO          582    i.HAL_ADC_ConfigChannel  stm32f4xx_hal_adc.o
    0x08000ef8   0x08000ef8   0x00000018   Code   RO           14    i.HAL_ADC_ConvCpltCallback  main.o
    0x08000f10   0x08000f10   0x00000002   Code   RO          584    i.HAL_ADC_ConvHalfCpltCallback  stm32f4xx_hal_adc.o
    0x08000f12   0x08000f12   0x00000002   Code   RO          586    i.HAL_ADC_ErrorCallback  stm32f4xx_hal_adc.o
    0x08000f14   0x08000f14   0x00000054   Code   RO          591    i.HAL_ADC_Init      stm32f4xx_hal_adc.o
    0x08000f68   0x08000f68   0x00000098   Code   RO          304    i.HAL_ADC_MspInit   adc.o
    0x08001000   0x08001000   0x00000158   Code   RO          598    i.HAL_ADC_Start_DMA  stm32f4xx_hal_adc.o
    0x08001158   0x08001158   0x000001a0   Code   RO         1322    i.HAL_DMA_IRQHandler  stm32f4xx_hal_dma.o
    0x080012f8   0x080012f8   0x000000d4   Code   RO         1323    i.HAL_DMA_Init      stm32f4xx_hal_dma.o
    0x080013cc   0x080013cc   0x0000006e   Code   RO         1327    i.HAL_DMA_Start_IT  stm32f4xx_hal_dma.o
    0x0800143a   0x0800143a   0x00000002   PAD
    0x0800143c   0x0800143c   0x000001f0   Code   RO         1210    i.HAL_GPIO_Init     stm32f4xx_hal_gpio.o
    0x0800162c   0x0800162c   0x0000000a   Code   RO         1214    i.HAL_GPIO_WritePin  stm32f4xx_hal_gpio.o
    0x08001636   0x08001636   0x00000002   PAD
    0x08001638   0x08001638   0x0000000c   Code   RO         1760    i.HAL_GetTick       stm32f4xx_hal.o
    0x08001644   0x08001644   0x00000010   Code   RO         1766    i.HAL_IncTick       stm32f4xx_hal.o
    0x08001654   0x08001654   0x00000034   Code   RO         1767    i.HAL_Init          stm32f4xx_hal.o
    0x08001688   0x08001688   0x00000040   Code   RO         1768    i.HAL_InitTick      stm32f4xx_hal.o
    0x080016c8   0x080016c8   0x00000030   Code   RO          553    i.HAL_MspInit       stm32f4xx_hal_msp.o
    0x080016f8   0x080016f8   0x0000001a   Code   RO         1602    i.HAL_NVIC_EnableIRQ  stm32f4xx_hal_cortex.o
    0x08001712   0x08001712   0x00000002   PAD
    0x08001714   0x08001714   0x00000040   Code   RO         1608    i.HAL_NVIC_SetPriority  stm32f4xx_hal_cortex.o
    0x08001754   0x08001754   0x00000024   Code   RO         1609    i.HAL_NVIC_SetPriorityGrouping  stm32f4xx_hal_cortex.o
    0x08001778   0x08001778   0x00000134   Code   RO          856    i.HAL_RCC_ClockConfig  stm32f4xx_hal_rcc.o
    0x080018ac   0x080018ac   0x00000020   Code   RO          863    i.HAL_RCC_GetPCLK1Freq  stm32f4xx_hal_rcc.o
    0x080018cc   0x080018cc   0x00000020   Code   RO          864    i.HAL_RCC_GetPCLK2Freq  stm32f4xx_hal_rcc.o
    0x080018ec   0x080018ec   0x00000060   Code   RO          865    i.HAL_RCC_GetSysClockFreq  stm32f4xx_hal_rcc.o
    0x0800194c   0x0800194c   0x0000036c   Code   RO          868    i.HAL_RCC_OscConfig  stm32f4xx_hal_rcc.o
    0x08001cb8   0x08001cb8   0x00000028   Code   RO         1613    i.HAL_SYSTICK_Config  stm32f4xx_hal_cortex.o
    0x08001ce0   0x08001ce0   0x00000090   Code   RO         2730    i.HAL_TIMEx_MasterConfigSynchronization  stm32f4xx_hal_tim_ex.o
    0x08001d70   0x08001d70   0x0000005a   Code   RO         2007    i.HAL_TIM_Base_Init  stm32f4xx_hal_tim.o
    0x08001dca   0x08001dca   0x00000002   PAD
    0x08001dcc   0x08001dcc   0x00000040   Code   RO          370    i.HAL_TIM_Base_MspInit  tim.o
    0x08001e0c   0x08001e0c   0x00000078   Code   RO         2010    i.HAL_TIM_Base_Start  stm32f4xx_hal_tim.o
    0x08001e84   0x08001e84   0x000000dc   Code   RO         2016    i.HAL_TIM_ConfigClockSource  stm32f4xx_hal_tim.o
    0x08001f60   0x08001f60   0x00000064   Code   RO         3008    i.HAL_UART_Init     stm32f4xx_hal_uart.o
    0x08001fc4   0x08001fc4   0x0000006c   Code   RO          418    i.HAL_UART_MspInit  usart.o
    0x08002030   0x08002030   0x000000a0   Code   RO         3016    i.HAL_UART_Transmit  stm32f4xx_hal_uart.o
    0x080020d0   0x080020d0   0x00000002   Code   RO          474    i.HardFault_Handler  stm32f4xx_it.o
    0x080020d2   0x080020d2   0x00000002   PAD
    0x080020d4   0x080020d4   0x0000003c   Code   RO         3385    i.IO_Update         ad9959_new.o
    0x08002110   0x08002110   0x000001d4   Code   RO         3386    i.Init_AD9959       ad9959_new.o
    0x080022e4   0x080022e4   0x0000003c   Code   RO         3387    i.IntReset          ad9959_new.o
    0x08002320   0x08002320   0x000000a0   Code   RO         3388    i.Intserve          ad9959_new.o
    0x080023c0   0x080023c0   0x00000060   Code   RO          305    i.MX_ADC1_Init      adc.o
    0x08002420   0x08002420   0x0000002c   Code   RO          345    i.MX_DMA_Init       dma.o
    0x0800244c   0x0800244c   0x0000002c   Code   RO          279    i.MX_GPIO_Init      gpio.o
    0x08002478   0x08002478   0x00000068   Code   RO          371    i.MX_TIM3_Init      tim.o
    0x080024e0   0x080024e0   0x00000064   Code   RO          372    i.MX_TIM4_Init      tim.o
    0x08002544   0x08002544   0x00000038   Code   RO          419    i.MX_USART1_UART_Init  usart.o
    0x0800257c   0x0800257c   0x00000002   Code   RO          475    i.MemManage_Handler  stm32f4xx_it.o
    0x0800257e   0x0800257e   0x00000002   Code   RO          476    i.NMI_Handler       stm32f4xx_it.o
    0x08002580   0x08002580   0x00000002   Code   RO          477    i.PendSV_Handler    stm32f4xx_it.o
    0x08002582   0x08002582   0x00000002   Code   RO          478    i.SVC_Handler       stm32f4xx_it.o
    0x08002584   0x08002584   0x00000004   Code   RO          479    i.SysTick_Handler   stm32f4xx_it.o
    0x08002588   0x08002588   0x00000094   Code   RO           15    i.SystemClock_Config  main.o
    0x0800261c   0x0800261c   0x00000010   Code   RO         3342    i.SystemInit        system_stm32f4xx.o
    0x0800262c   0x0800262c   0x000000d0   Code   RO         2100    i.TIM_Base_SetConfig  stm32f4xx_hal_tim.o
    0x080026fc   0x080026fc   0x00000014   Code   RO         2111    i.TIM_ETR_SetConfig  stm32f4xx_hal_tim.o
    0x08002710   0x08002710   0x00000010   Code   RO         2112    i.TIM_ITRx_SetConfig  stm32f4xx_hal_tim.o
    0x08002720   0x08002720   0x00000022   Code   RO         2118    i.TIM_TI1_ConfigInputStage  stm32f4xx_hal_tim.o
    0x08002742   0x08002742   0x00000024   Code   RO         2120    i.TIM_TI2_ConfigInputStage  stm32f4xx_hal_tim.o
    0x08002766   0x08002766   0x0000004e   Code   RO         3031    i.UART_EndRxTransfer  stm32f4xx_hal_uart.o
    0x080027b4   0x080027b4   0x0000010c   Code   RO         3034    i.UART_SetConfig    stm32f4xx_hal_uart.o
    0x080028c0   0x080028c0   0x00000072   Code   RO         3037    i.UART_WaitOnFlagUntilTimeout  stm32f4xx_hal_uart.o
    0x08002932   0x08002932   0x00000002   Code   RO          480    i.UsageFault_Handler  stm32f4xx_it.o
    0x08002934   0x08002934   0x00000068   Code   RO         3392    i.WriteAmplitude    ad9959_new.o
    0x0800299c   0x0800299c   0x000000dc   Code   RO         3393    i.WriteData_AD9959  ad9959_new.o
    0x08002a78   0x08002a78   0x00000094   Code   RO         3394    i.WriteFreq         ad9959_new.o
    0x08002b0c   0x08002b0c   0x00000020   Code   RO         9863    i.__0printf         mc_w.l(printfa.o)
    0x08002b2c   0x08002b2c   0x00000026   Code   RO         9935    i.__ARM_fpclassifyf  m_wm.l(fpclassifyf.o)
    0x08002b52   0x08002b52   0x00000020   Code   RO         1615    i.__NVIC_SetPriority  stm32f4xx_hal_cortex.o
    0x08002b72   0x08002b72   0x00000002   PAD
    0x08002b74   0x08002b74   0x00000150   Code   RO         9463    i.__hardfp_cosf     m_wm.l(cosf.o)
    0x08002cc4   0x08002cc4   0x000001e8   Code   RO         9489    i.__hardfp_expf     m_wm.l(expf.o)
    0x08002eac   0x08002eac   0x0000003a   Code   RO         9589    i.__hardfp_sqrtf    m_wm.l(sqrtf.o)
    0x08002ee6   0x08002ee6   0x00000006   Code   RO         9938    i.__mathlib_flt_infnan  m_wm.l(funder.o)
    0x08002eec   0x08002eec   0x00000010   Code   RO         9940    i.__mathlib_flt_invalid  m_wm.l(funder.o)
    0x08002efc   0x08002efc   0x00000010   Code   RO         9941    i.__mathlib_flt_overflow  m_wm.l(funder.o)
    0x08002f0c   0x08002f0c   0x00000010   Code   RO         9943    i.__mathlib_flt_underflow  m_wm.l(funder.o)
    0x08002f1c   0x08002f1c   0x00000154   Code   RO         9954    i.__mathlib_rredf2  m_wm.l(rredf.o)
    0x08003070   0x08003070   0x0000000e   Code   RO        10002    i.__scatterload_copy  mc_w.l(handlers.o)
    0x0800307e   0x0800307e   0x00000002   Code   RO        10003    i.__scatterload_null  mc_w.l(handlers.o)
    0x08003080   0x08003080   0x0000000e   Code   RO        10004    i.__scatterload_zeroinit  mc_w.l(handlers.o)
    0x0800308e   0x0800308e   0x00000002   PAD
    0x08003090   0x08003090   0x0000000c   Code   RO         9978    i.__set_errno       mc_w.l(errno.o)
    0x0800309c   0x0800309c   0x00000184   Code   RO         9870    i._fp_digits        mc_w.l(printfa.o)
    0x08003220   0x08003220   0x000006dc   Code   RO         9871    i._printf_core      mc_w.l(printfa.o)
    0x080038fc   0x080038fc   0x00000024   Code   RO         9872    i._printf_post_padding  mc_w.l(printfa.o)
    0x08003920   0x08003920   0x0000002e   Code   RO         9873    i._printf_pre_padding  mc_w.l(printfa.o)
    0x0800394e   0x0800394e   0x00000002   PAD
    0x08003950   0x08003950   0x0000003e   Code   RO         8694    i.arm_bitreversal_32  transformfunctions.o
    0x0800398e   0x0800398e   0x000000cc   Code   RO         8699    i.arm_cfft_f32      transformfunctions.o
    0x08003a5a   0x08003a5a   0x00000188   Code   RO         8724    i.arm_cfft_radix8by2_f32  transformfunctions.o
    0x08003be2   0x08003be2   0x0000042c   Code   RO         8725    i.arm_cfft_radix8by4_f32  transformfunctions.o
    0x0800400e   0x0800400e   0x00000002   PAD
    0x08004010   0x08004010   0x00000044   Code   RO         4667    i.arm_cmplx_mag_f32  complexmathfunctions.o
    0x08004054   0x08004054   0x000004c4   Code   RO         8751    i.arm_radix8_butterfly_f32  transformfunctions.o
    0x08004518   0x08004518   0x0000007c   Code   RO         3545    i.calculateThreshold  fft_processing.o
    0x08004594   0x08004594   0x0000000c   Code   RO         3400    i.delay1            ad9959_new.o
    0x080045a0   0x080045a0   0x00000018   Code   RO          421    i.fputc             usart.o
    0x080045b8   0x080045b8   0x00000168   Code   RO           16    i.main              main.o
    0x08004720   0x08004720   0x00000068   Code   RO         3546    i.parabolicInterpolation  fft_processing.o
    0x08004788   0x08004788   0x00000008   Data   RO         1329    .constdata          stm32f4xx_hal_dma.o
    0x08004790   0x08004790   0x00000010   Data   RO         3343    .constdata          system_stm32f4xx.o
    0x080047a0   0x080047a0   0x00000008   Data   RO         3344    .constdata          system_stm32f4xx.o
    0x080047a8   0x080047a8   0x00002000   Data   RO         4428    .constdata          commontables.o
    0x080067a8   0x080067a8   0x00000e10   Data   RO         4464    .constdata          commontables.o
    0x080075b8   0x080075b8   0x00000010   Data   RO         4544    .constdata          commontables.o
    0x080075c8   0x080075c8   0x00000030   Data   RO         9492    .constdata          m_wm.l(expf.o)
    0x080075f8   0x080075f8   0x00000020   Data   RO         9955    .constdata          m_wm.l(rredf.o)
    0x08007618   0x08007618   0x00000020   Data   RO        10000    Region$$Table       anon$$obj.o


    Execution Region RW_IRAM1 (Exec base: 0x20000000, Load base: 0x08007638, Size: 0x00004de8, Max: 0x0001c000, ABSOLUTE)

    Exec Addr    Load Addr    Size         Type   Attr      Idx    E Section Name        Object

    0x20000000   0x08007638   0x00000004   Data   RW           19    .data               main.o
    0x20000004   0x0800763c   0x0000000c   Data   RW         1774    .data               stm32f4xx_hal.o
    0x20000010   0x08007648   0x00000004   Data   RW         3345    .data               system_stm32f4xx.o
    0x20000014   0x0800764c   0x0000000b   Data   RW         3402    .data               ad9959_new.o
    0x2000001f   0x08007657   0x00000001   PAD
    0x20000020   0x08007658   0x00000004   Data   RW         9971    .data               mc_w.l(stdout.o)
    0x20000024   0x0800765c   0x00000004   Data   RW         9979    .data               mc_w.l(errno.o)
    0x20000028        -       0x00004820   Zero   RW           18    .bss                main.o
    0x20004848        -       0x000000a8   Zero   RW          306    .bss                adc.o
    0x200048f0        -       0x00000090   Zero   RW          373    .bss                tim.o
    0x20004980        -       0x00000048   Zero   RW          422    .bss                usart.o
    0x200049c8        -       0x00000020   Zero   RW         3401    .bss                ad9959_new.o
    0x200049e8        -       0x00000400   Zero   RW            1    STACK               startup_stm32f407xx.o


    Execution Region RW_IRAM2 (Exec base: 0x2001c000, Load base: 0x08007660, Size: 0x00000000, Max: 0x00004000, ABSOLUTE)

    **** No section assigned to this execution region ****


==============================================================================

Image component sizes


      Code (inc. data)   RO Data    RW Data    ZI Data      Debug   Object Name

      1276        104          0         11         32       9251   ad9959_new.o
       248         28          0          0        168       1711   adc.o
         0          0      11808          0          0      13461   commontables.o
        68          6          0          0          0       1952   complexmathfunctions.o
        44          4          0          0          0        742   dma.o
      1112         82          0          0          0       7491   fft_processing.o
        44          6          0          0          0        767   gpio.o
       536         80          0          4      18464     715422   main.o
        36          8        392          0       1024        820   startup_stm32f407xx.o
       144         24          0         12          0       8661   stm32f4xx_hal.o
      1202         66          0          0          0       6814   stm32f4xx_hal_adc.o
       198         14          0          0          0      33727   stm32f4xx_hal_cortex.o
       902         16          8          0          0       5691   stm32f4xx_hal_dma.o
       506         46          0          0          0       2144   stm32f4xx_hal_gpio.o
        48          6          0          0          0        826   stm32f4xx_hal_msp.o
      1344         72          0          0          0       5164   stm32f4xx_hal_rcc.o
       744         74          0          0          0       6880   stm32f4xx_hal_tim.o
       144         28          0          0          0       1344   stm32f4xx_hal_tim_ex.o
       720         10          0          0          0       4760   stm32f4xx_hal_uart.o
        32          6          0          0          0       4484   stm32f4xx_it.o
        16          4         24          4          0       1079   system_stm32f4xx.o
       268         30          0          0        144       2291   tim.o
      2946          4          0          0          0       9489   transformfunctions.o
       188         26          0          0         72       2194   usart.o

    ----------------------------------------------------------------------
     12786        <USER>      <GROUP>         32      19904     847165   Object Totals
         0          0         32          0          0          0   (incl. Generated)
        20          0          0          1          0          0   (incl. Padding)

    ----------------------------------------------------------------------

      Code (inc. data)   RO Data    RW Data    ZI Data      Debug   Library Member Name

       336         56          0          0          0        136   cosf.o
       488         62         48          0          0        236   expf.o
        38          0          0          0          0        116   fpclassifyf.o
        54         18          0          0          0        464   funder.o
       340         24         32          0          0        160   rredf.o
        58          0          0          0          0        136   sqrtf.o
         0          0          0          0          0          0   entry.o
         0          0          0          0          0          0   entry10a.o
         0          0          0          0          0          0   entry11a.o
         8          4          0          0          0          0   entry2.o
         4          0          0          0          0          0   entry5.o
         0          0          0          0          0          0   entry7b.o
         0          0          0          0          0          0   entry8b.o
         8          4          0          0          0          0   entry9a.o
        12          6          0          4          0         68   errno.o
        30          0          0          0          0          0   handlers.o
        36          8          0          0          0         68   init.o
         0          0          0          0          0          0   iusefp.o
        30          0          0          0          0         68   llshl.o
        36          0          0          0          0         68   llsshr.o
        32          0          0          0          0         68   llushr.o
        36          0          0          0          0        108   memseta.o
      2258         90          0          0          0        452   printfa.o
         0          0          0          4          0          0   stdout.o
        44          0          0          0          0         80   uidiv.o
        98          0          0          0          0         92   uldiv.o
        48          0          0          0          0         68   cdrcmple.o
       334          0          0          0          0        148   dadd.o
       222          0          0          0          0        100   ddiv.o
       186          0          0          0          0        176   depilogue.o
        50          0          0          0          0         76   dfixui.o
        48          0          0          0          0         68   dfixul.o
        26          0          0          0          0         76   dfltui.o
       228          0          0          0          0         96   dmul.o
        38          0          0          0          0         68   f2d.o

    ----------------------------------------------------------------------
      5134        <USER>         <GROUP>          8          0       3196   Library Totals
         8          0          0          0          0          0   (incl. Padding)

    ----------------------------------------------------------------------

      Code (inc. data)   RO Data    RW Data    ZI Data      Debug   Library Name

      1314        160         80          0          0       1248   m_wm.l
      2632        112          0          8          0       1072   mc_w.l
      1180          0          0          0          0        876   mf_w.l

    ----------------------------------------------------------------------
      5134        <USER>         <GROUP>          8          0       3196   Library Totals

    ----------------------------------------------------------------------

==============================================================================


      Code (inc. data)   RO Data    RW Data    ZI Data      Debug   

     17920       1016      12344         40      19904     840329   Grand Totals
     17920       1016      12344         40      19904     840329   ELF Image Totals
     17920       1016      12344         40          0          0   ROM Totals

==============================================================================

    Total RO  Size (Code + RO Data)                30264 (  29.55kB)
    Total RW  Size (RW Data + ZI Data)             19944 (  19.48kB)
    Total ROM Size (Code + RO Data + RW Data)      30304 (  29.59kB)

==============================================================================

