/**
 * @file freq_sweep.c
 * @brief 频率扫描控制实现文件
 * <AUTHOR>
 * @date 2025.7.30
 */

#include "freq_sweep.h"
#include "fft_processing.h"
#include <stdio.h>
#include <math.h>

// 全局变量定义
FreqSweepConfig_t g_sweep_config;
FreqSweepStatus_t g_sweep_status;
MeasureResult_t   g_measure_result;

/**
 * @brief 初始化频率扫描模块
 */
void FreqSweep_Init(void) {
    // 默认配置：1kHz到50kHz，步长200Hz
    g_sweep_config.start_freq = 1000;
    g_sweep_config.end_freq = 50000;
    g_sweep_config.step_freq = 200;
    g_sweep_config.dwell_time = 100;  // 100ms
    g_sweep_config.channel = 0;       // 使用通道0
    g_sweep_config.auto_restart = 1;  // 自动重启
    
    // 初始化状态
    g_sweep_status.current_freq = g_sweep_config.start_freq;
    g_sweep_status.current_index = 0;
    g_sweep_status.total_points = (g_sweep_config.end_freq - g_sweep_config.start_freq) / g_sweep_config.step_freq + 1;
    g_sweep_status.is_running = 0;
    g_sweep_status.is_complete = 0;
    g_sweep_status.start_time = 0;
    
    printf("FreqSweep initialized: %lu-%lu Hz, step %lu Hz, %d points\r\n", 
           g_sweep_config.start_freq, g_sweep_config.end_freq, 
           g_sweep_config.step_freq, g_sweep_status.total_points);
}

/**
 * @brief 设置扫频配置
 */
void FreqSweep_SetConfig(uint32_t start_freq, uint32_t end_freq, uint32_t step_freq, uint32_t dwell_time) {
    g_sweep_config.start_freq = start_freq;
    g_sweep_config.end_freq = end_freq;
    g_sweep_config.step_freq = step_freq;
    g_sweep_config.dwell_time = dwell_time;
    
    // 重新计算总点数
    g_sweep_status.total_points = (end_freq - start_freq) / step_freq + 1;
    
    printf("FreqSweep config updated: %lu-%lu Hz, step %lu Hz, dwell %lu ms\r\n", 
           start_freq, end_freq, step_freq, dwell_time);
}

/**
 * @brief 开始频率扫描
 */
void FreqSweep_Start(void) {
    g_sweep_status.current_freq = g_sweep_config.start_freq;
    g_sweep_status.current_index = 0;
    g_sweep_status.is_running = 1;
    g_sweep_status.is_complete = 0;
    g_sweep_status.start_time = HAL_GetTick();
    
    // 设置初始频率
    WriteFreq(g_sweep_config.channel, g_sweep_status.current_freq, 1);
    
    printf("FreqSweep started at %lu Hz\r\n", g_sweep_status.current_freq);
}

/**
 * @brief 停止频率扫描
 */
void FreqSweep_Stop(void) {
    g_sweep_status.is_running = 0;
    printf("FreqSweep stopped\r\n");
}

/**
 * @brief 重置频率扫描
 */
void FreqSweep_Reset(void) {
    g_sweep_status.current_freq = g_sweep_config.start_freq;
    g_sweep_status.current_index = 0;
    g_sweep_status.is_complete = 0;
    g_sweep_status.start_time = HAL_GetTick();
}

/**
 * @brief 设置下一个频率点
 */
void FreqSweep_SetNextFreq(void) {
    if (!g_sweep_status.is_running) {
        return;
    }
    
    // 移动到下一个频率点
    g_sweep_status.current_index++;
    
    // 检查是否完成扫描
    if (g_sweep_status.current_index >= g_sweep_status.total_points) {
        g_sweep_status.is_complete = 1;
        
        if (g_sweep_config.auto_restart) {
            // 自动重启
            FreqSweep_Reset();
            printf("FreqSweep completed, auto restarting...\r\n");
        } else {
            // 停止扫描
            FreqSweep_Stop();
            printf("FreqSweep completed\r\n");
            return;
        }
    }
    
    // 计算新频率
    g_sweep_status.current_freq = g_sweep_config.start_freq + 
                                  g_sweep_status.current_index * g_sweep_config.step_freq;
    
    // 设置AD9959输出频率
    WriteFreq(g_sweep_config.channel, g_sweep_status.current_freq, 1);
    
    printf("FreqSweep: %d/%d, Freq: %lu Hz\r\n", 
           g_sweep_status.current_index + 1, g_sweep_status.total_points, 
           g_sweep_status.current_freq);
}

/**
 * @brief 检查扫频是否完成
 */
uint8_t FreqSweep_IsComplete(void) {
    return g_sweep_status.is_complete;
}

/**
 * @brief 处理测量数据
 */
void FreqSweep_ProcessMeasurement(uint16_t* adc_data, float* fft_output) {
    if (!g_sweep_status.is_running) {
        return;
    }
    
    // 检测基频
    float detected_freq = FFT_DetectFundamentalFrequency(fft_output, 1000000.0f, 50.0f, 500000.0f);
    
    // 检测谐波
    float harmonic1 = FFT_DetectHarmonic(fft_output, 1000000.0f, detected_freq, 1);
    float harmonic2 = FFT_DetectHarmonic(fft_output, 1000000.0f, detected_freq, 2);
    
    // 计算总谐波失真 (简化计算)
    float fundamental_power = harmonic1 * harmonic1;
    float harmonic_power = harmonic2 * harmonic2;  // 可以添加更多谐波
    float thd = (fundamental_power > 0) ? sqrt(harmonic_power / fundamental_power) * 100.0f : 0.0f;
    
    // 保存测量结果
    g_measure_result.test_freq = g_sweep_status.current_freq;
    g_measure_result.detected_freq = detected_freq;
    g_measure_result.amplitude = harmonic1;
    g_measure_result.harmonic1 = harmonic1;
    g_measure_result.harmonic2 = harmonic2;
    g_measure_result.thd = thd;
    g_measure_result.timestamp = HAL_GetTick();
    
    // 输出测量结果
    printf("Test: %lu Hz, Detected: %.2f Hz, Amp: %.3f, H2: %.3f, THD: %.2f%%\r\n",
           g_measure_result.test_freq, g_measure_result.detected_freq,
           g_measure_result.amplitude, g_measure_result.harmonic2, g_measure_result.thd);
}
