/**
 * @file freq_sweep.h
 * @brief 频率扫描控制头文件
 * <AUTHOR>
 * @date 2025.7.30
 */

#ifndef __FREQ_SWEEP_H__
#define __FREQ_SWEEP_H__

#include "main.h"
#include "AD9959_NEW.H"

// 频率扫描配置
typedef struct {
    uint32_t start_freq;        // 起始频率 (Hz)
    uint32_t end_freq;          // 结束频率 (Hz)
    uint32_t step_freq;         // 频率步长 (Hz)
    uint32_t dwell_time;        // 每个频率点的停留时间 (ms)
    uint8_t  channel;           // AD9959输出通道 (0-3)
    uint8_t  auto_restart;      // 自动重启扫频 (0:否, 1:是)
} FreqSweepConfig_t;

// 频率扫描状态
typedef struct {
    uint32_t current_freq;      // 当前频率
    uint16_t current_index;     // 当前频率索引
    uint16_t total_points;      // 总频率点数
    uint8_t  is_running;        // 扫频运行状态
    uint8_t  is_complete;       // 扫频完成标志
    uint32_t start_time;        // 扫频开始时间
} FreqSweepStatus_t;

// 测量结果结构
typedef struct {
    uint32_t test_freq;         // 测试频率
    float    detected_freq;     // 检测到的频率
    float    amplitude;         // 幅度
    float    harmonic1;         // 一次谐波
    float    harmonic2;         // 二次谐波
    float    thd;               // 总谐波失真
    uint32_t timestamp;         // 时间戳
} MeasureResult_t;

// 全局变量声明
extern FreqSweepConfig_t g_sweep_config;
extern FreqSweepStatus_t g_sweep_status;
extern MeasureResult_t   g_measure_result;

// 函数声明
void FreqSweep_Init(void);
void FreqSweep_SetConfig(uint32_t start_freq, uint32_t end_freq, uint32_t step_freq, uint32_t dwell_time);
void FreqSweep_Start(void);
void FreqSweep_Stop(void);
void FreqSweep_Reset(void);
void FreqSweep_SetNextFreq(void);
uint8_t FreqSweep_IsComplete(void);
void FreqSweep_ProcessMeasurement(uint16_t* adc_data, float* fft_output);

// 预定义的扫频配置
#define FREQ_SWEEP_1K_50K_200HZ() FreqSweep_SetConfig(1000, 50000, 200, 100)
#define FREQ_SWEEP_1K_10K_100HZ() FreqSweep_SetConfig(1000, 10000, 100, 50)
#define FREQ_SWEEP_10K_50K_500HZ() FreqSweep_SetConfig(10000, 50000, 500, 200)

#endif /* __FREQ_SWEEP_H__ */
