/**
 * @file freq_sweep_test.c
 * @brief 频率扫描测试和验证程序
 * <AUTHOR>
 * @date 2025.7.30
 */

#include "freq_sweep_test.h"
#include "freq_sweep.h"
#include <stdio.h>

/**
 * @brief 测试频率扫描配置
 */
void FreqSweepTest_Config(void) {
    printf("=== Frequency Sweep Configuration Test ===\r\n");
    
    // 测试默认配置
    FreqSweep_Init();
    printf("Default config: %lu-%lu Hz, step %lu Hz\r\n", 
           g_sweep_config.start_freq, g_sweep_config.end_freq, g_sweep_config.step_freq);
    
    // 测试自定义配置
    FreqSweep_SetConfig(2000, 20000, 500, 200);
    printf("Custom config: %lu-%lu Hz, step %lu Hz, dwell %lu ms\r\n", 
           g_sweep_config.start_freq, g_sweep_config.end_freq, 
           g_sweep_config.step_freq, g_sweep_config.dwell_time);
    
    // 测试预定义配置
    FREQ_SWEEP_1K_10K_100HZ();
    printf("Preset config: %lu-%lu Hz, step %lu Hz\r\n", 
           g_sweep_config.start_freq, g_sweep_config.end_freq, g_sweep_config.step_freq);
    
    printf("Configuration test completed.\r\n\r\n");
}

/**
 * @brief 测试频率计算
 */
void FreqSweepTest_FreqCalculation(void) {
    printf("=== Frequency Calculation Test ===\r\n");
    
    // 设置测试配置
    FreqSweep_SetConfig(1000, 5000, 200, 100);
    FreqSweep_Start();
    
    // 测试前10个频率点
    for (int i = 0; i < 10 && i < g_sweep_status.total_points; i++) {
        uint32_t expected_freq = g_sweep_config.start_freq + i * g_sweep_config.step_freq;
        printf("Point %d: Expected %lu Hz, Current %lu Hz\r\n", 
               i, expected_freq, g_sweep_status.current_freq);
        
        FreqSweep_SetNextFreq();
    }
    
    printf("Frequency calculation test completed.\r\n\r\n");
}

/**
 * @brief 测试扫频状态管理
 */
void FreqSweepTest_StateManagement(void) {
    printf("=== State Management Test ===\r\n");
    
    // 初始状态测试
    FreqSweep_Init();
    printf("Initial state - Running: %d, Complete: %d\r\n", 
           g_sweep_status.is_running, g_sweep_status.is_complete);
    
    // 启动测试
    FreqSweep_Start();
    printf("After start - Running: %d, Complete: %d\r\n", 
           g_sweep_status.is_running, g_sweep_status.is_complete);
    
    // 停止测试
    FreqSweep_Stop();
    printf("After stop - Running: %d, Complete: %d\r\n", 
           g_sweep_status.is_running, g_sweep_status.is_complete);
    
    // 重置测试
    FreqSweep_Reset();
    printf("After reset - Index: %d, Freq: %lu Hz\r\n", 
           g_sweep_status.current_index, g_sweep_status.current_freq);
    
    printf("State management test completed.\r\n\r\n");
}

/**
 * @brief 测试边界条件
 */
void FreqSweepTest_BoundaryConditions(void) {
    printf("=== Boundary Conditions Test ===\r\n");
    
    // 测试单点扫频
    FreqSweep_SetConfig(5000, 5000, 1000, 100);
    printf("Single point sweep - Total points: %d\r\n", g_sweep_status.total_points);
    
    // 测试大步长
    FreqSweep_SetConfig(1000, 10000, 10000, 100);
    printf("Large step sweep - Total points: %d\r\n", g_sweep_status.total_points);
    
    // 测试小范围扫频
    FreqSweep_SetConfig(1000, 1200, 50, 100);
    printf("Small range sweep - Total points: %d\r\n", g_sweep_status.total_points);
    
    printf("Boundary conditions test completed.\r\n\r\n");
}

/**
 * @brief 运行所有测试
 */
void FreqSweepTest_RunAll(void) {
    printf("\r\n=== Frequency Sweep Test Suite ===\r\n");
    printf("Starting comprehensive tests...\r\n\r\n");
    
    FreqSweepTest_Config();
    FreqSweepTest_FreqCalculation();
    FreqSweepTest_StateManagement();
    FreqSweepTest_BoundaryConditions();
    
    printf("=== All Tests Completed ===\r\n");
    printf("Please verify the output above for correctness.\r\n\r\n");
}

/**
 * @brief 显示当前扫频状态
 */
void FreqSweepTest_ShowStatus(void) {
    printf("=== Current Sweep Status ===\r\n");
    printf("Config: %lu-%lu Hz, step %lu Hz, dwell %lu ms\r\n",
           g_sweep_config.start_freq, g_sweep_config.end_freq,
           g_sweep_config.step_freq, g_sweep_config.dwell_time);
    printf("Status: Index %d/%d, Freq %lu Hz\r\n",
           g_sweep_status.current_index, g_sweep_status.total_points,
           g_sweep_status.current_freq);
    printf("State: Running %d, Complete %d\r\n",
           g_sweep_status.is_running, g_sweep_status.is_complete);
    printf("Channel: %d, Auto-restart: %d\r\n",
           g_sweep_config.channel, g_sweep_config.auto_restart);
    printf("========================\r\n\r\n");
}

/**
 * @brief 模拟测量数据处理
 */
void FreqSweepTest_SimulateMeasurement(void) {
    printf("=== Measurement Simulation Test ===\r\n");
    
    // 模拟ADC数据（简单的正弦波）
    uint16_t sim_adc_data[1024];
    float sim_fft_output[1024];
    
    // 生成模拟数据
    for (int i = 0; i < 1024; i++) {
        sim_adc_data[i] = 2048 + 1000 * sin(2 * 3.14159 * i / 1024);
        sim_fft_output[i] = (i == 10) ? 1000.0f : 10.0f;  // 模拟FFT输出
    }
    
    // 设置测试频率
    FreqSweep_SetConfig(1000, 2000, 200, 100);
    FreqSweep_Start();
    
    // 模拟几个测量点
    for (int i = 0; i < 5; i++) {
        printf("Simulating measurement at %lu Hz...\r\n", g_sweep_status.current_freq);
        FreqSweep_ProcessMeasurement(sim_adc_data, sim_fft_output);
        FreqSweep_SetNextFreq();
    }
    
    printf("Measurement simulation completed.\r\n\r\n");
}
