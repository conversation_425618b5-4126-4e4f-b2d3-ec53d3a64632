# 频率扫描系统使用说明

## 概述
本系统实现了1kHz到50kHz的频率扫描功能，步长为200Hz，用于信号分析和测试。

## 主要特性
- 频率范围：1kHz - 50kHz
- 频率步长：200Hz（可配置）
- 自动扫频：支持循环扫描
- 实时分析：FFT频谱分析、谐波检测
- 灵活配置：支持多种扫频模式

## 文件结构
```
Core/Src/main.c          - 主程序文件
MDK-ARM/USER/freq_sweep.h - 频率扫描头文件
MDK-ARM/USER/freq_sweep.c - 频率扫描实现文件
MDK-ARM/USER/AD9959_NEW.H - AD9959驱动头文件
MDK-ARM/USER/AD9959_NEW.C - AD9959驱动实现文件
```

## 主要修改内容

### 1. 添加频率扫描参数
在main.c中添加了频率扫描相关的宏定义和变量：
- `FREQ_START`: 起始频率 1kHz
- `FREQ_END`: 结束频率 50kHz
- `FREQ_STEP`: 频率步长 200Hz

### 2. 创建频率扫描模块
新增了`freq_sweep.h`和`freq_sweep.c`文件，提供：
- 扫频配置管理
- 扫频状态控制
- 测量结果处理
- 预定义扫频模式

### 3. 修改主程序逻辑
- 初始化时设置扫频参数
- 主循环中处理频率扫描和测量
- 自动切换到下一个频率点

## 使用方法

### 基本使用
```c
// 初始化频率扫描
FreqSweep_Init();

// 使用预定义配置：1kHz-50kHz，步长200Hz
FREQ_SWEEP_1K_50K_200HZ();

// 开始扫频
FreqSweep_Start();
```

### 自定义配置
```c
// 自定义扫频参数
FreqSweep_SetConfig(
    5000,   // 起始频率 5kHz
    25000,  // 结束频率 25kHz
    500,    // 频率步长 500Hz
    150     // 停留时间 150ms
);
```

### 预定义配置
- `FREQ_SWEEP_1K_50K_200HZ()`: 1kHz-50kHz，步长200Hz
- `FREQ_SWEEP_1K_10K_100HZ()`: 1kHz-10kHz，步长100Hz
- `FREQ_SWEEP_10K_50K_500HZ()`: 10kHz-50kHz，步长500Hz

## 输出格式
系统会通过串口输出测量结果：
```
Test: 1000 Hz, Detected: 1002.34 Hz, Amp: 0.856, H2: 0.023, THD: 2.68%
Test: 1200 Hz, Detected: 1198.76 Hz, Amp: 0.892, H2: 0.019, THD: 2.13%
...
```

输出包含：
- Test: 设置的测试频率
- Detected: 检测到的实际频率
- Amp: 基波幅度
- H2: 二次谐波幅度
- THD: 总谐波失真百分比

## 系统参数

### 采样参数
- ADC采样频率: 1MHz（由TIM3控制，Period=83）
- FFT长度: 1024点
- 频率分辨率: ~977Hz

### 扫频参数
- 默认停留时间: 100ms每个频率点
- 总扫频时间: 约24.5秒（245个频率点 × 100ms）
- 自动重启: 支持循环扫描

## 注意事项

1. **频率分辨率限制**: 由于FFT长度为1024，频率分辨率约为977Hz，对于200Hz步长的扫频，可能存在频率检测精度限制。

2. **系统稳定时间**: 每个频率点设置了100ms的稳定时间，确保AD9959输出稳定后再进行测量。

3. **内存使用**: FFT处理需要较多内存，注意STM32的RAM使用情况。

4. **串口输出**: 大量的printf输出可能影响实时性，可根据需要调整输出频率。

## 扩展功能

### 添加更多谐波检测
可以在`FreqSweep_ProcessMeasurement`函数中添加更多谐波检测：
```c
float harmonic3 = FFT_DetectHarmonic(fft_output, 1000000.0f, detected_freq, 3);
float harmonic4 = FFT_DetectHarmonic(fft_output, 1000000.0f, detected_freq, 4);
```

### 数据存储
可以添加数据存储功能，将测量结果保存到Flash或SD卡：
```c
// 在freq_sweep.c中添加数据存储函数
void FreqSweep_SaveResult(MeasureResult_t* result);
```

### 远程控制
可以通过串口命令控制扫频参数：
```c
// 添加串口命令解析
void FreqSweep_ParseCommand(char* cmd);
```

## 故障排除

1. **频率检测不准确**: 检查FFT窗函数设置和采样频率配置
2. **扫频不工作**: 确认AD9959初始化正常，检查WriteFreq函数调用
3. **系统卡死**: 检查DMA配置和中断处理函数
4. **输出异常**: 确认串口配置和printf重定向设置

## 版本历史
- v1.0 (2025.7.30): 初始版本，支持1kHz-50kHz扫频，步长200Hz
