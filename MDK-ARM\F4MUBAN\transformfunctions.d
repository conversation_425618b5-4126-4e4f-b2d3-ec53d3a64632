f4muban\transformfunctions.o: D:/keil5/ARM/PACK/ARM/CMSIS-DSP/1.14.2/Source/TransformFunctions/TransformFunctions.c
f4muban\transformfunctions.o: D:/keil5/ARM/PACK/ARM/CMSIS-DSP/1.14.2/Source/TransformFunctions/arm_bitreversal.c
f4muban\transformfunctions.o: D:\keil5\ARM\PACK\ARM\CMSIS-DSP\1.14.2\Include\dsp/transform_functions.h
f4muban\transformfunctions.o: D:\keil5\ARM\PACK\ARM\CMSIS-DSP\1.14.2\Include\arm_math_types.h
f4muban\transformfunctions.o: ../Drivers/CMSIS/Include/cmsis_compiler.h
f4muban\transformfunctions.o: D:\keil5\ARM\ARMCC\Bin\..\include\stdint.h
f4muban\transformfunctions.o: ../Drivers/CMSIS/Include/cmsis_armcc.h
f4muban\transformfunctions.o: D:\keil5\ARM\ARMCC\Bin\..\include\string.h
f4muban\transformfunctions.o: D:\keil5\ARM\ARMCC\Bin\..\include\math.h
f4muban\transformfunctions.o: D:\keil5\ARM\ARMCC\Bin\..\include\float.h
f4muban\transformfunctions.o: D:\keil5\ARM\ARMCC\Bin\..\include\limits.h
f4muban\transformfunctions.o: D:\keil5\ARM\PACK\ARM\CMSIS-DSP\1.14.2\Include\arm_math_memory.h
f4muban\transformfunctions.o: D:\keil5\ARM\PACK\ARM\CMSIS-DSP\1.14.2\Include\dsp/none.h
f4muban\transformfunctions.o: D:\keil5\ARM\PACK\ARM\CMSIS-DSP\1.14.2\Include\dsp/utils.h
f4muban\transformfunctions.o: D:\keil5\ARM\PACK\ARM\CMSIS-DSP\1.14.2\Include\dsp/basic_math_functions.h
f4muban\transformfunctions.o: D:\keil5\ARM\PACK\ARM\CMSIS-DSP\1.14.2\Include\dsp/complex_math_functions.h
f4muban\transformfunctions.o: D:\keil5\ARM\PACK\ARM\CMSIS-DSP\1.14.2\Include\dsp/fast_math_functions.h
f4muban\transformfunctions.o: D:\keil5\ARM\PACK\ARM\CMSIS-DSP\1.14.2\Include\arm_common_tables.h
f4muban\transformfunctions.o: D:/keil5/ARM/PACK/ARM/CMSIS-DSP/1.14.2/Source/TransformFunctions/arm_bitreversal2.c
f4muban\transformfunctions.o: D:/keil5/ARM/PACK/ARM/CMSIS-DSP/1.14.2/Source/TransformFunctions/arm_cfft_f32.c
f4muban\transformfunctions.o: D:/keil5/ARM/PACK/ARM/CMSIS-DSP/1.14.2/Source/TransformFunctions/arm_cfft_f64.c
f4muban\transformfunctions.o: D:/keil5/ARM/PACK/ARM/CMSIS-DSP/1.14.2/Source/TransformFunctions/arm_cfft_q15.c
f4muban\transformfunctions.o: D:/keil5/ARM/PACK/ARM/CMSIS-DSP/1.14.2/Source/TransformFunctions/arm_cfft_q31.c
f4muban\transformfunctions.o: D:/keil5/ARM/PACK/ARM/CMSIS-DSP/1.14.2/Source/TransformFunctions/arm_cfft_init_f32.c
f4muban\transformfunctions.o: D:\keil5\ARM\PACK\ARM\CMSIS-DSP\1.14.2\Include\arm_const_structs.h
f4muban\transformfunctions.o: D:/keil5/ARM/PACK/ARM/CMSIS-DSP/1.14.2/Source/TransformFunctions/arm_cfft_init_f64.c
f4muban\transformfunctions.o: D:/keil5/ARM/PACK/ARM/CMSIS-DSP/1.14.2/Source/TransformFunctions/arm_cfft_init_q15.c
f4muban\transformfunctions.o: D:/keil5/ARM/PACK/ARM/CMSIS-DSP/1.14.2/Source/TransformFunctions/arm_cfft_init_q31.c
f4muban\transformfunctions.o: D:/keil5/ARM/PACK/ARM/CMSIS-DSP/1.14.2/Source/TransformFunctions/arm_cfft_radix2_f32.c
f4muban\transformfunctions.o: D:/keil5/ARM/PACK/ARM/CMSIS-DSP/1.14.2/Source/TransformFunctions/arm_cfft_radix2_q15.c
f4muban\transformfunctions.o: D:/keil5/ARM/PACK/ARM/CMSIS-DSP/1.14.2/Source/TransformFunctions/arm_cfft_radix2_q31.c
f4muban\transformfunctions.o: D:/keil5/ARM/PACK/ARM/CMSIS-DSP/1.14.2/Source/TransformFunctions/arm_cfft_radix4_f32.c
f4muban\transformfunctions.o: D:/keil5/ARM/PACK/ARM/CMSIS-DSP/1.14.2/Source/TransformFunctions/arm_cfft_radix4_q15.c
f4muban\transformfunctions.o: D:/keil5/ARM/PACK/ARM/CMSIS-DSP/1.14.2/Source/TransformFunctions/arm_cfft_radix4_q31.c
f4muban\transformfunctions.o: D:/keil5/ARM/PACK/ARM/CMSIS-DSP/1.14.2/Source/TransformFunctions/arm_cfft_radix8_f32.c
f4muban\transformfunctions.o: D:/keil5/ARM/PACK/ARM/CMSIS-DSP/1.14.2/Source/TransformFunctions/arm_rfft_fast_f32.c
f4muban\transformfunctions.o: D:/keil5/ARM/PACK/ARM/CMSIS-DSP/1.14.2/Source/TransformFunctions/arm_rfft_fast_f64.c
f4muban\transformfunctions.o: D:/keil5/ARM/PACK/ARM/CMSIS-DSP/1.14.2/Source/TransformFunctions/arm_rfft_fast_init_f32.c
f4muban\transformfunctions.o: D:/keil5/ARM/PACK/ARM/CMSIS-DSP/1.14.2/Source/TransformFunctions/arm_rfft_fast_init_f64.c
f4muban\transformfunctions.o: D:/keil5/ARM/PACK/ARM/CMSIS-DSP/1.14.2/Source/TransformFunctions/arm_mfcc_init_f32.c
f4muban\transformfunctions.o: D:/keil5/ARM/PACK/ARM/CMSIS-DSP/1.14.2/Source/TransformFunctions/arm_mfcc_f32.c
f4muban\transformfunctions.o: D:\keil5\ARM\PACK\ARM\CMSIS-DSP\1.14.2\Include\dsp/statistics_functions.h
f4muban\transformfunctions.o: D:\keil5\ARM\PACK\ARM\CMSIS-DSP\1.14.2\Include\dsp/matrix_functions.h
f4muban\transformfunctions.o: D:/keil5/ARM/PACK/ARM/CMSIS-DSP/1.14.2/Source/TransformFunctions/arm_mfcc_init_q31.c
f4muban\transformfunctions.o: D:/keil5/ARM/PACK/ARM/CMSIS-DSP/1.14.2/Source/TransformFunctions/arm_mfcc_q31.c
f4muban\transformfunctions.o: D:/keil5/ARM/PACK/ARM/CMSIS-DSP/1.14.2/Source/TransformFunctions/arm_mfcc_init_q15.c
f4muban\transformfunctions.o: D:/keil5/ARM/PACK/ARM/CMSIS-DSP/1.14.2/Source/TransformFunctions/arm_mfcc_q15.c
f4muban\transformfunctions.o: D:/keil5/ARM/PACK/ARM/CMSIS-DSP/1.14.2/Source/TransformFunctions/arm_dct4_f32.c
f4muban\transformfunctions.o: D:/keil5/ARM/PACK/ARM/CMSIS-DSP/1.14.2/Source/TransformFunctions/arm_dct4_init_f32.c
f4muban\transformfunctions.o: D:/keil5/ARM/PACK/ARM/CMSIS-DSP/1.14.2/Source/TransformFunctions/arm_dct4_init_q15.c
f4muban\transformfunctions.o: D:/keil5/ARM/PACK/ARM/CMSIS-DSP/1.14.2/Source/TransformFunctions/arm_dct4_init_q31.c
f4muban\transformfunctions.o: D:/keil5/ARM/PACK/ARM/CMSIS-DSP/1.14.2/Source/TransformFunctions/arm_dct4_q15.c
f4muban\transformfunctions.o: D:/keil5/ARM/PACK/ARM/CMSIS-DSP/1.14.2/Source/TransformFunctions/arm_dct4_q31.c
f4muban\transformfunctions.o: D:/keil5/ARM/PACK/ARM/CMSIS-DSP/1.14.2/Source/TransformFunctions/arm_rfft_f32.c
f4muban\transformfunctions.o: D:/keil5/ARM/PACK/ARM/CMSIS-DSP/1.14.2/Source/TransformFunctions/arm_rfft_q15.c
f4muban\transformfunctions.o: D:/keil5/ARM/PACK/ARM/CMSIS-DSP/1.14.2/Source/TransformFunctions/arm_rfft_q31.c
f4muban\transformfunctions.o: D:/keil5/ARM/PACK/ARM/CMSIS-DSP/1.14.2/Source/TransformFunctions/arm_rfft_init_f32.c
f4muban\transformfunctions.o: D:/keil5/ARM/PACK/ARM/CMSIS-DSP/1.14.2/Source/TransformFunctions/arm_rfft_init_q15.c
f4muban\transformfunctions.o: D:/keil5/ARM/PACK/ARM/CMSIS-DSP/1.14.2/Source/TransformFunctions/arm_rfft_init_q31.c
f4muban\transformfunctions.o: D:/keil5/ARM/PACK/ARM/CMSIS-DSP/1.14.2/Source/TransformFunctions/arm_cfft_radix4_init_f32.c
f4muban\transformfunctions.o: D:/keil5/ARM/PACK/ARM/CMSIS-DSP/1.14.2/Source/TransformFunctions/arm_cfft_radix4_init_q15.c
f4muban\transformfunctions.o: D:/keil5/ARM/PACK/ARM/CMSIS-DSP/1.14.2/Source/TransformFunctions/arm_cfft_radix4_init_q31.c
f4muban\transformfunctions.o: D:/keil5/ARM/PACK/ARM/CMSIS-DSP/1.14.2/Source/TransformFunctions/arm_cfft_radix2_init_f32.c
f4muban\transformfunctions.o: D:/keil5/ARM/PACK/ARM/CMSIS-DSP/1.14.2/Source/TransformFunctions/arm_cfft_radix2_init_q15.c
f4muban\transformfunctions.o: D:/keil5/ARM/PACK/ARM/CMSIS-DSP/1.14.2/Source/TransformFunctions/arm_cfft_radix2_init_q31.c
